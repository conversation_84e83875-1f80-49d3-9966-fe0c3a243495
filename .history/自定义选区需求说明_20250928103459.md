# 自定义选区需求说明

## 需求说明

实现一个类似于微信/QQ 截图的自定义选区截图功能（仅限当前浏览器页面内），使用方式为 Vue3 Hook

## 功能说明

1. 调用 screenShotHook.startScreenShot() 后在屏幕上显示选框，选框样式如下图（需要添加遮罩层）
2. 自定义框选框内不显示遮罩层
3. 选框可以通过在左上角和右下角方块上点击并摁住鼠标左键并拖动改变大小
4. 可以动过在选框内部按下鼠标左键并拖动来移动选框位置
5. 选框内左下角区域用于选择截图后保存的文件格式
6. 选区完成后，点击保存按钮后，将选中的页面内容保存为对应的图片文件
7. 截取的图片可以自定义清晰度以保证足够的清晰度和尽可能小的文件大小

## 实现技术

1. snapdom 依赖库实现 html 页面转为 canvas
2. Typescript

## 实现思路

1. 点击某个按钮唤起屏幕截图，将当前屏幕显示内容通过 snapdom 依赖库转换为 canvas
2. 点击保存后，计算当前自定义选框的范围
3. 将步骤 1 中的 canvas 按自定义选框的范围裁剪
4. 使用裁剪的内容生成图片并保存

## 功能迭代说明

1. 第一版只需要实现上面的功能即可
2. 第二版需要实现类似于 微信/QQ 截图中编辑所截取图片的功能
   1. 包括画笔、框选、标记箭头、马赛克等
