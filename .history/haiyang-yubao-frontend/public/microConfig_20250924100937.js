/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> x<PERSON><PERSON><PERSON>@piesat.cn
 * @Date: 2024-10-11 11:07:22
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-04-23 14:31:22
 * @FilePath: \hainan-jianzai-web\public\microConfig.js
 * @Description:
 *
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved.
 */
// 开发环境地址
const config = {
  // base_vue_simple_admin: 'http://localhost:9010',
  assemble_sub_web: 'http://*************:14006/collect/', //数据汇集
  dispatch_sub_web: 'http://*************:14002/p/dispatch/web/', // 综合调度
  data_manage_sub_web: 'http://*************:14004', // 数据管理
  // auth_sub_web:'http://localhost:9050/p/auth/web/',
  auth_sub_web: 'http://*************:14001/p/auth/web/', // 用户管理
  data_push_web: 'http://*************:14005/push', //数据推送
  dictionary_sub_web: 'http://*************:14004/p/dictionary/web/', // 字典管理
  loginExpireCode: '9079', //登录失效code
  permissionSwitch: true, //权限开关
  userMultipleType: false, //用户多类型
  checklogin: false, //ssoclient 开关
  passportUrl: 'https://localhost:8081/api/auth',
  mapService: 'https://t0.tianditu.gov.cn',
  onlyOfficeCallBack: 'https://*************:38081/upload/onlyOfficeCallback/',
  onlyOfficeServerUrl: 'https://*************:80', //onlyoffice 服务器地址
  // kkFileUrl:"http://**********:38012/onlinePreview?url=", // kkFileView 文件预览地址
  kkFileUrl: 'https://*************:80/kkfileView/onlinePreview?url=', // kkFileView 文件预览地址
  hnMapService: 'https://localhost:8080/apione/',
  navMenuUrl: 'https://*************:8000/navigationMenu.html?access_token=',
  loginUrl: 'https://localhost:8081/login',
  qixiangDateUrl: 'https://**********:80/qixiang/Data/ZPEIYQ/sampleData/', //气象数据
  fileService: 'https://*************:80', // 文件服务地址
  // fileService:'http://*************', // 文件服务地址
  ettpService: 'https://*************:80/legend/', // ettp服务地址,
  mapParams: 'key=3bc7005ccd8a4190b1c8af7c90d705fc&userno=hytsjzt', //海南陆地地图参数
  // vecMapLayer:'https://*************/vec_c/wmts',
  vecMapLayer: 'https://*************:80/vec_c/wmts',
  cvaMapLayer: 'https://*************:80/cva_c/wmts',
  // cvaMapLayer:'https://*************/cva_c/wmts',
  landLayer: 'https://*************:8096' //海南陆地图层
}
