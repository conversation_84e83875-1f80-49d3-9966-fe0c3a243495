/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-02-23 18:12:06
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-03-27 13:19:25
 * @Description: vite配置
 */
import path from 'path'
import { resolve } from 'path'
import { defineConfig, loadEnv, UserConfig, searchForWorkspaceRoot } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import pxtorem from 'postcss-pxtorem'
import postcssPresetEnv from 'postcss-preset-env'
import checker from 'vite-plugin-checker'
import strip from '@rollup/plugin-strip'
import { viteMockServe } from 'vite-plugin-mock'
import viteCompression from 'vite-plugin-compression'
import visualizer from 'rollup-plugin-visualizer'
import { terser } from 'rollup-plugin-terser'
import basicSsl from '@vitejs/plugin-basic-ssl'

/**
 * @description: 获取Browsers配置
 * @param {*} command
 * @return {*}
 */
function getBrowsers(command: 'build' | 'serve') {
  return command === 'serve'
    ? [
        'last 1 chrome version',
        'last 1 firefox version',
        'last 1 safari version'
      ]
    : ['ie > 8', '>1%', 'not dead', 'not op_mini all']
}
// 显式类型转换
const terserPlugin = terser() as any
const stripPlugin = strip() as any
// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd())
  const config: UserConfig = {
    plugins: [
      vue({
        template: {
          compilerOptions: {
            isCustomElement: tag => /^micro-app/.test(tag)
          }
        }
      }),
      vueJsx(),
      // mock支持
      viteMockServe({
        mockPath: './mock',
        enable: command === 'serve',
        logger: false
      }),
      visualizer({ open: true }), // 生成打包报告并自动打开
      terserPlugin, // 压缩代码
      basicSsl()
    ],
    server: {
      headers: {
        'Access-Control-Allow-Origin': '*'
      },
      port: 8081,
      open: true,
      https: true,
      fs: {
        allow: [
          searchForWorkspaceRoot(process.cwd()),
          '/mygit/micro-zoe/micro-app/'
        ]
      },
      proxy: {
        '/zhongtaiApi': {
          target: 'http://**************:8089/',
          changeOrigin: true,
          secure: false,
          rewrite: path => path.replace('/zhongtaiApi', '')
        },
        '/zhongtaiShipApi': {
          target: 'http://*************:8066/',
          changeOrigin: true,
          secure: false,
          rewrite: path => path.replace('/zhongtaiShipApi', '')
        },
        [env.VITE_AUTH_BASE_URL]: {
          target: env.VITE_AUTH_BASE_API_URL,
          changeOrigin: true,
          secure: false,
          rewrite: path => path.replace(env.VITE_AUTH_BASE_URL, '')
        },
        [env.VITE_SYS_BASE_URL]: {
          target: env.VITE_SYS_BASE_API_URL,
          secure: false,
          changeOrigin: true,
          rewrite: path => path.replace(env.VITE_SYS_BASE_URL, '')
        },
        [env.VITE_SYS_ADMIN_URL]: {
          target: env.VITE_SYS_ADMIN_API_URL,
          secure: false,
          changeOrigin: true,
          rewrite: path => path.replace(env.VITE_SYS_ADMIN_URL, '')
        },
        [env.VITE_DATA_SCRAP_BASE_URL]: {
          target: env.VITE_DATA_SCRAP_BASE_API_URL,
          secure: false,
          changeOrigin: true,
          rewrite: path => path.replace(env.VITE_DATA_SCRAP_BASE_URL, '')
        },
        [env.VITE_PRODUCT_PUSH_BASE_URL]: {
          target: env.VITE_PRODUCT_PUSH_BASE_API_URL,
          secure: false,
          changeOrigin: true,
          rewrite: path => path.replace(env.VITE_PRODUCT_PUSH_BASE_URL, '')
        },
        [env.VITE_PERS_BASE_URL]: {
          target: env.VITE_PERS_BASE_API_URL,
          secure: false,
          changeOrigin: true,
          rewrite: path => path.replace(env.VITE_PERS_BASE_URL, '')
        },
        [env.VITE_DISPATCH_BASE_URL]: {
          target: env.VITE_DISPATCH_BASE_API_URL,
          secure: false,
          changeOrigin: true,
          rewrite: path => path.replace(env.VITE_DISPATCH_BASE_URL, '')
        },
        [env.VITE_FORECAST_BASE_URL]: {
          target: env.VITE_FORECAST_BASE_API_URL,
          secure: false,
          changeOrigin: true,
          rewrite: path => path.replace(env.VITE_FORECAST_BASE_URL, '')
        },
        [env.VITE_BASE_PRODUCT_URL]: {
          target: env.VITE_BASE_PRODUCT_API_URL,
          secure: false,
          changeOrigin: true,
          rewrite: path => path.replace(env.VITE_BASE_PRODUCT_URL, '')
        },
        [env.VITE_MONITOR_BASE_URL]: {
          target: env.VITE_MONITOR_BASE_API_URL,
          secure: false,
          changeOrigin: true,
          rewrite: path => path.replace(env.VITE_MONITOR_BASE_URL, '')
        },
        [env.VITE_BASE_EMAIL_URL]: {
          target: env.VITE_BASE_EMAIL_API_URL,
          secure: false,
          changeOrigin: true,
          rewrite: path => path.replace(env.VITE_BASE_EMAIL_URL, '')
        },
        [env.VITE_PRODUCT_BASE_URL]: {
          target: env.VITE_PRODUCT_BASE_API_URL,
          secure: false,
          changeOrigin: true,
          rewrite: path => path.replace(env.VITE_PRODUCT_BASE_URL, '')
        },
        [env.VITE_ETTP_BASE_API]: {
          target: env.VITE_ETTP_BASE_API_URL,
          secure: false,
          changeOrigin: true,
          rewrite: path => path.replace(env.VITE_ETTP_BASE_API, '')
        },
        [env.VITE_ETTP_SERVICE_BASE_API]: {
          target: env.VITE_ETTP_SERVICE_BASE_API_URL,
          secure: false,
          changeOrigin: true,
          rewrite: path => path.replace(env.VITE_ETTP_SERVICE_BASE_API, '')
        },
        [env.VITE_MICROBLOG_URL]: {
          target: env.VITE_MICROBLOG_API_RUL,
          secure: false,
          changeOrigin: true,
          rewrite: path => path.replace(env.VITE_MICROBLOG_URL, '')
        }
      }
    },
    resolve: {
      alias: {
        src: path.resolve(__dirname, './src')
      }
    },
    css: {
      postcss: {
        plugins: [
          pxtorem({
            rootValue: 192, //pc端建议：192，移动端建议：75；设计稿宽度的1 / 10
            unitPrecision: 5, // 转换后的精度，即小数点位数
            propList: ['*', '!border'], // 除 border 外所有px 转 rem // 需要转换的属性，这里选择全部都进行转换
            selectorBlackList: [],
            replace: true,
            mediaQuery: false, // 媒体查询( @media screen 之类的)中不生效
            minPixelValue: 1 //可以选择px小于1的不会被转换
            // exclude: (file: string) => {
            //   const regs = [/node_modules/i, /src\/views\/other/i]
            //   return regs.some(reg => reg.test(file))
            // }
          }),
          postcssPresetEnv({
            browsers: getBrowsers(command)
          }) as any
        ]
      },
      preprocessorOptions: {
        scss: {
          // @import 弃用
          additionalData: '@use "./src/style/vars.scss";' // scss必须要加分号
        }
      }
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks(id) {
            if (id.includes('node_modules')) {
              const arr1 = id.toString().split('node_modules/')
              const arr2 = arr1[arr1.length - 1].split('/')
              switch (arr2[0]) {
                case '@vue':
                case 'vue-router':
                case 'naive-ui':
                case 'echarts':
                case 'pinia':
                case 'dayjs':
                  return 'vendor_' + arr2[0]
                case 'lodash': // 单独打包 lodash
                  return 'vendor_lodash'
                case 'moment':
                  return 'vendor_moment'
                case 'ol':
                  return 'vendor_ol'
                case 'html2canvas':
                  return 'vendor_html2canvas'
                case '@micro-zoe':
                  return 'vendor_@micro-zoe'
                default:
                  return 'vendor'
              }
            }
          },
          chunkFileNames: 'assets/[name]-[hash].js',
          entryFileNames: 'assets/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]'
        }
      },
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true
        }
      },
      sourcemap: true
    }
  }
  if (config.plugins?.length) {
    if (command === 'serve') {
      config.plugins?.push(
        checker({
          vueTsc: true
        })
      )
    } else {
      config.plugins?.push(
        stripPlugin,
        viteCompression({
          filter: /\.(js|mjs|json|css|html|ttf)$/i
        })
      )
    }
  }
  return config
})
