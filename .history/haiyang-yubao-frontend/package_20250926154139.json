{"name": "vue3-ts-starter", "private": true, "version": "0.0.1", "description": "海南海洋灾害一体化", "scripts": {"start": "pnpm run dev", "lint:code": "eslint \"src/**/*.{ts,tsx,js,jsx,vue}\"", "lint:fix": "eslint \"src/**/*.{ts,tsx,js,jsx,vue}\" --fix", "dev": "vite --mode test --host 0.0.0.0", "build": "vite build", "build-zip": "rimraf dist.zip && vite build && node scripts/zip.js", "preview": "vite preview", "unzip": "node scripts/unzip.js", "test": "vite --mode test --host 0.0.0.0"}, "dependencies": {"@hufe921/canvas-editor": "^0.9.90", "@micro-zoe/micro-app": "^1.0.0-rc.8", "@onlyoffice/document-editor-vue": "^1.4.0", "@turf/turf": "^7.1.0", "@types/crypto-js": "^4.2.2", "@types/geojson": "^7946.0.16", "@types/lodash": "^4.17.12", "@umoteam/editor": "^4.2.0", "@vicons/ionicons5": "^0.12.0", "@vueup/vue-quill": "^1.0.0-alpha.40", "@vueuse/core": "^13.4.0", "amfe-flexible": "^2.2.1", "axios": "^1.1.3", "class-transformer": "^0.5.1", "color": "^5.0.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.6", "echarts": "^5.5.1", "geotiff": "^2.1.3", "html2canvas": "^1.4.1", "lib-flexible-computer": "^1.0.2", "lodash": "^4.17.21", "mitt": "^3.0.1", "moment": "^2.30.1", "naive-ui": "^2.34.4", "ol": "^7.5.2", "ol-ext": "^4.0.27", "onlyoffice-vue": "^1.0.1", "pinia": "^2.1.4", "pnpm": "^10.5.2", "proj4": "^2.15.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-visualizer": "^5.12.0", "shapefile": "^0.6.6", "vue": "^3.3.4", "vue-clipboard3": "^2.0.0", "vue-router": "^4.2.2", "vue3-cookies": "^1.0.6", "vue3-sketch-ruler": "^2.0.10"}, "devDependencies": {"@rollup/plugin-strip": "^3.0.2", "@types/mockjs": "^1.0.10", "@types/moment": "^2.13.0", "@types/node": "^18.11.9", "@types/postcss-preset-env": "^7.7.0", "@types/postcss-pxtorem": "^6.0.0", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "@vicons/antd": "^0.12.0", "@vicons/utils": "^0.1.4", "@vitejs/plugin-basic-ssl": "^2.0.0", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "adm-zip": "^0.5.10", "eslint": "^8.43.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.15.0", "lint-staged": "^13.2.2", "mockjs": "^1.1.0", "postcss": "^8.4.24", "postcss-preset-env": "^8.5.0", "postcss-pxtorem": "^6.1.0", "prettier": "^2.8.8", "rimraf": "^5.0.1", "sass": "^1.63.4", "simple-git-hooks": "^2.8.1", "terser": "^5.39.0", "ts-node": "^10.9.1", "typescript": "5.0.4", "vite": "^4.3.9", "vite-plugin-checker": "^0.6.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-mock": "^3.0.1", "vue-eslint-parser": "^9.3.1", "vue-tsc": "^1.8.0"}, "simple-git-hooks": {"pre-commit": "pnpm exec lint-staged --concurrent false"}, "lint-staged": {"*": ["prettier --write --cache --ignore-unknown"], "*.{js,jsx,ts,tsx,vue}": ["eslint --fix"]}, "volta": {"node": "21.7.3", "pnpm": "10.11.0"}}