<template>
  <div
    v-if="isActive"
    class="screenshot-overlay-v3"
    :style="{ zIndex: level || 9999 }"
    @mousedown="handleMouseDown"
    @contextmenu="handleRightClick"
  >
    <!-- 背景Canvas显示截图 -->
    <canvas
      ref="backgroundCanvasRef"
      class="background-canvas"
      :style="backgroundCanvasStyle"
    ></canvas>

    <!-- 蒙层 -->
    <div class="mask-layer" :style="maskStyle">
      <!-- 选区显示 -->
      <div
        v-if="selection.width > 0 && selection.height > 0"
        class="selection-area"
        :style="selectionStyle"
      >
        <!-- 选区边框 -->
        <div class="selection-border" :style="borderStyle"></div>

        <!-- 调整手柄 -->
        <div
          v-if="editState.currentTool === 'select' && !editState.isSelecting"
          class="resize-handles"
        >
          <div
            v-for="handle in resizeHandles"
            :key="handle.position"
            :class="['resize-handle', `handle-${handle.position}`]"
            :style="handle.style"
            @mousedown.stop="handleResizeStart(handle.position, $event)"
          ></div>
        </div>

        <!-- 选区信息显示 -->
        <div class="selection-info" :style="infoStyle">
          {{ Math.round(selection.width) }} × {{ Math.round(selection.height) }}
        </div>
      </div>
    </div>

    <!-- 编辑Canvas -->
    <canvas
      ref="editCanvasRef"
      class="edit-canvas"
      :style="editCanvasStyle"
      @mousedown="handleCanvasMouseDown"
      @mousemove="handleCanvasMouseMove"
      @mouseup="handleCanvasMouseUp"
    ></canvas>

    <!-- 工具栏 - 只在有选区时显示 -->
    <div
      v-if="
        selection.width > 0 && selection.height > 0 && !editState.isSelecting
      "
      class="toolbar"
      :style="toolbarStyle"
    >
      <!-- 编辑工具 -->
      <div class="edit-tools">
        <button
          v-for="tool in availableTools"
          :key="tool.name"
          :class="['tool-btn', { active: editState.currentTool === tool.name }]"
          :title="tool.title"
          @click="setCurrentTool(tool.name as EditTool)"
        >
          <img :src="tool.icon" :alt="tool.title" />
        </button>
      </div>

      <!-- 分割线 -->
      <div v-if="!hiddenToolIco?.separateLine" class="separator"></div>

      <!-- 撤销重做 -->
      <div class="undo-redo">
        <button
          v-if="!hiddenToolIco?.undo"
          class="tool-btn"
          :disabled="!canUndo"
          title="撤销 (Ctrl+Z)"
          @click="undo"
        >
          <img src="/icons/undo.svg" alt="撤销" />
        </button>
        <button
          class="tool-btn"
          :disabled="!canRedo"
          title="重做 (Ctrl+Shift+Z)"
          @click="redo"
        >
          <img src="/icons/redo.svg" alt="重做" />
        </button>
      </div>

      <!-- 分割线 -->
      <div class="separator"></div>

      <!-- 保存和取消 -->
      <div class="action-buttons">
        <button
          v-if="!hiddenToolIco?.save"
          class="tool-btn save-btn"
          title="下载图片"
          @click="saveScreenShot"
        >
          <img src="/icons/download.svg" alt="下载" />
        </button>
        <button
          v-if="!hiddenToolIco?.confirm"
          class="tool-btn confirm-btn"
          title="确认 (Enter)"
          @click="saveScreenShot"
        >
          <img src="/icons/check.svg" alt="确认" />
        </button>
        <button
          class="tool-btn cancel-btn"
          title="取消 (Esc)"
          @click="cancelScreenShot"
        >
          <img src="/icons/close.svg" alt="取消" />
        </button>
      </div>
    </div>

    <!-- 工具配置面板 -->
    <div
      v-if="showToolConfig"
      class="tool-config-panel"
      :style="configPanelStyle"
    >
      <!-- 画笔配置 -->
      <div v-if="editState.currentTool === 'brush'" class="brush-config">
        <label>粗细:</label>
        <input
          v-model="brushSize"
          type="range"
          min="1"
          max="20"
          class="size-slider"
        />
        <label>颜色:</label>
        <input v-model="brushColor" type="color" class="color-picker" />
      </div>

      <!-- 文本配置 -->
      <div v-if="editState.currentTool === 'text'" class="text-config">
        <label>字体大小:</label>
        <input
          v-model="fontSize"
          type="range"
          min="12"
          max="48"
          class="size-slider"
        />
        <label>颜色:</label>
        <input v-model="textColor" type="color" class="color-picker" />
      </div>

      <!-- 马赛克配置 -->
      <div v-if="editState.currentTool === 'mosaicPen'" class="mosaic-config">
        <label>强度:</label>
        <input
          v-model="mosaicSize"
          type="range"
          min="5"
          max="30"
          class="size-slider"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import type {
  SelectionArea,
  DragState,
  EditState,
  EditTool,
  HiddenToolIco,
  MaskColor
} from './types'

/**
 * 组件属性定义
 */
interface Props {
  /** 是否激活截图模式 */
  isActive: boolean
  /** 是否正在截图 */
  isCapturing: boolean
  /** 选区信息 */
  selection: SelectionArea
  /** 拖拽状态 */
  dragState: DragState
  /** 编辑状态 */
  editState: EditState
  /** 是否可以撤销 */
  canUndo: boolean
  /** 是否可以重做 */
  canRedo: boolean
  /** 截图容器层级 */
  level?: number
  /** 裁剪区域边框颜色 */
  cutBoxBdColor?: string
  /** 需要隐藏的工具图标 */
  hiddenToolIco?: HiddenToolIco
  /** 蒙层颜色 */
  maskColor?: MaskColor
  /** 工具栏位置 */
  toolPosition?: 'left' | 'center' | 'right'
  /** 画布宽度 */
  canvasWidth?: number
  /** 画布高度 */
  canvasHeight?: number
}

/**
 * 组件事件定义
 */
interface Emits {
  (e: 'mousedown', event: MouseEvent): void
  (e: 'cancel'): void
  (e: 'save'): void
  (e: 'setTool', tool: EditTool): void
  (e: 'undo'): void
  (e: 'redo'): void
}

// 定义属性和事件
const props = withDefaults(defineProps<Props>(), {
  level: 9999,
  cutBoxBdColor: '#007acc',
  hiddenToolIco: () => ({}),
  maskColor: () => ({ r: 0, g: 0, b: 0, a: 0.6 }),
  toolPosition: 'center',
  canvasWidth: 0,
  canvasHeight: 0
})

const emit = defineEmits<Emits>()

// Canvas引用
const backgroundCanvasRef = ref<HTMLCanvasElement>()
const editCanvasRef = ref<HTMLCanvasElement>()

// 工具配置
const showToolConfig = ref(false)
const brushSize = ref(3)
const brushColor = ref('#ff0000')
const fontSize = ref(16)
const textColor = ref('#000000')
const mosaicSize = ref(10)

// 可用工具列表
const availableTools = computed(() => {
  const tools = [
    { name: 'select', title: '选择', icon: '/icons/select.svg' },
    { name: 'square', title: '矩形', icon: '/icons/square.svg' },
    { name: 'round', title: '圆形', icon: '/icons/circle.svg' },
    { name: 'rightTop', title: '箭头', icon: '/icons/arrow.svg' },
    { name: 'brush', title: '画笔', icon: '/icons/brush.svg' },
    { name: 'mosaicPen', title: '马赛克', icon: '/icons/mosaic.svg' },
    { name: 'text', title: '文本', icon: '/icons/text.svg' }
  ]

  return tools.filter(
    tool => !props.hiddenToolIco?.[tool.name as keyof HiddenToolIco]
  )
})

// 调整手柄
const resizeHandles = computed(() => [
  { position: 'nw', style: { top: '-4px', left: '-4px' } },
  {
    position: 'n',
    style: { top: '-4px', left: '50%', transform: 'translateX(-50%)' }
  },
  { position: 'ne', style: { top: '-4px', right: '-4px' } },
  {
    position: 'e',
    style: { top: '50%', right: '-4px', transform: 'translateY(-50%)' }
  },
  { position: 'se', style: { bottom: '-4px', right: '-4px' } },
  {
    position: 's',
    style: { bottom: '-4px', left: '50%', transform: 'translateX(-50%)' }
  },
  { position: 'sw', style: { bottom: '-4px', left: '-4px' } },
  {
    position: 'w',
    style: { top: '50%', left: '-4px', transform: 'translateY(-50%)' }
  }
])

// 样式计算
const maskStyle = computed(() => ({
  backgroundColor: `rgba(${props.maskColor.r}, ${props.maskColor.g}, ${props.maskColor.b}, ${props.maskColor.a})`
}))

const selectionStyle = computed(() => ({
  left: `${props.selection.x}px`,
  top: `${props.selection.y}px`,
  width: `${props.selection.width}px`,
  height: `${props.selection.height}px`
}))

const borderStyle = computed(() => ({
  borderColor: props.cutBoxBdColor
}))

const infoStyle = computed(() => ({
  top: props.selection.y > 30 ? '-25px' : `${props.selection.height + 5}px`
}))

const backgroundCanvasStyle = computed(() => ({
  position: 'absolute',
  top: '0',
  left: '0',
  width: '100vw',
  height: '100vh',
  pointerEvents: 'none'
}))

const editCanvasStyle = computed(() => ({
  position: 'absolute',
  top: '0',
  left: '0',
  width: '100vw',
  height: '100vh',
  pointerEvents: 'auto'
}))

const toolbarStyle = computed(() => {
  if (props.selection.width === 0 || props.selection.height === 0) {
    return { display: 'none' }
  }

  const toolbarHeight = 50 // 工具栏高度
  const margin = 10 // 与选区的间距
  const windowHeight = window.innerHeight

  // 计算工具栏位置
  const selectionBottom = props.selection.y + props.selection.height
  const spaceBelow = windowHeight - selectionBottom
  const spaceAbove = props.selection.y

  // 优先显示在下方，空间不足时显示在上方
  const showBelow = spaceBelow >= toolbarHeight + margin

  const baseStyle = {
    position: 'absolute' as const,
    left: `${props.selection.x + props.selection.width / 2}px`,
    transform: 'translateX(-50%)',
    zIndex: 10000
  }

  if (showBelow) {
    return {
      ...baseStyle,
      top: `${selectionBottom + margin}px`
    }
  } else {
    return {
      ...baseStyle,
      bottom: `${windowHeight - props.selection.y + margin}px`
    }
  }
})

const configPanelStyle = computed(() => ({
  position: 'absolute' as const,
  top: `${props.selection.y + props.selection.height + 60}px`,
  left: `${props.selection.x}px`
}))

/**
 * 处理鼠标按下事件
 */
const handleMouseDown = (event: MouseEvent): void => {
  emit('mousedown', event)
}

/**
 * 处理选区鼠标按下事件
 */
const handleSelectionMouseDown = (event: MouseEvent): void => {
  // 阻止事件冒泡，避免触发选区的鼠标事件
  event.stopPropagation()
  emit('mousedown', event)
}

/**
 * 处理右键点击事件
 */
const handleRightClick = (event: MouseEvent): void => {
  event.preventDefault()
  // 可以在这里添加自定义右键菜单
}

/**
 * 处理调整手柄鼠标按下
 */
const handleResizeStart = (position: string, event: MouseEvent): void => {
  event.stopPropagation()
  // 这里可以添加调整大小的逻辑
}

/**
 * 处理Canvas鼠标事件
 */
const handleCanvasMouseDown = (event: MouseEvent): void => {
  if (props.editState.currentTool !== 'select') {
    event.stopPropagation()
    // 开始绘制
  }
}

const handleCanvasMouseMove = (event: MouseEvent): void => {
  if (props.editState.isDrawing) {
    // 继续绘制
  }
}

const handleCanvasMouseUp = (event: MouseEvent): void => {
  if (props.editState.isDrawing) {
    // 结束绘制
  }
}

/**
 * 取消截图
 */
const cancelScreenShot = (): void => {
  emit('cancel')
}

/**
 * 保存截图
 */
const saveScreenShot = (): void => {
  emit('save')
}

/**
 * 设置当前工具
 */
const setCurrentTool = (tool: EditTool): void => {
  emit('setTool', tool)
  showToolConfig.value = ['brush', 'text', 'mosaicPen'].includes(tool)
}

/**
 * 撤销操作
 */
const undo = (): void => {
  emit('undo')
}

/**
 * 重做操作
 */
const redo = (): void => {
  emit('redo')
}

// 监听工具变化
watch(
  () => props.editState.currentTool,
  newTool => {
    showToolConfig.value = ['brush', 'text', 'mosaicPen'].includes(newTool)
  }
)

// 组件挂载时绘制背景截图
onMounted(() => {
  drawBackgroundCanvas()
})

// 监听截图Canvas变化
watch(
  () => props.isActive,
  () => {
    if (props.isActive) {
      nextTick(() => {
        drawBackgroundCanvas()
      })
    }
  }
)

/**
 * 绘制背景Canvas
 */
const drawBackgroundCanvas = (): void => {
  if (!backgroundCanvasRef.value) return

  const canvas = backgroundCanvasRef.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 设置Canvas尺寸
  canvas.width = window.innerWidth
  canvas.height = window.innerHeight

  // 清空Canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height)
}
</script>

<style scoped>
.screenshot-overlay-v3 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  cursor: crosshair;
  user-select: none;
}

.mask-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.selection-area {
  position: absolute;
  pointer-events: auto;
  cursor: move;
}

.selection-border {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid;
  box-sizing: border-box;
}

.resize-handles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #007acc;
  border: 1px solid #fff;
  border-radius: 50%;
  cursor: pointer;
}

.resize-handle:hover {
  background: #005a9e;
}

.selection-info {
  position: absolute;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  white-space: nowrap;
}

.edit-canvas {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: auto;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 6px;
  padding: 8px;
  backdrop-filter: blur(10px);
}

.edit-tools,
.undo-redo,
.action-buttons {
  display: flex;
  gap: 4px;
}

.tool-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.tool-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.tool-btn.active {
  background: rgba(0, 122, 204, 0.8);
}

.tool-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tool-btn img {
  width: 16px;
  height: 16px;
  filter: invert(1);
}

.separator {
  width: 1px;
  height: 24px;
  background: rgba(255, 255, 255, 0.3);
}

.save-btn {
  background: rgba(0, 150, 0, 0.8);
}

.confirm-btn {
  background: rgba(0, 122, 204, 0.8);
}

.cancel-btn {
  background: rgba(220, 53, 69, 0.8);
}

.tool-config-panel {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 6px;
  padding: 12px;
  backdrop-filter: blur(10px);
  color: white;
  min-width: 200px;
}

.tool-config-panel label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
}

.size-slider,
.color-picker {
  width: 100%;
  margin-bottom: 8px;
}

.brush-config,
.text-config,
.mosaic-config {
  display: flex;
  flex-direction: column;
}
</style>
