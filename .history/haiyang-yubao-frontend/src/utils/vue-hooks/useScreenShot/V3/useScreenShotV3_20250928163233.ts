/**
 * 截图功能V3版本Vue3 Hook
 * 基于js-web-screen-shot的完整功能实现
 * 使用@zumer/snapdom作为HTML转图片引擎
 */

import {
  ref,
  reactive,
  computed,
  onMounted,
  onUnmounted,
  readonly,
  type Ref
} from 'vue'
import { ScreenShotV3Utils } from './utils'
import type {
  ImageFormat,
  SelectionArea,
  DragState,
  EditState,
  EditTool,
  ScreenShotV3Options,
  ScreenShotV3Result,
  ScreenShotV3Instance,
  CallbackResult,
  TriggerCallbackResult,
  CancelCallbackResult,
  SaveCallbackResult,
  EditOperation,
  MaskColor,
  CropBoxInfo
} from './types'

/**
 * 截图功能V3版本Hook
 * @param options 截图配置选项
 * @returns 截图相关的状态和方法
 */
export function useScreenShotV3(options: ScreenShotV3Options = {}): {
  // 状态
  isActive: Readonly<Ref<boolean>>
  isCapturing: Readonly<Ref<boolean>>
  selection: SelectionArea
  dragState: DragState
  editState: EditState
  selectedFormat: Readonly<Ref<ImageFormat>>
  canUndo: Readonly<Ref<boolean>>
  canRedo: Readonly<Ref<boolean>>

  // 方法
  startScreenShot: () => Promise<void>
  cancelScreenShot: () => void
  saveScreenShot: () => Promise<ScreenShotV3Result>
  setImageFormat: (format: ImageFormat) => void
  handleMouseDown: (event: MouseEvent) => void
  handleMouseMove: (event: MouseEvent) => void
  handleMouseUp: () => void
  setCurrentTool: (tool: EditTool) => void
  undo: () => void
  redo: () => void

  // 实例方法
  getScreenshotCanvas: () => HTMLCanvasElement | null
  getCanvasController: () => HTMLCanvasElement | null
  destroyComponents: () => void
  completeScreenshot: () => void
  getCutBoxInfo: () => {
    startX: number
    startY: number
    width: number
    height: number
  } | null
} {
  // 默认配置
  const defaultOptions: Required<ScreenShotV3Options> = {
    // V1基础配置
    quality: 0.9,
    defaultFormat: 'png',
    allowMove: true,
    allowResize: true,
    minSize: { width: 50, height: 50 },

    // V3新增配置
    enableWebRtc: false,
    screenFlow: undefined as any,
    completeCallback: undefined as any,
    closeCallback: undefined as any,
    triggerCallback: undefined as any,
    cancelCallback: undefined as any,
    saveCallback: undefined as any,
    level: 9999,
    cutBoxBdColor: '#007acc',
    maxUndoNum: 50,
    canvasWidth: 0,
    canvasHeight: 0,
    position: {},
    clickCutFullScreen: false,
    hiddenToolIco: {},
    showScreenData: false,
    customRightClickEvent: { state: false },
    imgSrc: '',
    loadCrossImg: false,
    proxyUrl: '',
    screenShotDom: document.body,
    useRatioArrow: false,
    imgAutoFit: false,
    cropBoxInfo: undefined as any,
    wrcReplyTime: 500,
    wrcImgPosition: undefined as any,
    noScroll: true,
    maskColor: { r: 0, g: 0, b: 0, a: 0.6 },
    toolPosition: 'center',
    writeBase64: true,
    wrcWindowMode: false,
    hiddenScrollBar: { state: false },
    menuBarHeight: 0
  }

  const config = { ...defaultOptions, ...options }

  // 响应式状态
  const isActive = ref(false)
  const isCapturing = ref(false)
  const selectedFormat = ref<ImageFormat>(config.defaultFormat)
  const screenshotCanvas = ref<HTMLCanvasElement | null>(null)

  // 选区状态
  const selection = reactive<SelectionArea>({
    x: 0,
    y: 0,
    width: 300,
    height: 200
  })

  // 拖拽状态
  const dragState = reactive<DragState>({
    isDragging: false,
    dragType: 'move',
    startMousePos: { x: 0, y: 0 },
    startSelection: { x: 0, y: 0, width: 0, height: 0 }
  })

  // 编辑状态
  const editState = reactive<EditState>({
    currentTool: 'select',
    history: [],
    currentIndex: -1,
    isDrawing: false,
    isSelecting: true,
    startPoint: null,
    tempCanvas: null
  })

  // 计算属性
  const canUndo = computed(() => editState.currentIndex >= 0)
  const canRedo = computed(
    () => editState.currentIndex < editState.history.length - 1
  )

  /**
   * 开始截图
   */
  const startScreenShot = async (): Promise<void> => {
    try {
      isCapturing.value = true

      let canvas: HTMLCanvasElement

      if (config.enableWebRtc && config.screenFlow) {
        // 使用WebRTC流
        canvas = await ScreenShotV3Utils.captureFromStream(
          config.screenFlow,
          config
        )

        // 触发响应回调
        if (config.triggerCallback) {
          const result: TriggerCallbackResult = {
            code: 0,
            msg: '截图加载完成',
            displaySurface: 'monitor',
            displayLabel: 'Screen'
          }
          config.triggerCallback(result)
        }
      } else if (config.enableWebRtc && !config.screenFlow) {
        // 请求屏幕共享
        try {
          const stream = await navigator.mediaDevices.getDisplayMedia({
            video: {
              mediaSource: config.wrcWindowMode ? 'window' : 'screen'
            } as any
          })

          canvas = await ScreenShotV3Utils.captureFromStream(stream, config)

          // 触发响应回调
          if (config.triggerCallback) {
            const result: TriggerCallbackResult = {
              code: 0,
              msg: '截图加载完成',
              displaySurface: config.wrcWindowMode ? 'window' : 'monitor',
              displayLabel: 'Screen'
            }
            config.triggerCallback(result)
          }
        } catch (error) {
          // 用户取消或不支持
          if (config.cancelCallback) {
            const result: CancelCallbackResult = {
              code: -1,
              msg: '用户取消或浏览器不支持',
              errorInfo: error instanceof Error ? error.message : '未知错误'
            }
            config.cancelCallback(result)
          }
          throw error
        }
      } else if (config.imgSrc) {
        // 使用自定义图片
        canvas = await loadImageToCanvas(config.imgSrc)
      } else {
        // 使用snapdom截图
        const targetElement = config.screenShotDom || document.body
        canvas = await ScreenShotV3Utils.captureScreen(targetElement, config)
      }

      screenshotCanvas.value = canvas

      // 重置选区状态，等待用户选择
      selection.x = 0
      selection.y = 0
      selection.width = 0
      selection.height = 0

      // 重置编辑状态
      editState.isSelecting = true
      editState.isDrawing = false
      editState.currentTool = 'select'
      editState.history = []
      editState.currentIndex = -1

      isActive.value = true
      isCapturing.value = false

      if (config.triggerCallback) {
        const result: TriggerCallbackResult = {
          code: 0,
          msg: '截图模式已启动，请拖拽选择截图区域',
          displaySurface: 'monitor',
          displayLabel: 'Ready'
        }
        config.triggerCallback(result)
      }
    } catch (error) {
      console.error('截图启动失败:', error)
      isCapturing.value = false
      throw error
    }
  }

  /**
   * 取消截图
   */
  const cancelScreenShot = (): void => {
    isActive.value = false
    isCapturing.value = false
    screenshotCanvas.value = null
    editState.history = []
    editState.currentIndex = -1

    if (config.closeCallback) {
      config.closeCallback()
    }
  }

  /**
   * 保存截图
   */
  const saveScreenShot = async (): Promise<ScreenShotV3Result> => {
    if (!screenshotCanvas.value) {
      throw new Error('没有可保存的截图')
    }

    try {
      // 裁剪选区
      const croppedCanvas = ScreenShotV3Utils.cropSelection(
        screenshotCanvas.value,
        selection
      )

      // 转换为数据URL
      const dataUrl = ScreenShotV3Utils.canvasToDataURL(
        croppedCanvas,
        selectedFormat.value,
        config.quality
      )

      // 生成文件名并下载
      const filename = ScreenShotV3Utils.generateFilename(selectedFormat.value)
      ScreenShotV3Utils.downloadImage(dataUrl, filename)

      // 写入剪切板
      if (config.writeBase64) {
        const success = await ScreenShotV3Utils.writeToClipboard(croppedCanvas)
        if (config.saveCallback) {
          const result: SaveCallbackResult = {
            code: success ? 0 : -1,
            msg: success ? '保存成功' : '剪切板写入失败'
          }
          config.saveCallback(result)
        }
      }

      // 构建结果
      const result: ScreenShotV3Result = {
        dataUrl,
        format: selectedFormat.value,
        selection: { ...selection },
        editHistory: [...editState.history],
        hasEdits: editState.history.length > 0,
        cutInfo: {
          startX: selection.x,
          startY: selection.y,
          width: selection.width,
          height: selection.height
        }
      }

      // 调用完成回调
      if (config.completeCallback) {
        const callbackResult: CallbackResult = {
          base64: dataUrl,
          cutInfo: {
            startX: selection.x,
            startY: selection.y,
            width: selection.width,
            height: selection.height
          }
        }
        config.completeCallback(callbackResult)
      } else {
        // 存储到sessionStorage
        sessionStorage.setItem('screenShotImg', dataUrl)
      }

      // 关闭截图
      cancelScreenShot()

      return result
    } catch (error) {
      console.error('保存截图失败:', error)
      throw new Error('保存截图失败，请重试')
    }
  }

  /**
   * 设置图片格式
   */
  const setImageFormat = (format: ImageFormat): void => {
    selectedFormat.value = format
  }

  /**
   * 处理鼠标按下事件
   */
  const handleMouseDown = (event: MouseEvent): void => {
    if (!isActive.value) return

    const x = event.clientX
    const y = event.clientY

    if (
      editState.isSelecting &&
      selection.width === 0 &&
      selection.height === 0
    ) {
      // 开始新的选区
      selection.x = x
      selection.y = y
      selection.width = 0
      selection.height = 0
      dragState.isDragging = true
      dragState.dragType = 'resize'
      dragState.startMousePos = { x, y }
      dragState.startSelection = { ...selection }
    } else if (editState.currentTool === 'select') {
      // 检查是否点击在选区内
      if (
        x >= selection.x &&
        x <= selection.x + selection.width &&
        y >= selection.y &&
        y <= selection.y + selection.height
      ) {
        // 开始拖拽移动
        startDrag('move', { x, y })
      } else {
        // 重新开始选区
        selection.x = x
        selection.y = y
        selection.width = 0
        selection.height = 0
        dragState.isDragging = true
        dragState.dragType = 'resize'
        dragState.startMousePos = { x, y }
        dragState.startSelection = { ...selection }
        editState.isSelecting = true
      }
    } else {
      // 开始绘制
      editState.isDrawing = true
    }

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }

  /**
   * 处理鼠标移动事件
   */
  const handleMouseMove = (event: MouseEvent): void => {
    if (!dragState.isDragging) return

    const x = event.clientX
    const y = event.clientY

    if (dragState.dragType === 'resize') {
      // 调整选区大小
      const startX = dragState.startMousePos.x
      const startY = dragState.startMousePos.y

      selection.x = Math.min(startX, x)
      selection.y = Math.min(startY, y)
      selection.width = Math.abs(x - startX)
      selection.height = Math.abs(y - startY)
    } else if (dragState.dragType === 'move') {
      // 移动选区
      const deltaX = x - dragState.startMousePos.x
      const deltaY = y - dragState.startMousePos.y

      selection.x = dragState.startSelection.x + deltaX
      selection.y = dragState.startSelection.y + deltaY
    }
  }

  /**
   * 处理鼠标释放事件
   */
  const handleMouseUp = (): void => {
    if (dragState.isDragging) {
      dragState.isDragging = false

      // 如果选区太小，重置
      if (selection.width < 10 || selection.height < 10) {
        selection.width = 0
        selection.height = 0
        editState.isSelecting = true
      } else {
        editState.isSelecting = false
      }
    }

    // 移除全局事件监听
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  /**
   * 开始拖拽
   */
  const startDrag = (
    type: 'move' | 'resize',
    startPos: { x: number; y: number }
  ): void => {
    dragState.isDragging = true
    dragState.dragType = type
    dragState.startMousePos = startPos
    dragState.startSelection = { ...selection }
  }

  /**
   * 设置当前工具
   */
  const setCurrentTool = (tool: EditTool): void => {
    editState.currentTool = tool
  }

  /**
   * 撤销操作
   */
  const undo = (): void => {
    if (canUndo.value) {
      editState.currentIndex--
    }
  }

  /**
   * 重做操作
   */
  const redo = (): void => {
    if (canRedo.value) {
      editState.currentIndex++
    }
  }

  /**
   * 获取Canvas控制器
   */
  const getCanvasController = (): HTMLCanvasElement | null => {
    return screenshotCanvas.value
  }

  /**
   * 销毁组件
   */
  const destroyComponents = (): void => {
    cancelScreenShot()
  }

  /**
   * 完成截图
   */
  const completeScreenshot = (): void => {
    if (isActive.value) {
      saveScreenShot().catch(console.error)
    }
  }

  /**
   * 获取裁剪框信息
   */
  const getCutBoxInfo = () => {
    if (!isActive.value) return null

    return {
      startX: selection.x,
      startY: selection.y,
      width: selection.width,
      height: selection.height
    }
  }

  /**
   * 加载图片到Canvas
   */
  const loadImageToCanvas = (src: string): Promise<HTMLCanvasElement> => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.crossOrigin = config.loadCrossImg ? 'anonymous' : null

      img.onload = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')!

        canvas.width = img.width
        canvas.height = img.height
        ctx.drawImage(img, 0, 0)

        resolve(canvas)
      }

      img.onerror = () => {
        reject(new Error('图片加载失败'))
      }

      img.src = src
    })
  }

  // 键盘事件处理
  const handleKeyDown = (event: KeyboardEvent): void => {
    if (!isActive.value) return

    switch (event.key) {
      case 'Escape':
        cancelScreenShot()
        break
      case 'Enter':
        saveScreenShot().catch(console.error)
        break
      case 'z':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault()
          if (event.shiftKey) {
            redo()
          } else {
            undo()
          }
        }
        break
    }
  }

  // 生命周期
  onMounted(() => {
    document.addEventListener('keydown', handleKeyDown)
  })

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown)
    destroyComponents()
  })

  return {
    // 状态
    isActive: readonly(isActive),
    isCapturing: readonly(isCapturing),
    selection,
    dragState,
    editState,
    selectedFormat: readonly(selectedFormat),
    canUndo,
    canRedo,

    // 方法
    startScreenShot,
    cancelScreenShot,
    saveScreenShot,
    setImageFormat,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    setCurrentTool,
    undo,
    redo,

    // 实例方法
    getScreenshotCanvas: () => screenshotCanvas.value,
    getCanvasController,
    destroyComponents,
    completeScreenshot,
    getCutBoxInfo
  }
}
