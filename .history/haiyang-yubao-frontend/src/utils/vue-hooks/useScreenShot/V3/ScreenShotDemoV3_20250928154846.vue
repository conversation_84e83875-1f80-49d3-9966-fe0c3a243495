<template>
  <div class="screenshot-demo-v3">
    <div class="demo-container">
      <h2>🚀 截图功能V3版本演示</h2>
      <p class="demo-description">
        基于js-web-screen-shot的完整功能实现，使用@zumer/snapdom作为HTML转图片引擎
      </p>
      
      <!-- 功能配置 -->
      <div class="config-section">
        <h3>功能配置</h3>
        <div class="config-grid">
          <div class="config-item">
            <label>
              <input
                v-model="config.enableWebRtc"
                type="checkbox"
              />
              启用WebRTC模式
            </label>
          </div>
          <div class="config-item">
            <label>
              <input
                v-model="config.clickCutFullScreen"
                type="checkbox"
              />
              单击截全屏
            </label>
          </div>
          <div class="config-item">
            <label>
              <input
                v-model="config.writeBase64"
                type="checkbox"
              />
              写入剪切板
            </label>
          </div>
          <div class="config-item">
            <label>
              <input
                v-model="config.wrcWindowMode"
                type="checkbox"
              />
              窗口截图模式
            </label>
          </div>
          <div class="config-item">
            <label>
              <input
                v-model="config.loadCrossImg"
                type="checkbox"
              />
              加载跨域图片
            </label>
          </div>
          <div class="config-item">
            <label>
              <input
                v-model="config.imgAutoFit"
                type="checkbox"
              />
              图片自适应
            </label>
          </div>
        </div>
        
        <div class="config-row">
          <div class="config-item">
            <label>图片质量:</label>
            <input
              v-model.number="config.quality"
              type="range"
              min="0.1"
              max="1"
              step="0.1"
              class="quality-slider"
            />
            <span>{{ Math.round(config.quality * 100) }}%</span>
          </div>
        </div>
        
        <div class="config-row">
          <div class="config-item">
            <label>最大撤销次数:</label>
            <input
              v-model.number="config.maxUndoNum"
              type="number"
              min="1"
              max="100"
              class="number-input"
            />
          </div>
          <div class="config-item">
            <label>工具栏位置:</label>
            <select v-model="config.toolPosition" class="select-input">
              <option value="left">左对齐</option>
              <option value="center">居中</option>
              <option value="right">右对齐</option>
            </select>
          </div>
        </div>
        
        <div class="config-row">
          <div class="config-item">
            <label>蒙层颜色:</label>
            <div class="color-config">
              <input
                v-model.number="config.maskColor.r"
                type="range"
                min="0"
                max="255"
                class="color-slider"
              />
              <span>R: {{ config.maskColor.r }}</span>
              <input
                v-model.number="config.maskColor.g"
                type="range"
                min="0"
                max="255"
                class="color-slider"
              />
              <span>G: {{ config.maskColor.g }}</span>
              <input
                v-model.number="config.maskColor.b"
                type="range"
                min="0"
                max="255"
                class="color-slider"
              />
              <span>B: {{ config.maskColor.b }}</span>
              <input
                v-model.number="config.maskColor.a"
                type="range"
                min="0"
                max="1"
                step="0.1"
                class="color-slider"
              />
              <span>A: {{ config.maskColor.a }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-section">
        <button class="demo-btn primary" @click="startScreenShot">
          🎯 开始截图
        </button>
        <button class="demo-btn" @click="resetDemo">
          🔄 重置演示
        </button>
      </div>
      
      <!-- 截图结果显示 -->
      <div
        v-if="lastResult && hasResultData(lastResult)"
        class="result-section"
      >
        <h3>最后截图结果</h3>
        <div class="result-info">
          <p><strong>格式:</strong> {{ lastResult.format.toUpperCase() }}</p>
          <p>
            <strong>选区:</strong> {{ lastResult.selection.width }} ×
            {{ lastResult.selection.height }}
          </p>
          <p>
            <strong>位置:</strong> ({{ lastResult.selection.x }},
            {{ lastResult.selection.y }})
          </p>
          <p>
            <strong>包含编辑:</strong> {{ lastResult.hasEdits ? '是' : '否' }}
          </p>
          <p v-if="lastResult.hasEdits">
            <strong>编辑操作数:</strong>
            {{ lastResult.editHistory?.length || 0 }}
          </p>
          <p v-if="lastResult.cutInfo">
            <strong>裁剪信息:</strong>
            {{ lastResult.cutInfo.width }} × {{ lastResult.cutInfo.height }}
            @ ({{ lastResult.cutInfo.startX }}, {{ lastResult.cutInfo.startY }})
          </p>
        </div>
      </div>
      
      <!-- 功能说明 -->
      <div class="features-section">
        <h3>🎨 V3版本功能特性</h3>
        <div class="features-grid">
          <div class="feature-item">
            <h4>🖼️ 截图引擎</h4>
            <ul>
              <li>使用@zumer/snapdom替代html2canvas</li>
              <li>支持WebRTC屏幕共享</li>
              <li>支持自定义图片源</li>
              <li>支持跨域图片处理</li>
            </ul>
          </div>
          <div class="feature-item">
            <h4>✏️ 编辑工具</h4>
            <ul>
              <li>选择工具：移动和调整选区</li>
              <li>矩形工具：绘制矩形框</li>
              <li>圆形工具：绘制圆形</li>
              <li>箭头工具：绘制指向箭头</li>
              <li>画笔工具：自由绘制</li>
              <li>马赛克工具：敏感区域处理</li>
              <li>文本工具：添加文本标注</li>
            </ul>
          </div>
          <div class="feature-item">
            <h4>⚙️ 高级功能</h4>
            <ul>
              <li>无限次撤销/重做</li>
              <li>工具栏位置自定义</li>
              <li>蒙层颜色自定义</li>
              <li>快捷键支持</li>
              <li>剪切板写入</li>
              <li>多种图片格式</li>
              <li>移动端兼容</li>
            </ul>
          </div>
          <div class="feature-item">
            <h4>🔧 配置选项</h4>
            <ul>
              <li>工具图标隐藏</li>
              <li>自定义右键事件</li>
              <li>初始裁剪框设置</li>
              <li>图片质量控制</li>
              <li>容器层级设置</li>
              <li>滚动条处理</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- V3截图覆盖层 -->
    <ScreenShotOverlayV3
      :is-active="isActive"
      :is-capturing="isCapturing"
      :selection="selection"
      :drag-state="dragState"
      :edit-state="editState"
      :can-undo="canUndo"
      :can-redo="canRedo"
      :level="config.level"
      :cut-box-bd-color="config.cutBoxBdColor"
      :hidden-tool-ico="config.hiddenToolIco"
      :mask-color="config.maskColor"
      :tool-position="config.toolPosition"
      :canvas-width="config.canvasWidth"
      :canvas-height="config.canvasHeight"
      @mousedown="handleMouseDown"
      @cancel="cancelScreenShot"
      @save="saveScreenShot"
      @set-tool="setCurrentTool"
      @undo="undo"
      @redo="redo"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useScreenShotV3 } from './useScreenShotV3'
import ScreenShotOverlayV3 from './ScreenShotOverlayV3.vue'
import type { ScreenShotV3Options, ScreenShotV3Result } from './types'

// 配置选项
const config = reactive<ScreenShotV3Options>({
  enableWebRtc: false,
  quality: 0.9,
  defaultFormat: 'png',
  allowMove: true,
  allowResize: true,
  minSize: { width: 50, height: 50 },
  level: 9999,
  cutBoxBdColor: '#007acc',
  maxUndoNum: 50,
  clickCutFullScreen: false,
  writeBase64: true,
  wrcWindowMode: false,
  loadCrossImg: false,
  imgAutoFit: false,
  toolPosition: 'center',
  maskColor: { r: 0, g: 0, b: 0, a: 0.6 },
  hiddenToolIco: {},
  canvasWidth: 0,
  canvasHeight: 0,
  completeCallback: (result) => {
    lastResult.value = result as any
    console.log('V3截图完成:', result)
  },
  closeCallback: () => {
    console.log('V3截图取消')
  },
  triggerCallback: (result) => {
    console.log('V3截图响应:', result)
  },
  saveCallback: (result) => {
    console.log('V3截图保存:', result)
  }
})

// 最后的截图结果
const lastResult = ref<ScreenShotV3Result | null>(null)

// 类型守卫函数
const hasResultData = (result: ScreenShotV3Result): boolean => {
  return 'format' in result && 'selection' in result
}

// V3截图Hook
const {
  isActive,
  isCapturing,
  selection,
  dragState,
  editState,
  canUndo,
  canRedo,
  startScreenShot: startScreenShotOriginal,
  cancelScreenShot,
  saveScreenShot: saveScreenShotOriginal,
  setCurrentTool,
  handleMouseDown,
  undo,
  redo
} = useScreenShotV3(config)

/**
 * 开始截图
 */
const startScreenShot = async (): Promise<void> => {
  try {
    await startScreenShotOriginal()
  } catch (error) {
    console.error('启动截图失败:', error)
    alert('启动截图失败，请重试')
  }
}

/**
 * 保存截图并记录结果
 */
const saveScreenShot = async (): Promise<void> => {
  try {
    const result = await saveScreenShotOriginal()
    lastResult.value = result
    console.log('截图保存成功:', result)
  } catch (error) {
    console.error('截图保存失败:', error)
    alert('截图保存失败，请重试')
  }
}

/**
 * 重置演示
 */
const resetDemo = (): void => {
  lastResult.value = null
  if (isActive.value) {
    cancelScreenShot()
  }
}
</script>

<style scoped>
.screenshot-demo-v3 {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.demo-container {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.demo-description {
  color: #666;
  margin-bottom: 24px;
  line-height: 1.6;
}

.config-section {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.config-row {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-item label {
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.quality-slider,
.color-slider {
  width: 120px;
}

.number-input,
.select-input {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.color-config {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.color-config span {
  font-size: 12px;
  color: #666;
  min-width: 40px;
}

.action-section {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.demo-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  background: #f8f9fa;
  color: #333;
  border: 1px solid #dee2e6;
}

.demo-btn:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.demo-btn.primary {
  background: #007acc;
  color: white;
  border-color: #007acc;
}

.demo-btn.primary:hover {
  background: #005a9e;
  border-color: #005a9e;
}

.result-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #e8f5e8;
  border-radius: 8px;
  border: 1px solid #c3e6c3;
}

.result-info p {
  margin: 8px 0;
  font-size: 14px;
}

.result-info strong {
  color: #2d5a2d;
}

.features-section {
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.feature-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.feature-item h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
}

.feature-item ul {
  margin: 0;
  padding-left: 20px;
}

.feature-item li {
  margin: 6px 0;
  font-size: 14px;
  color: #555;
  line-height: 1.4;
}
</style>
