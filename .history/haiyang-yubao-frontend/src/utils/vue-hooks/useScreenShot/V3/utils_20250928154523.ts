/**
 * 截图功能V3版本工具函数
 * 基于js-web-screen-shot的完整功能实现
 * 使用@zumer/snapdom作为HTML转图片引擎
 */

import { snapdom } from '@zumer/snapdom'
import type { 
  ImageFormat, 
  SelectionArea, 
  ScreenShotV3Options,
  MaskColor,
  CropBoxInfo,
  EditTool
} from './types'

/**
 * 截图工具类V3版本
 */
export class ScreenShotV3Utils {
  /**
   * 使用@zumer/snapdom捕获页面截图
   * @param element 要截图的元素，默认为document.body
   * @param options 截图选项
   * @returns Promise<HTMLCanvasElement>
   */
  static async captureScreen(
    element: HTMLElement = document.body,
    options: ScreenShotV3Options = {}
  ): Promise<HTMLCanvasElement> {
    try {
      // 配置snapdom选项
      const snapdomOptions = {
        // 基础配置
        width: options.canvasWidth || window.innerWidth,
        height: options.canvasHeight || window.innerHeight,
        // 跨域图片处理
        allowTaint: options.loadCrossImg || false,
        useCORS: options.loadCrossImg || false,
        // 代理配置
        proxy: options.proxyUrl || undefined,
        // 图片自适应
        scale: options.imgAutoFit ? window.devicePixelRatio : 1,
        // 滚动处理
        scrollX: 0,
        scrollY: 0,
        // 背景处理
        backgroundColor: null
      }

      // 使用snapdom进行截图
      const result = await snapdom(element, snapdomOptions)
      return result.toCanvas()
    } catch (error) {
      console.error('截图失败:', error)
      throw new Error('截图失败，请重试')
    }
  }

  /**
   * 从WebRTC流中获取截图
   * @param stream 媒体流
   * @param options 截图选项
   * @returns Promise<HTMLCanvasElement>
   */
  static async captureFromStream(
    stream: MediaStream,
    options: ScreenShotV3Options = {}
  ): Promise<HTMLCanvasElement> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video')
      video.srcObject = stream
      video.autoplay = true
      video.muted = true
      
      video.onloadedmetadata = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')!
        
        canvas.width = options.canvasWidth || video.videoWidth
        canvas.height = options.canvasHeight || video.videoHeight
        
        // 等待视频准备就绪
        setTimeout(() => {
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
          
          // 如果需要裁剪
          if (options.wrcImgPosition) {
            const croppedCanvas = this.cropCanvas(canvas, options.wrcImgPosition)
            resolve(croppedCanvas)
          } else {
            resolve(canvas)
          }
          
          // 清理资源
          video.srcObject = null
          stream.getTracks().forEach(track => track.stop())
        }, options.wrcReplyTime || 500)
      }
      
      video.onerror = () => {
        reject(new Error('视频流加载失败'))
      }
    })
  }

  /**
   * 裁剪Canvas
   * @param sourceCanvas 源Canvas
   * @param cropInfo 裁剪信息
   * @returns HTMLCanvasElement
   */
  static cropCanvas(sourceCanvas: HTMLCanvasElement, cropInfo: CropBoxInfo): HTMLCanvasElement {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    canvas.width = cropInfo.w
    canvas.height = cropInfo.h
    
    ctx.drawImage(
      sourceCanvas,
      cropInfo.x, cropInfo.y, cropInfo.w, cropInfo.h,
      0, 0, cropInfo.w, cropInfo.h
    )
    
    return canvas
  }

  /**
   * 裁剪选区
   * @param sourceCanvas 源Canvas
   * @param selection 选区信息
   * @returns HTMLCanvasElement
   */
  static cropSelection(sourceCanvas: HTMLCanvasElement, selection: SelectionArea): HTMLCanvasElement {
    return this.cropCanvas(sourceCanvas, {
      x: selection.x,
      y: selection.y,
      w: selection.width,
      h: selection.height
    })
  }

  /**
   * Canvas转换为指定格式的数据URL
   * @param canvas Canvas元素
   * @param format 图片格式
   * @param quality 图片质量
   * @returns string
   */
  static canvasToDataURL(
    canvas: HTMLCanvasElement,
    format: ImageFormat,
    quality: number = 0.9
  ): string {
    const mimeType = this.getImageMimeType(format)
    return canvas.toDataURL(mimeType, quality)
  }

  /**
   * 获取图片MIME类型
   * @param format 图片格式
   * @returns string
   */
  static getImageMimeType(format: ImageFormat): string {
    const mimeTypes = {
      png: 'image/png',
      jpeg: 'image/jpeg',
      jpg: 'image/jpeg',
      webp: 'image/webp',
      bmp: 'image/bmp'
    }
    return mimeTypes[format] || 'image/png'
  }

  /**
   * 下载图片
   * @param dataUrl 图片数据URL
   * @param filename 文件名
   */
  static downloadImage(dataUrl: string, filename: string): void {
    const link = document.createElement('a')
    link.download = filename
    link.href = dataUrl
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  /**
   * 生成带时间戳的文件名
   * @param format 图片格式
   * @param prefix 文件名前缀
   * @returns string
   */
  static generateFilename(format: ImageFormat, prefix: string = 'screenshot'): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    return `${prefix}-${timestamp}.${format}`
  }

  /**
   * 将图片写入剪切板
   * @param canvas Canvas元素
   * @returns Promise<boolean>
   */
  static async writeToClipboard(canvas: HTMLCanvasElement): Promise<boolean> {
    try {
      if (!navigator.clipboard || !window.ClipboardItem) {
        console.warn('剪切板API不支持')
        return false
      }

      return new Promise((resolve) => {
        canvas.toBlob(async (blob) => {
          if (!blob) {
            resolve(false)
            return
          }

          try {
            const item = new ClipboardItem({ [blob.type]: blob })
            await navigator.clipboard.write([item])
            resolve(true)
          } catch (error) {
            console.error('写入剪切板失败:', error)
            resolve(false)
          }
        })
      })
    } catch (error) {
      console.error('剪切板操作失败:', error)
      return false
    }
  }

  /**
   * 检查点是否在矩形内
   * @param point 点坐标
   * @param rect 矩形区域
   * @returns boolean
   */
  static isPointInRect(
    point: { x: number; y: number },
    rect: { x: number; y: number; width: number; height: number }
  ): boolean {
    return (
      point.x >= rect.x &&
      point.x <= rect.x + rect.width &&
      point.y >= rect.y &&
      point.y <= rect.y + rect.height
    )
  }

  /**
   * 约束选区在画布范围内
   * @param selection 选区信息
   * @param canvasSize 画布尺寸
   * @returns SelectionArea
   */
  static constrainSelection(
    selection: SelectionArea,
    canvasSize: { width: number; height: number }
  ): SelectionArea {
    const result = { ...selection }
    
    // 确保选区不超出画布边界
    result.x = Math.max(0, Math.min(result.x, canvasSize.width - result.width))
    result.y = Math.max(0, Math.min(result.y, canvasSize.height - result.height))
    result.width = Math.min(result.width, canvasSize.width - result.x)
    result.height = Math.min(result.height, canvasSize.height - result.y)
    
    return result
  }

  /**
   * 获取窗口尺寸
   * @returns { width: number; height: number }
   */
  static getWindowSize(): { width: number; height: number } {
    return {
      width: window.innerWidth,
      height: window.innerHeight
    }
  }

  /**
   * 创建蒙层Canvas
   * @param width 宽度
   * @param height 高度
   * @param maskColor 蒙层颜色
   * @param selection 选区信息
   * @returns HTMLCanvasElement
   */
  static createMaskCanvas(
    width: number,
    height: number,
    maskColor: MaskColor,
    selection: SelectionArea
  ): HTMLCanvasElement {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    canvas.width = width
    canvas.height = height
    
    // 绘制蒙层
    ctx.fillStyle = `rgba(${maskColor.r}, ${maskColor.g}, ${maskColor.b}, ${maskColor.a})`
    ctx.fillRect(0, 0, width, height)
    
    // 清除选区部分
    ctx.globalCompositeOperation = 'destination-out'
    ctx.fillRect(selection.x, selection.y, selection.width, selection.height)
    ctx.globalCompositeOperation = 'source-over'
    
    return canvas
  }

  /**
   * 检测是否为移动设备
   * @returns boolean
   */
  static isMobileDevice(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  }

  /**
   * 检测是否支持WebRTC
   * @returns boolean
   */
  static isWebRTCSupported(): boolean {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia)
  }

  /**
   * 获取设备像素比
   * @returns number
   */
  static getDevicePixelRatio(): number {
    return window.devicePixelRatio || 1
  }
}
