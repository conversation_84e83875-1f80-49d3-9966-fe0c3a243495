<template>
  <div class="screenshot-demo">
    <div class="demo-header">
      <h2>自定义选区截图演示</h2>
      <p>点击下方按钮开始截图，支持拖拽移动和调整选区大小</p>
    </div>

    <div class="demo-content">
      <!-- 演示内容区域 -->
      <div class="content-area">
        <div class="demo-card">
          <h3>演示内容 1</h3>
          <p>这是一些演示文本内容，用于测试截图功能。</p>
          <div class="demo-chart">
            <div class="chart-bar" style="height: 60%"></div>
            <div class="chart-bar" style="height: 80%"></div>
            <div class="chart-bar" style="height: 40%"></div>
            <div class="chart-bar" style="height: 90%"></div>
          </div>
        </div>

        <div class="demo-card">
          <h3>演示内容 2</h3>
          <p>您可以选择任意区域进行截图，支持多种格式保存。</p>
          <div class="demo-image">
            <div class="placeholder-image">图片占位符</div>
          </div>
        </div>

        <div class="demo-card">
          <h3>功能特性</h3>
          <ul>
            <li>✅ 自定义选区截图</li>
            <li>✅ 拖拽移动选区</li>
            <li>✅ 调整选区大小</li>
            <li>✅ 多种图片格式</li>
            <li>✅ 高清晰度输出</li>
          </ul>
        </div>
      </div>

      <!-- 控制面板 -->
      <div class="control-panel">
        <div class="panel-section">
          <h4>截图控制</h4>
          <button 
            class="btn btn-primary" 
            @click="handleStartScreenShot"
            :disabled="isCapturing"
          >
            {{ isCapturing ? '正在截图...' : '开始截图' }}
          </button>
        </div>

        <div class="panel-section">
          <h4>配置选项</h4>
          <div class="config-item">
            <label>图片质量:</label>
            <input 
              v-model.number="config.quality" 
              type="range" 
              min="0.1" 
              max="1" 
              step="0.1"
              class="quality-slider"
            />
            <span>{{ (config.quality * 100).toFixed(0) }}%</span>
          </div>
          
          <div class="config-item">
            <label>默认格式:</label>
            <select v-model="config.defaultFormat" class="format-select">
              <option value="png">PNG</option>
              <option value="jpeg">JPEG</option>
              <option value="webp">WebP</option>
            </select>
          </div>
          
          <div class="config-item">
            <label>
              <input 
                v-model="config.allowMove" 
                type="checkbox"
              />
              允许移动选区
            </label>
          </div>
          
          <div class="config-item">
            <label>
              <input 
                v-model="config.allowResize" 
                type="checkbox"
              />
              允许调整大小
            </label>
          </div>
        </div>

        <div class="panel-section" v-if="lastResult">
          <h4>最后截图结果</h4>
          <div class="result-info">
            <p><strong>格式:</strong> {{ lastResult.format.toUpperCase() }}</p>
            <p><strong>选区:</strong> {{ lastResult.selection.width }} × {{ lastResult.selection.height }}</p>
            <p><strong>位置:</strong> ({{ lastResult.selection.x }}, {{ lastResult.selection.y }})</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 截图覆盖层 -->
    <ScreenShotOverlay
      :is-active="isActive"
      :is-capturing="isCapturing"
      :selection="selection"
      :selected-format="selectedFormat"
      @mousedown="handleMouseDown"
      @cancel="cancelScreenShot"
      @save="handleSaveScreenShot"
      @format-change="setImageFormat"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useScreenShot, ScreenShotOverlay } from './index';
import type { ScreenShotOptions, ScreenShotResult } from './types';

// 配置选项
const config = reactive<ScreenShotOptions>({
  quality: 0.9,
  defaultFormat: 'png',
  allowMove: true,
  allowResize: true,
  minSize: { width: 50, height: 50 }
});

// 最后的截图结果
const lastResult = ref<ScreenShotResult | null>(null);

// 使用截图Hook
const {
  isActive,
  isCapturing,
  selection,
  selectedFormat,
  startScreenShot,
  cancelScreenShot,
  saveScreenShot,
  setImageFormat,
  handleMouseDown
} = useScreenShot(config);

/**
 * 开始截图
 */
const handleStartScreenShot = async (): Promise<void> => {
  try {
    await startScreenShot();
  } catch (error) {
    console.error('启动截图失败:', error);
    alert('截图失败，请重试');
  }
};

/**
 * 保存截图
 */
const handleSaveScreenShot = async (): Promise<void> => {
  try {
    const result = await saveScreenShot();
    lastResult.value = result;
    console.log('截图保存成功:', result);
  } catch (error) {
    console.error('保存截图失败:', error);
    alert('保存失败，请重试');
  }
};
</script>

<style scoped lang="scss">
.screenshot-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
  
  h2 {
    color: #333;
    margin-bottom: 10px;
  }
  
  p {
    color: #666;
    font-size: 14px;
  }
}

.demo-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 30px;
}

.content-area {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.demo-card {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  h3 {
    margin-top: 0;
    color: #333;
  }
  
  p {
    color: #666;
    line-height: 1.6;
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 5px;
      color: #666;
    }
  }
}

.demo-chart {
  display: flex;
  align-items: end;
  gap: 10px;
  height: 100px;
  margin-top: 15px;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(to top, #409eff, #66b3ff);
  border-radius: 4px 4px 0 0;
  min-height: 20px;
}

.demo-image {
  margin-top: 15px;
}

.placeholder-image {
  width: 100%;
  height: 120px;
  background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
}

.control-panel {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  height: fit-content;
}

.panel-section {
  margin-bottom: 25px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
  }
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  
  &.btn-primary {
    background-color: #409eff;
    color: #fff;
    
    &:hover:not(:disabled) {
      background-color: #66b3ff;
    }
    
    &:disabled {
      background-color: #c0c4cc;
      cursor: not-allowed;
    }
  }
}

.config-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  
  label {
    min-width: 80px;
    color: #666;
  }
  
  input[type="checkbox"] {
    margin-right: 8px;
  }
}

.quality-slider {
  flex: 1;
  margin: 0 10px;
}

.format-select {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
}

.result-info {
  background-color: #fff;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  
  p {
    margin: 5px 0;
    font-size: 13px;
    color: #666;
  }
}
</style>
