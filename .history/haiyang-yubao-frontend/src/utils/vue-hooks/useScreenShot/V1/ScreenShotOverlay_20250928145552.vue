<template>
  <div
    v-if="isActive"
    class="screenshot-overlay"
    @mousedown="handleMouseDown"
    @contextmenu.prevent
  >
    <!-- 遮罩层 -->
    <div class="overlay-mask">
      <!-- 上方遮罩 -->
      <div
        class="mask-area"
        :style="{
          left: 0,
          top: 0,
          width: '100%',
          height: `${selection.y}px`
        }"
      />

      <!-- 左侧遮罩 -->
      <div
        class="mask-area"
        :style="{
          left: 0,
          top: `${selection.y}px`,
          width: `${selection.x}px`,
          height: `${selection.height}px`
        }"
      />

      <!-- 右侧遮罩 -->
      <div
        class="mask-area"
        :style="{
          left: `${selection.x + selection.width}px`,
          top: `${selection.y}px`,
          width: `${Math.max(
            0,
            getWindowSize().width - selection.x - selection.width
          )}px`,
          height: `${selection.height}px`
        }"
      />

      <!-- 下方遮罩 -->
      <div
        class="mask-area"
        :style="{
          left: 0,
          top: `${selection.y + selection.height}px`,
          width: '100%',
          height: `${Math.max(
            0,
            getWindowSize().height - selection.y - selection.height
          )}px`
        }"
      />
    </div>

    <!-- 选区框 -->
    <div
      class="selection-box"
      :style="{
        left: `${selection.x}px`,
        top: `${selection.y}px`,
        width: `${selection.width}px`,
        height: `${selection.height}px`
      }"
    >
      <!-- 选区边框 -->
      <div class="selection-border" />

      <!-- 调整大小手柄 -->
      <div class="resize-handle top-left" />
      <div class="resize-handle bottom-right" />

      <!-- 工具栏 -->
      <div class="toolbar" :style="toolbarStyle">
        <!-- 导出类型 -->
        <div class="export-type-section">
          <div class="section-label">导出类型：</div>
          <div class="radio-group">
            <label class="radio-item">
              <input type="radio" name="exportType" value="image" checked />
              <span class="radio-dot"></span>
              <span class="radio-text">导出图片</span>
            </label>
          </div>
        </div>

        <!-- 图片格式 -->
        <div class="format-section">
          <div class="section-label">图片格式：</div>
          <div class="radio-group">
            <label
              class="radio-item"
              :class="{ active: selectedFormat === 'png' }"
            >
              <input
                type="radio"
                name="imageFormat"
                value="png"
                :checked="selectedFormat === 'png'"
                @change="setImageFormat('png')"
              />
              <span class="radio-dot"></span>
              <span class="radio-text">png</span>
            </label>
            <label
              class="radio-item"
              :class="{ active: selectedFormat === 'jpeg' }"
            >
              <input
                type="radio"
                name="imageFormat"
                value="jpeg"
                :checked="selectedFormat === 'jpeg'"
                @change="setImageFormat('jpeg')"
              />
              <span class="radio-dot"></span>
              <span class="radio-text">jpg</span>
            </label>
            <label
              class="radio-item"
              :class="{ active: selectedFormat === 'webp' }"
            >
              <input
                type="radio"
                name="imageFormat"
                value="webp"
                :checked="selectedFormat === 'webp'"
                @change="setImageFormat('webp')"
              />
              <span class="radio-dot"></span>
              <span class="radio-text">bmp</span>
            </label>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button class="btn btn-confirm" @click="saveScreenShot">确认</button>
          <button class="btn btn-cancel" @click="cancelScreenShot">取消</button>
        </div>
      </div>
    </div>

    <!-- 加载提示 -->
    <div v-if="isCapturing" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner" />
        <div class="loading-text">正在截图...</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ImageFormat } from './types'

// 获取窗口尺寸
const getWindowSize = () => {
  if (typeof window !== 'undefined') {
    return {
      width: window.innerWidth,
      height: window.innerHeight
    }
  }
  return { width: 1920, height: 1080 } // 默认尺寸
}

// Props
interface Props {
  isActive: boolean
  isCapturing: boolean
  selection: {
    x: number
    y: number
    width: number
    height: number
  }
  selectedFormat: ImageFormat
}

// Emits
interface Emits {
  (e: 'mousedown', event: MouseEvent): void
  (e: 'cancel'): void
  (e: 'save'): void
  (e: 'format-change', format: ImageFormat): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

/**
 * 工具栏位置计算
 * 确保工具栏始终在可视区域内
 */
const toolbarStyle = computed(() => {
  const { x, y, height } = props.selection
  const toolbarHeight = 120 // 增加高度以适应新的工具栏布局
  const toolbarWidth = 320 // 增加宽度以适应新的工具栏布局
  const windowSize = getWindowSize()

  // 默认显示在选区左下角
  let left = x
  let top = y + height + 10

  // 如果超出右边界，向左调整
  if (left + toolbarWidth > windowSize.width) {
    left = windowSize.width - toolbarWidth - 10
  }

  // 如果超出下边界，显示在选区上方
  if (top + toolbarHeight > windowSize.height) {
    top = y - toolbarHeight - 10
  }

  // 确保不超出左边界和上边界
  left = Math.max(10, left)
  top = Math.max(10, top)

  return {
    left: `${left}px`,
    top: `${top}px`
  }
})

/**
 * 处理鼠标按下事件
 */
const handleMouseDown = (event: MouseEvent): void => {
  emit('mousedown', event)
}

/**
 * 取消截图
 */
const cancelScreenShot = (): void => {
  emit('cancel')
}

/**
 * 保存截图
 */
const saveScreenShot = (): void => {
  emit('save')
}

/**
 * 设置图片格式
 */
const setImageFormat = (format: ImageFormat): void => {
  emit('format-change', format)
}
</script>

<style scoped lang="scss">
.screenshot-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  cursor: crosshair;
  user-select: none;
}

.overlay-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.mask-area {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.5);
  pointer-events: none;
}

.selection-box {
  position: absolute;
  cursor: move;
}

.selection-border {
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border: 2px solid #409eff;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.8);
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #409eff;
  border: 1px solid #fff;
  cursor: nw-resize;

  &.top-left {
    top: -4px;
    left: -4px;
  }

  &.bottom-right {
    bottom: -4px;
    right: -4px;
    cursor: se-resize;
  }
}

.toolbar {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.85);
  border-radius: 8px;
  padding: 16px 20px;
  color: #ffffff;
  font-size: 14px;
  min-width: 320px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.export-type-section,
.format-section {
  margin-bottom: 16px;

  &:last-of-type {
    margin-bottom: 20px;
  }
}

.section-label {
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 500;
}

.radio-group {
  display: flex;
  gap: 16px;
  align-items: center;
}

.radio-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #ffffff;
  font-size: 14px;

  input[type='radio'] {
    display: none;
  }

  .radio-dot {
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    margin-right: 6px;
    position: relative;
    transition: all 0.2s ease;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #ffffff;
      opacity: 0;
      transition: opacity 0.2s ease;
    }
  }

  input[type='radio']:checked + .radio-dot::after {
    opacity: 1;
  }

  &:hover .radio-dot {
    border-color: #66b3ff;
  }

  &.active .radio-dot {
    border-color: #409eff;
  }

  .radio-text {
    user-select: none;
  }
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;

  &.btn-confirm {
    background-color: #409eff;
    color: #ffffff;

    &:hover {
      background-color: #66b3ff;
    }

    &:active {
      background-color: #337ecc;
    }
  }

  &.btn-cancel {
    background-color: #6c757d;
    color: #ffffff;

    &:hover {
      background-color: #5a6268;
    }

    &:active {
      background-color: #545b62;
    }
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: #fff;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 14px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
