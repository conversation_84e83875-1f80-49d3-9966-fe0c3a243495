<template>
  <div class="screenshot-demo-v2">
    <!-- 演示内容 -->
    <div class="demo-content">
      <h1>截图功能V2演示</h1>
      <p>这是一个包含编辑功能的高级截图工具演示页面。</p>

      <div class="feature-grid">
        <div class="feature-card">
          <h3>🎯 精确选区</h3>
          <p>支持拖拽移动和8个方向的大小调整</p>
        </div>

        <div class="feature-card">
          <h3>🎨 编辑工具</h3>
          <p>画笔、矩形、箭头、文本、马赛克等多种编辑工具</p>
        </div>

        <div class="feature-card">
          <h3>↩️ 撤销重做</h3>
          <p>支持无限次撤销和重做操作</p>
        </div>

        <div class="feature-card">
          <h3>💾 多格式导出</h3>
          <p>支持PNG、JPEG、WebP等多种图片格式</p>
        </div>
      </div>

      <div class="demo-actions">
        <button
          class="demo-btn primary"
          :disabled="isActive || isCapturing"
          @click="startScreenShot"
        >
          {{ isCapturing ? '正在截图...' : '开始截图' }}
        </button>

        <button class="demo-btn" @click="resetDemo">重置演示</button>
      </div>

      <!-- 截图结果显示 -->
      <div
        v-if="lastResult && hasResultData(lastResult)"
        class="result-section"
      >
        <h3>最后截图结果</h3>
        <div class="result-info">
          <p><strong>格式:</strong> {{ lastResult.format.toUpperCase() }}</p>
          <p>
            <strong>选区:</strong> {{ lastResult.selection.width }} ×
            {{ lastResult.selection.height }}
          </p>
          <p>
            <strong>包含编辑:</strong> {{ lastResult.hasEdits ? '是' : '否' }}
          </p>
          <p v-if="lastResult.hasEdits">
            <strong>编辑操作数:</strong>
            {{ lastResult.editHistory?.length || 0 }}
          </p>
        </div>
      </div>
    </div>

    <!-- V2截图覆盖层 -->
    <ScreenShotOverlayV2
      :is-active="isActive"
      :is-capturing="isCapturing"
      :selection="selection"
      :drag-state="dragState"
      :edit-state="editState"
      :selected-format="selectedFormat"
      @mousedown="handleMouseDown"
      @cancel="cancelScreenShot"
      @save="saveScreenShot"
      @set-tool="setCurrentTool"
      @undo="undo"
      @redo="redo"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useScreenShotV2 } from './useScreenShotV2'
import ScreenShotOverlayV2 from './ScreenShotOverlayV2.vue'
import type { ScreenShotV2Result } from './types'

/**
 * 使用V2截图Hook
 */
const {
  // 状态
  isActive,
  isCapturing,
  selection,
  selectedFormat,
  dragState,
  editState,

  // 方法
  startScreenShot,
  cancelScreenShot,
  saveScreenShot: saveScreenShotOriginal,
  setCurrentTool,
  undo,
  redo,
  handleMouseDown
} = useScreenShotV2({
  enableEdit: true,
  defaultTool: 'select',
  availableTools: ['select', 'brush', 'rectangle', 'arrow', 'text', 'mosaic'],
  enableUndo: true,
  maxUndoSteps: 50
})

// 最后的截图结果
const lastResult = ref<ScreenShotV2Result | null>(null)

// 类型守卫函数
const hasResultData = (result: ScreenShotV2Result): boolean => {
  return 'format' in result && 'selection' in result
}

/**
 * 保存截图并记录结果
 */
const saveScreenShot = async (): Promise<void> => {
  try {
    const result = await saveScreenShotOriginal()
    lastResult.value = result
    console.log('截图保存成功:', result)
  } catch (error) {
    console.error('截图保存失败:', error)
    alert('截图保存失败，请重试')
  }
}

/**
 * 重置演示
 */
const resetDemo = (): void => {
  lastResult.value = null
  if (isActive.value) {
    cancelScreenShot()
  }
}
</script>

<style scoped lang="scss">
.screenshot-demo-v2 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
  color: #ffffff;

  h1 {
    font-size: 3rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  > p {
    font-size: 1.2rem;
    text-align: center;
    margin-bottom: 3rem;
    opacity: 0.9;
  }
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  }

  h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #ffffff;
  }

  p {
    opacity: 0.9;
    line-height: 1.6;
  }
}

.demo-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
}

.demo-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.3);

  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.primary {
    background: #409eff;
    border-color: #409eff;

    &:hover:not(:disabled) {
      background: #66b3ff;
      border-color: #66b3ff;
    }
  }
}

.result-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);

  h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #ffffff;
  }
}

.result-info {
  p {
    margin-bottom: 0.5rem;
    opacity: 0.9;

    strong {
      color: #ffffff;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .demo-content {
    padding: 0 1rem;

    h1 {
      font-size: 2rem;
    }

    > p {
      font-size: 1rem;
    }
  }

  .feature-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .demo-actions {
    flex-direction: column;
    align-items: center;
  }

  .demo-btn {
    width: 200px;
  }
}
</style>
