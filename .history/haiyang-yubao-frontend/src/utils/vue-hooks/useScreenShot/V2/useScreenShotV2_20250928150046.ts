import { ref, reactive, nextTick } from 'vue'
import type {
  ScreenShotV2Options,
  SelectionArea,
  DragState,
  ImageFormat,
  ScreenShotV2Result,
  EditTool,
  EditState,
  EditOperation,
  EditToolConfig,
  BrushConfig,
  RectangleConfig,
  ArrowConfig,
  TextConfig,
  MosaicConfig
} from './types'
import { ScreenShotV2Utils } from './utils'

/**
 * V2版本自定义选区截图Hook
 * 包含编辑功能：画笔、框选、标记箭头、马赛克等
 * @param options 截图配置选项
 * @returns 截图相关的状态和方法
 */
export function useScreenShotV2(options: ScreenShotV2Options = {}) {
  // 默认配置
  const defaultOptions: Required<ScreenShotV2Options> = {
    quality: 0.9,
    defaultFormat: 'png',
    allowMove: true,
    allowResize: true,
    minSize: { width: 50, height: 50 },
    enableEdit: true,
    defaultTool: 'select',
    availableTools: [
      'select',
      'brush',
      'rectangle',
      'arrow',
      'text',
      'mosaic',
      'eraser'
    ],
    toolConfig: {},
    enableUndo: true,
    maxUndoSteps: 50
  }

  const config = { ...defaultOptions, ...options }

  // 默认工具配置
  const defaultToolConfig: EditToolConfig = {
    brush: {
      color: '#ff0000',
      size: 3,
      opacity: 1
    },
    rectangle: {
      strokeColor: '#ff0000',
      fillColor: '#ff0000',
      strokeWidth: 2,
      fillOpacity: 0.2
    },
    arrow: {
      color: '#ff0000',
      width: 2,
      headSize: 10
    },
    text: {
      color: '#000000',
      fontSize: 16,
      fontFamily: 'Arial, sans-serif',
      fontWeight: 'normal'
    },
    mosaic: {
      blockSize: 10,
      intensity: 1
    }
  }

  // 响应式状态
  const isActive = ref(false)
  const isCapturing = ref(false)
  const selectedFormat = ref<ImageFormat>(config.defaultFormat)

  // 选区状态
  const selection = reactive<SelectionArea>({
    x: 100,
    y: 100,
    width: 300,
    height: 200
  })

  // 拖拽状态
  const dragState = reactive<DragState>({
    isDragging: false,
    dragType: null,
    resizeDirection: undefined,
    startMousePos: { x: 0, y: 0 },
    startSelection: { x: 0, y: 0, width: 0, height: 0 }
  })

  // 编辑状态
  const editState = reactive<EditState>({
    currentTool: config.defaultTool,
    isDrawing: false,
    currentPath: null,
    history: [],
    historyIndex: -1,
    toolConfig: { ...defaultToolConfig, ...config.toolConfig }
  })

  // 截图Canvas缓存
  let capturedCanvas: HTMLCanvasElement | null = null
  // 编辑Canvas
  let editCanvas: HTMLCanvasElement | null = null
  let editCtx: CanvasRenderingContext2D | null = null

  /**
   * 开始截图流程
   */
  const startScreenShot = async (): Promise<void> => {
    try {
      isCapturing.value = true

      // 等待DOM更新
      await nextTick()

      // 捕获当前页面
      capturedCanvas = await ScreenShotV2Utils.captureElement()

      // 创建编辑Canvas
      createEditCanvas()

      // 激活选区界面
      isActive.value = true

      // 重置选区到屏幕中央
      resetSelection()

      // 重置编辑状态
      resetEditState()
    } catch (error) {
      console.error('截图失败:', error)
      throw error
    } finally {
      isCapturing.value = false
    }
  }

  /**
   * 创建编辑Canvas
   */
  const createEditCanvas = (): void => {
    if (!capturedCanvas) return

    editCanvas = document.createElement('canvas')
    editCanvas.width = capturedCanvas.width
    editCanvas.height = capturedCanvas.height
    editCtx = editCanvas.getContext('2d')

    if (editCtx) {
      // 将原始截图绘制到编辑Canvas上
      editCtx.drawImage(capturedCanvas, 0, 0)
    }
  }

  /**
   * 重置选区到屏幕中央
   */
  const resetSelection = (): void => {
    const centerX = (window.innerWidth - 300) / 2
    const centerY = (window.innerHeight - 200) / 2

    selection.x = Math.max(0, centerX)
    selection.y = Math.max(0, centerY)
    selection.width = 300
    selection.height = 200
  }

  /**
   * 重置编辑状态
   */
  const resetEditState = (): void => {
    editState.currentTool = config.defaultTool
    editState.isDrawing = false
    editState.currentPath = null
    editState.history = []
    editState.historyIndex = -1
  }

  /**
   * 取消截图
   */
  const cancelScreenShot = (): void => {
    isActive.value = false
    capturedCanvas = null
    editCanvas = null
    editCtx = null
    resetDragState()
    resetEditState()
  }

  /**
   * 保存截图
   */
  const saveScreenShot = async (): Promise<ScreenShotV2Result> => {
    if (!editCanvas) {
      throw new Error('没有可用的截图数据')
    }

    try {
      // 裁剪选中区域
      const croppedCanvas = ScreenShotV2Utils.cropCanvas(editCanvas, selection)

      // 转换为DataURL
      const dataUrl = ScreenShotV2Utils.canvasToDataURL(
        croppedCanvas,
        selectedFormat.value,
        config.quality
      )

      // 生成文件名并下载
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const filename = `screenshot-${timestamp}.${selectedFormat.value}`
      ScreenShotV2Utils.downloadImage(dataUrl, filename)

      // 关闭截图界面
      cancelScreenShot()

      return {
        dataUrl,
        format: selectedFormat.value,
        selection: { ...selection },
        editHistory: [...editState.history],
        hasEdits: editState.history.length > 0
      }
    } catch (error) {
      console.error('保存截图失败:', error)
      throw error
    }
  }

  /**
   * 重置拖拽状态
   */
  const resetDragState = (): void => {
    dragState.isDragging = false
    dragState.dragType = null
    dragState.resizeDirection = undefined
  }

  /**
   * 设置当前编辑工具
   */
  const setCurrentTool = (tool: EditTool): void => {
    editState.currentTool = tool
    // 如果正在绘制，结束当前绘制
    if (editState.isDrawing) {
      finishCurrentEdit()
    }
  }

  /**
   * 完成当前编辑操作
   */
  const finishCurrentEdit = (): void => {
    if (editState.currentPath && editState.isDrawing) {
      // 添加到历史记录
      addToHistory({
        id: ScreenShotV2Utils.generateId(),
        type: editState.currentTool,
        data: editState.currentPath,
        timestamp: Date.now()
      })
    }

    editState.isDrawing = false
    editState.currentPath = null
  }

  /**
   * 添加操作到历史记录
   */
  const addToHistory = (operation: EditOperation): void => {
    // 如果当前不在历史记录末尾，删除后面的记录
    if (editState.historyIndex < editState.history.length - 1) {
      editState.history.splice(editState.historyIndex + 1)
    }

    // 添加新操作
    editState.history.push(operation)
    editState.historyIndex = editState.history.length - 1

    // 限制历史记录数量
    if (editState.history.length > config.maxUndoSteps) {
      editState.history.shift()
      editState.historyIndex--
    }
  }

  /**
   * 撤销操作
   */
  const undo = (): void => {
    if (!config.enableUndo || editState.historyIndex < 0) return

    editState.historyIndex--
    redrawCanvas()
  }

  /**
   * 重做操作
   */
  const redo = (): void => {
    if (
      !config.enableUndo ||
      editState.historyIndex >= editState.history.length - 1
    )
      return

    editState.historyIndex++
    redrawCanvas()
  }

  /**
   * 重新绘制Canvas
   */
  const redrawCanvas = (): void => {
    if (!editCtx || !capturedCanvas) return

    // 清空Canvas并重新绘制原始截图
    editCtx.clearRect(0, 0, editCanvas!.width, editCanvas!.height)
    editCtx.drawImage(capturedCanvas, 0, 0)

    // 重新绘制所有历史操作
    for (let i = 0; i <= editState.historyIndex; i++) {
      const operation = editState.history[i]
      drawOperation(operation)
    }
  }

  /**
   * 绘制单个操作
   */
  const drawOperation = (operation: EditOperation): void => {
    if (!editCtx) return

    switch (operation.type) {
      case 'brush':
        ScreenShotV2Utils.drawBrushPath(editCtx, operation.data)
        break
      case 'rectangle':
        ScreenShotV2Utils.drawRectangle(editCtx, operation.data)
        break
      case 'arrow':
        ScreenShotV2Utils.drawArrow(editCtx, operation.data)
        break
      case 'text':
        ScreenShotV2Utils.drawText(editCtx, operation.data)
        break
      case 'mosaic':
        ScreenShotV2Utils.applyMosaic(editCtx, operation.data)
        break
    }
  }

  /**
   * 设置图片格式
   */
  const setImageFormat = (format: ImageFormat): void => {
    selectedFormat.value = format
  }

  /**
   * 更新工具配置
   */
  const updateToolConfig = <T extends keyof EditToolConfig>(
    tool: T,
    config: Partial<EditToolConfig[T]>
  ): void => {
    editState.toolConfig[tool] = { ...editState.toolConfig[tool], ...config }
  }

  /**
   * 处理鼠标按下事件
   */
  const handleMouseDown = (event: MouseEvent): void => {
    if (!isActive.value) return

    const mousePos = { x: event.clientX, y: event.clientY }

    // 如果是编辑工具，处理编辑操作
    if (editState.currentTool !== 'select') {
      handleEditMouseDown(mousePos)
      return
    }

    // 检查是否点击了调整大小的手柄
    const handles = ScreenShotV2Utils.getResizeHandles(selection)

    if (
      config.allowResize &&
      ScreenShotV2Utils.isPointInRect(mousePos, handles.topLeft)
    ) {
      startDrag('resize', 'top-left', mousePos)
    } else if (
      config.allowResize &&
      ScreenShotV2Utils.isPointInRect(mousePos, handles.bottomRight)
    ) {
      startDrag('resize', 'bottom-right', mousePos)
    } else if (
      config.allowMove &&
      ScreenShotV2Utils.isPointInRect(mousePos, selection)
    ) {
      startDrag('move', undefined, mousePos)
    }
  }

  /**
   * 处理编辑工具的鼠标按下事件
   */
  const handleEditMouseDown = (mousePos: { x: number; y: number }): void => {
    // 检查是否在选区内
    if (!ScreenShotV2Utils.isPointInRect(mousePos, selection)) return

    editState.isDrawing = true

    // 转换为相对于选区的坐标
    const relativePos = {
      x: mousePos.x - selection.x,
      y: mousePos.y - selection.y
    }

    switch (editState.currentTool) {
      case 'brush':
        editState.currentPath = {
          points: [relativePos],
          config: { ...editState.toolConfig.brush }
        }
        break
      case 'rectangle':
      case 'arrow':
        editState.currentPath = {
          startX: relativePos.x,
          startY: relativePos.y,
          endX: relativePos.x,
          endY: relativePos.y
        }
        break
    }

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleEditMouseMove)
    document.addEventListener('mouseup', handleEditMouseUp)
  }

  /**
   * 处理编辑工具的鼠标移动事件
   */
  const handleEditMouseMove = (event: MouseEvent): void => {
    if (!editState.isDrawing) return

    const mousePos = { x: event.clientX, y: event.clientY }
    const relativePos = {
      x: mousePos.x - selection.x,
      y: mousePos.y - selection.y
    }

    switch (editState.currentTool) {
      case 'brush':
        if (editState.currentPath?.points) {
          editState.currentPath.points.push(relativePos)
        }
        break
      case 'rectangle':
      case 'arrow':
        if (editState.currentPath) {
          editState.currentPath.endX = relativePos.x
          editState.currentPath.endY = relativePos.y
        }
        break
    }
  }

  /**
   * 处理编辑工具的鼠标释放事件
   */
  const handleEditMouseUp = (): void => {
    if (editState.isDrawing) {
      finishCurrentEdit()
    }

    // 移除全局鼠标事件监听
    document.removeEventListener('mousemove', handleEditMouseMove)
    document.removeEventListener('mouseup', handleEditMouseUp)
  }

  /**
   * 开始拖拽
   */
  const startDrag = (
    type: 'move' | 'resize',
    direction: 'top-left' | 'bottom-right' | undefined,
    mousePos: { x: number; y: number }
  ): void => {
    dragState.isDragging = true
    dragState.dragType = type
    dragState.resizeDirection = direction
    dragState.startMousePos = { ...mousePos }
    dragState.startSelection = { ...selection }

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }

  /**
   * 处理鼠标移动事件
   */
  const handleMouseMove = (event: MouseEvent): void => {
    if (!dragState.isDragging) return

    const deltaX = event.clientX - dragState.startMousePos.x
    const deltaY = event.clientY - dragState.startMousePos.y

    if (dragState.dragType === 'move') {
      // 移动选区
      const newX = dragState.startSelection.x + deltaX
      const newY = dragState.startSelection.y + deltaY

      const constrainedSelection = ScreenShotV2Utils.constrainSelection(
        { ...selection, x: newX, y: newY },
        window.innerWidth,
        window.innerHeight
      )

      selection.x = constrainedSelection.x
      selection.y = constrainedSelection.y
    } else if (dragState.dragType === 'resize') {
      // 调整选区大小
      handleResize(deltaX, deltaY)
    }
  }

  /**
   * 处理选区大小调整
   */
  const handleResize = (deltaX: number, deltaY: number): void => {
    const { startSelection } = dragState

    if (dragState.resizeDirection === 'top-left') {
      // 从左上角调整
      const newX = startSelection.x + deltaX
      const newY = startSelection.y + deltaY
      const newWidth = startSelection.width - deltaX
      const newHeight = startSelection.height - deltaY

      if (
        newWidth >= config.minSize.width &&
        newHeight >= config.minSize.height
      ) {
        selection.x = Math.max(0, newX)
        selection.y = Math.max(0, newY)
        selection.width = newWidth
        selection.height = newHeight
      }
    } else if (dragState.resizeDirection === 'bottom-right') {
      // 从右下角调整
      const newWidth = startSelection.width + deltaX
      const newHeight = startSelection.height + deltaY

      if (
        newWidth >= config.minSize.width &&
        newHeight >= config.minSize.height
      ) {
        const constrainedSelection = ScreenShotV2Utils.constrainSelection(
          { ...selection, width: newWidth, height: newHeight },
          window.innerWidth,
          window.innerHeight
        )

        selection.width = constrainedSelection.width
        selection.height = constrainedSelection.height
      }
    }
  }

  /**
   * 处理鼠标释放事件
   */
  const handleMouseUp = (): void => {
    resetDragState()

    // 移除全局鼠标事件监听
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  return {
    // 状态
    isActive,
    isCapturing,
    selection,
    selectedFormat,
    dragState,
    editState,

    // 方法
    startScreenShot,
    cancelScreenShot,
    saveScreenShot,
    setImageFormat,
    setCurrentTool,
    updateToolConfig,
    undo,
    redo,

    // 工具方法
    resetSelection,
    finishCurrentEdit,
    handleMouseDown
  }
}
