<template>
  <!-- 截图覆盖层 -->
  <div v-if="isActive" class="screenshot-overlay" @mousedown="handleMouseDown">
    <!-- 遮罩层 -->
    <div class="mask-layer">
      <!-- 上方遮罩 -->
      <div
        class="mask-area mask-top"
        :style="{
          height: `${selection.y}px`
        }"
      />

      <!-- 中间行 -->
      <div
        class="mask-row"
        :style="{
          top: `${selection.y}px`,
          height: `${selection.height}px`
        }"
      >
        <!-- 左侧遮罩 -->
        <div
          class="mask-area mask-left"
          :style="{
            width: `${selection.x}px`
          }"
        />

        <!-- 选区 -->
        <div class="selection-area" :style="selectionStyle">
          <!-- 选区尺寸显示 -->
          <div class="selection-info">
            {{ selection.width }} × {{ selection.height }}
          </div>

          <!-- 调整大小手柄 -->
          <div v-if="editState.currentTool === 'select'" class="resize-handles">
            <!-- 8个调整手柄 -->
            <div class="resize-handle top-left" />
            <div class="resize-handle top-center" />
            <div class="resize-handle top-right" />
            <div class="resize-handle middle-left" />
            <div class="resize-handle middle-right" />
            <div class="resize-handle bottom-left" />
            <div class="resize-handle bottom-center" />
            <div class="resize-handle bottom-right" />
          </div>

          <!-- 编辑Canvas覆盖层 -->
          <canvas
            v-if="editState.currentTool !== 'select'"
            ref="editCanvasRef"
            class="edit-canvas"
            :width="selection.width"
            :height="selection.height"
            @mousedown.stop="handleEditCanvasMouseDown"
          />
        </div>

        <!-- 右侧遮罩 -->
        <div
          class="mask-area mask-right"
          :style="{
            left: `${selection.x + selection.width}px`,
            width: `calc(100vw - ${selection.x + selection.width}px)`
          }"
        />
      </div>

      <!-- 下方遮罩 -->
      <div
        class="mask-area mask-bottom"
        :style="{
          top: `${selection.y + selection.height}px`,
          height: `calc(100vh - ${selection.y + selection.height}px)`
        }"
      />
    </div>

    <!-- 底部工具栏 -->
    <div class="bottom-toolbar" :style="toolbarStyle">
      <!-- 工具选择区 -->
      <div class="tool-section">
        <!-- 选择工具 -->
        <button
          class="tool-btn"
          :class="{ active: editState.currentTool === 'select' }"
          title="选择"
          @click="setCurrentTool('select')"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <rect
              x="2"
              y="2"
              width="12"
              height="12"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-dasharray="2,2"
            />
          </svg>
        </button>

        <!-- 画笔工具 -->
        <button
          class="tool-btn"
          :class="{ active: editState.currentTool === 'brush' }"
          title="画笔"
          @click="setCurrentTool('brush')"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path d="M2 14l4-4 8-8-2-2-8 8-4 4 2 2z" fill="currentColor" />
          </svg>
        </button>

        <!-- 矩形工具 -->
        <button
          class="tool-btn"
          :class="{ active: editState.currentTool === 'rectangle' }"
          title="矩形"
          @click="setCurrentTool('rectangle')"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <rect
              x="2"
              y="4"
              width="12"
              height="8"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
            />
          </svg>
        </button>

        <!-- 箭头工具 -->
        <button
          class="tool-btn"
          :class="{ active: editState.currentTool === 'arrow' }"
          title="箭头"
          @click="setCurrentTool('arrow')"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path
              d="M2 8h10m-4-4l4 4-4 4"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>

        <!-- 文本工具 -->
        <button
          class="tool-btn"
          :class="{ active: editState.currentTool === 'text' }"
          title="文本"
          @click="setCurrentTool('text')"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path
              d="M4 2h8M8 2v12M6 14h4"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
            />
          </svg>
        </button>

        <!-- 马赛克工具 -->
        <button
          class="tool-btn"
          :class="{ active: editState.currentTool === 'mosaic' }"
          title="马赛克"
          @click="setCurrentTool('mosaic')"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <rect x="2" y="2" width="3" height="3" fill="currentColor" />
            <rect
              x="6"
              y="2"
              width="3"
              height="3"
              fill="currentColor"
              opacity="0.6"
            />
            <rect x="11" y="2" width="3" height="3" fill="currentColor" />
            <rect
              x="2"
              y="6"
              width="3"
              height="3"
              fill="currentColor"
              opacity="0.6"
            />
            <rect x="6" y="6" width="3" height="3" fill="currentColor" />
            <rect
              x="11"
              y="6"
              width="3"
              height="3"
              fill="currentColor"
              opacity="0.6"
            />
            <rect x="2" y="11" width="3" height="3" fill="currentColor" />
            <rect
              x="6"
              y="11"
              width="3"
              height="3"
              fill="currentColor"
              opacity="0.6"
            />
            <rect x="11" y="11" width="3" height="3" fill="currentColor" />
          </svg>
        </button>
      </div>

      <!-- 分隔线 -->
      <div class="toolbar-divider" />

      <!-- 撤销重做 -->
      <div class="undo-section">
        <button
          class="tool-btn"
          :disabled="!canUndo"
          title="撤销"
          @click="undo"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path
              d="M3 8a5 5 0 0 1 5-5h4M3 8l3-3M3 8l3 3"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>

        <button
          class="tool-btn"
          :disabled="!canRedo"
          title="重做"
          @click="redo"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path
              d="M13 8a5 5 0 0 0-5-5H4M13 8l-3-3M13 8l-3 3"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>

      <!-- 分隔线 -->
      <div class="toolbar-divider" />

      <!-- 下载和取消 -->
      <div class="action-section">
        <button
          class="tool-btn download-btn"
          title="下载"
          @click="saveScreenShot"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path
              d="M8 2v8M5 7l3 3 3-3M2 12h12"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>

        <button
          class="tool-btn cancel-btn"
          title="取消"
          @click="cancelScreenShot"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path
              d="M12 4L4 12M4 4l8 8"
              fill="none"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- 加载提示 -->
    <div v-if="isCapturing" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner" />
        <div class="loading-text">正在截图...</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type {
  SelectionArea,
  DragState,
  EditState,
  EditTool,
  ImageFormat
} from './types'

/**
 * 组件属性定义
 */
interface Props {
  /** 是否激活截图模式 */
  isActive: boolean
  /** 是否正在截图 */
  isCapturing: boolean
  /** 选区信息 */
  selection: SelectionArea
  /** 拖拽状态 */
  dragState: DragState
  /** 编辑状态 */
  editState: EditState
  /** 选中的图片格式 */
  selectedFormat: ImageFormat
}

/**
 * 组件事件定义
 */
interface Emits {
  /** 鼠标按下事件 */
  (e: 'mousedown', event: MouseEvent): void
  /** 取消截图 */
  (e: 'cancel'): void
  /** 保存截图 */
  (e: 'save'): void
  /** 设置当前工具 */
  (e: 'setTool', tool: EditTool): void
  /** 撤销操作 */
  (e: 'undo'): void
  /** 重做操作 */
  (e: 'redo'): void
}

// 定义属性和事件
const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 编辑Canvas引用
const editCanvasRef = ref<HTMLCanvasElement>()

/**
 * 选区样式计算
 */
const selectionStyle = computed(() => ({
  left: `${props.selection.x}px`,
  top: `${props.selection.y}px`,
  width: `${props.selection.width}px`,
  height: `${props.selection.height}px`
}))

/**
 * 工具栏位置计算
 */
const toolbarStyle = computed(() => {
  const toolbarHeight = 50
  const windowHeight = window.innerHeight

  // 默认显示在底部
  let bottom = 20

  // 如果选区太靠下，工具栏可能被遮挡，向上调整
  if (
    props.selection.y + props.selection.height + toolbarHeight + 40 >
    windowHeight
  ) {
    bottom = windowHeight - (props.selection.y - toolbarHeight - 20)
  }

  return {
    bottom: `${Math.max(20, bottom)}px`
  }
})

/**
 * 是否可以撤销
 */
const canUndo = computed(() => props.editState.historyIndex >= 0)

/**
 * 是否可以重做
 */
const canRedo = computed(
  () => props.editState.historyIndex < props.editState.history.length - 1
)

/**
 * 处理鼠标按下事件
 */
const handleMouseDown = (event: MouseEvent): void => {
  emit('mousedown', event)
}

/**
 * 处理编辑Canvas鼠标按下事件
 */
const handleEditCanvasMouseDown = (event: MouseEvent): void => {
  // 阻止事件冒泡，避免触发选区的鼠标事件
  event.stopPropagation()
  emit('mousedown', event)
}

/**
 * 取消截图
 */
const cancelScreenShot = (): void => {
  emit('cancel')
}

/**
 * 保存截图
 */
const saveScreenShot = (): void => {
  emit('save')
}

/**
 * 设置当前工具
 */
const setCurrentTool = (tool: EditTool): void => {
  emit('setTool', tool)
}

/**
 * 撤销操作
 */
const undo = (): void => {
  emit('undo')
}

/**
 * 重做操作
 */
const redo = (): void => {
  emit('redo')
}
</script>

<style scoped lang="scss">
.screenshot-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  cursor: crosshair;
}

.mask-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.mask-area {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.5);
  pointer-events: none;
}

.mask-top {
  top: 0;
  left: 0;
  width: 100%;
}

.mask-row {
  position: absolute;
  left: 0;
  width: 100%;
  display: flex;
}

.mask-left {
  height: 100%;
}

.mask-right {
  position: absolute;
  height: 100%;
}

.mask-bottom {
  left: 0;
  width: 100%;
}

.selection-area {
  position: absolute;
  border: 2px solid #409eff;
  background: transparent;
  cursor: move;

  .selection-info {
    position: absolute;
    top: -30px;
    left: 0;
    background-color: rgba(0, 0, 0, 0.8);
    color: #ffffff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    pointer-events: none;
  }
}

.resize-handles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;

  .resize-handle {
    position: absolute;
    width: 8px;
    height: 8px;
    background-color: #409eff;
    border: 1px solid #ffffff;
    border-radius: 1px;
    pointer-events: all;

    &.top-left {
      top: -4px;
      left: -4px;
      cursor: nw-resize;
    }

    &.top-center {
      top: -4px;
      left: 50%;
      transform: translateX(-50%);
      cursor: n-resize;
    }

    &.top-right {
      top: -4px;
      right: -4px;
      cursor: ne-resize;
    }

    &.middle-left {
      top: 50%;
      left: -4px;
      transform: translateY(-50%);
      cursor: w-resize;
    }

    &.middle-right {
      top: 50%;
      right: -4px;
      transform: translateY(-50%);
      cursor: e-resize;
    }

    &.bottom-left {
      bottom: -4px;
      left: -4px;
      cursor: sw-resize;
    }

    &.bottom-center {
      bottom: -4px;
      left: 50%;
      transform: translateX(-50%);
      cursor: s-resize;
    }

    &.bottom-right {
      bottom: -4px;
      right: -4px;
      cursor: se-resize;
    }
  }
}

.edit-canvas {
  position: absolute;
  top: 0;
  left: 0;
  cursor: crosshair;
  pointer-events: all;
}

.bottom-toolbar {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.9);
  border-radius: 8px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.tool-section,
.undo-section,
.action-section {
  display: flex;
  align-items: center;
  gap: 4px;
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background-color: rgba(255, 255, 255, 0.2);
  margin: 0 4px;
}

.tool-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background-color: transparent;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &.active {
    background-color: #409eff;
    color: #ffffff;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      background-color: transparent;
    }
  }

  &.download-btn {
    background-color: #67c23a;

    &:hover {
      background-color: #85ce61;
    }
  }

  &.cancel-btn {
    background-color: #f56c6c;

    &:hover {
      background-color: #f78989;
    }
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: #ffffff;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 16px;
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
