<template>
  <!-- 截图覆盖层 -->
  <div
    v-if="isActive"
    class="screenshot-overlay"
    @mousedown="handleMouseDown"
  >
    <!-- 遮罩层 -->
    <div class="mask-layer">
      <!-- 上方遮罩 -->
      <div
        class="mask-area mask-top"
        :style="{
          height: `${selection.y}px`
        }"
      />
      
      <!-- 中间行 -->
      <div
        class="mask-row"
        :style="{
          top: `${selection.y}px`,
          height: `${selection.height}px`
        }"
      >
        <!-- 左侧遮罩 -->
        <div
          class="mask-area mask-left"
          :style="{
            width: `${selection.x}px`
          }"
        />
        
        <!-- 选区 -->
        <div
          class="selection-area"
          :style="selectionStyle"
        >
          <!-- 选区尺寸显示 -->
          <div class="selection-info">
            {{ selection.width }} × {{ selection.height }}
          </div>
          
          <!-- 调整大小手柄 -->
          <div
            v-if="editState.currentTool === 'select'"
            class="resize-handles"
          >
            <!-- 8个调整手柄 -->
            <div class="resize-handle top-left" />
            <div class="resize-handle top-center" />
            <div class="resize-handle top-right" />
            <div class="resize-handle middle-left" />
            <div class="resize-handle middle-right" />
            <div class="resize-handle bottom-left" />
            <div class="resize-handle bottom-center" />
            <div class="resize-handle bottom-right" />
          </div>
          
          <!-- 编辑Canvas覆盖层 -->
          <canvas
            v-if="editState.currentTool !== 'select'"
            ref="editCanvasRef"
            class="edit-canvas"
            :width="selection.width"
            :height="selection.height"
            @mousedown.stop="handleEditCanvasMouseDown"
          />
        </div>
        
        <!-- 右侧遮罩 -->
        <div
          class="mask-area mask-right"
          :style="{
            left: `${selection.x + selection.width}px`,
            width: `calc(100vw - ${selection.x + selection.width}px)`
          }"
        />
      </div>
      
      <!-- 下方遮罩 -->
      <div
        class="mask-area mask-bottom"
        :style="{
          top: `${selection.y + selection.height}px`,
          height: `calc(100vh - ${selection.y + selection.height}px)`
        }"
      />
    </div>

    <!-- 底部工具栏 -->
    <div class="bottom-toolbar" :style="toolbarStyle">
      <!-- 工具选择区 -->
      <div class="tool-section">
        <!-- 选择工具 -->
        <button
          class="tool-btn"
          :class="{ active: editState.currentTool === 'select' }"
          @click="setCurrentTool('select')"
          title="选择"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <rect x="2" y="2" width="12" height="12" fill="none" stroke="currentColor" stroke-width="1.5" stroke-dasharray="2,2"/>
          </svg>
        </button>
        
        <!-- 画笔工具 -->
        <button
          class="tool-btn"
          :class="{ active: editState.currentTool === 'brush' }"
          @click="setCurrentTool('brush')"
          title="画笔"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path d="M2 14l4-4 8-8-2-2-8 8-4 4 2 2z" fill="currentColor"/>
          </svg>
        </button>
        
        <!-- 矩形工具 -->
        <button
          class="tool-btn"
          :class="{ active: editState.currentTool === 'rectangle' }"
          @click="setCurrentTool('rectangle')"
          title="矩形"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <rect x="2" y="4" width="12" height="8" fill="none" stroke="currentColor" stroke-width="1.5"/>
          </svg>
        </button>
        
        <!-- 箭头工具 -->
        <button
          class="tool-btn"
          :class="{ active: editState.currentTool === 'arrow' }"
          @click="setCurrentTool('arrow')"
          title="箭头"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path d="M2 8h10m-4-4l4 4-4 4" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
        
        <!-- 文本工具 -->
        <button
          class="tool-btn"
          :class="{ active: editState.currentTool === 'text' }"
          @click="setCurrentTool('text')"
          title="文本"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path d="M4 2h8M8 2v12M6 14h4" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
          </svg>
        </button>
        
        <!-- 马赛克工具 -->
        <button
          class="tool-btn"
          :class="{ active: editState.currentTool === 'mosaic' }"
          @click="setCurrentTool('mosaic')"
          title="马赛克"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <rect x="2" y="2" width="3" height="3" fill="currentColor"/>
            <rect x="6" y="2" width="3" height="3" fill="currentColor" opacity="0.6"/>
            <rect x="11" y="2" width="3" height="3" fill="currentColor"/>
            <rect x="2" y="6" width="3" height="3" fill="currentColor" opacity="0.6"/>
            <rect x="6" y="6" width="3" height="3" fill="currentColor"/>
            <rect x="11" y="6" width="3" height="3" fill="currentColor" opacity="0.6"/>
            <rect x="2" y="11" width="3" height="3" fill="currentColor"/>
            <rect x="6" y="11" width="3" height="3" fill="currentColor" opacity="0.6"/>
            <rect x="11" y="11" width="3" height="3" fill="currentColor"/>
          </svg>
        </button>
      </div>
      
      <!-- 分隔线 -->
      <div class="toolbar-divider" />
      
      <!-- 撤销重做 -->
      <div class="undo-section">
        <button
          class="tool-btn"
          :disabled="!canUndo"
          @click="undo"
          title="撤销"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path d="M3 8a5 5 0 0 1 5-5h4M3 8l3-3M3 8l3 3" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
        
        <button
          class="tool-btn"
          :disabled="!canRedo"
          @click="redo"
          title="重做"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path d="M13 8a5 5 0 0 0-5-5H4M13 8l-3-3M13 8l-3 3" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
      
      <!-- 分隔线 -->
      <div class="toolbar-divider" />
      
      <!-- 下载和取消 -->
      <div class="action-section">
        <button
          class="tool-btn download-btn"
          @click="saveScreenShot"
          title="下载"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path d="M8 2v8M5 7l3 3 3-3M2 12h12" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
        
        <button
          class="tool-btn cancel-btn"
          @click="cancelScreenShot"
          title="取消"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path d="M12 4L4 12M4 4l8 8" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 加载提示 -->
    <div v-if="isCapturing" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner" />
        <div class="loading-text">正在截图...</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import type { 
  SelectionArea, 
  DragState, 
  EditState, 
  EditTool,
  ImageFormat 
} from './types';

/**
 * 组件属性定义
 */
interface Props {
  /** 是否激活截图模式 */
  isActive: boolean;
  /** 是否正在截图 */
  isCapturing: boolean;
  /** 选区信息 */
  selection: SelectionArea;
  /** 拖拽状态 */
  dragState: DragState;
  /** 编辑状态 */
  editState: EditState;
  /** 选中的图片格式 */
  selectedFormat: ImageFormat;
}

/**
 * 组件事件定义
 */
interface Emits {
  /** 鼠标按下事件 */
  mousedown: [event: MouseEvent];
  /** 取消截图 */
  cancel: [];
  /** 保存截图 */
  save: [];
  /** 设置当前工具 */
  setTool: [tool: EditTool];
  /** 撤销操作 */
  undo: [];
  /** 重做操作 */
  redo: [];
}

// 定义属性和事件
const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 编辑Canvas引用
const editCanvasRef = ref<HTMLCanvasElement>();

/**
 * 选区样式计算
 */
const selectionStyle = computed(() => ({
  left: `${props.selection.x}px`,
  top: `${props.selection.y}px`,
  width: `${props.selection.width}px`,
  height: `${props.selection.height}px`
}));

/**
 * 工具栏位置计算
 */
const toolbarStyle = computed(() => {
  const toolbarHeight = 50;
  const windowHeight = window.innerHeight;
  
  // 默认显示在底部
  let bottom = 20;
  
  // 如果选区太靠下，工具栏可能被遮挡，向上调整
  if (props.selection.y + props.selection.height + toolbarHeight + 40 > windowHeight) {
    bottom = windowHeight - (props.selection.y - toolbarHeight - 20);
  }
  
  return {
    bottom: `${Math.max(20, bottom)}px`
  };
});

/**
 * 是否可以撤销
 */
const canUndo = computed(() => props.editState.historyIndex >= 0);

/**
 * 是否可以重做
 */
const canRedo = computed(() => 
  props.editState.historyIndex < props.editState.history.length - 1
);

/**
 * 处理鼠标按下事件
 */
const handleMouseDown = (event: MouseEvent): void => {
  emit('mousedown', event);
};

/**
 * 处理编辑Canvas鼠标按下事件
 */
const handleEditCanvasMouseDown = (event: MouseEvent): void => {
  // 阻止事件冒泡，避免触发选区的鼠标事件
  event.stopPropagation();
  emit('mousedown', event);
};

/**
 * 取消截图
 */
const cancelScreenShot = (): void => {
  emit('cancel');
};

/**
 * 保存截图
 */
const saveScreenShot = (): void => {
  emit('save');
};

/**
 * 设置当前工具
 */
const setCurrentTool = (tool: EditTool): void => {
  emit('setTool', tool);
};

/**
 * 撤销操作
 */
const undo = (): void => {
  emit('undo');
};

/**
 * 重做操作
 */
const redo = (): void => {
  emit('redo');
};
</script>
