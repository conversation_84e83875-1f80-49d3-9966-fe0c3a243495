/**
 * 截图工具配置示例
 * 展示不同场景下的配置选项
 */

import type { ScreenshotOptions } from "./types";

/**
 * 示例1: 仅显示格式选择、确认、取消、保存的简化工具栏
 * 适用于简单的截图需求，不需要绘制功能
 */
export const simpleToolbarConfig: ScreenshotOptions = {
  format: "png",
  toolbarPosition: "center",
  toolbarConfig: {
    showToolbar: true,
    availableTools: [], // 不显示任何绘制工具
    showFormatSelector: true, // 显示格式选择器
  },
  completeCallback: (data) => {
    console.log('简化工具栏截图完成:', data.base64);
  }
};

/**
 * 示例2: 完整功能工具栏
 * 包含所有绘制工具和操作按钮
 */
export const fullToolbarConfig: ScreenshotOptions = {
  format: "png",
  toolbarPosition: "center",
  toolbarConfig: {
    showToolbar: true,
    availableTools: ["rectangle", "circle", "arrow", "brush", "mosaic", "text"],
    showFormatSelector: true,
  },
  historyConfig: {
    maxHistorySize: 10,
  },
  completeCallback: (data) => {
    console.log('完整工具栏截图完成:', data.base64);
  }
};

/**
 * 示例3: 仅绘制工具，不显示格式选择器
 * 适用于固定格式的绘制需求
 */
export const drawingOnlyConfig: ScreenshotOptions = {
  format: "jpg",
  toolbarPosition: "center",
  toolbarConfig: {
    showToolbar: true,
    availableTools: ["rectangle", "circle", "arrow", "brush"],
    showFormatSelector: false, // 不显示格式选择器
  },
  completeCallback: (data) => {
    console.log('绘制工具截图完成:', data.base64);
  }
};

/**
 * 示例4: 隐藏工具栏
 * 仅提供基本的截图功能，通过键盘快捷键操作
 */
export const noToolbarConfig: ScreenshotOptions = {
  format: "webp",
  toolbarPosition: "center",
  toolbarConfig: {
    showToolbar: false, // 隐藏工具栏
    availableTools: [],
    showFormatSelector: false,
  },
  completeCallback: (data) => {
    console.log('无工具栏截图完成:', data.base64);
  }
};

/**
 * 示例5: 自定义工具栏位置和工具
 * 左对齐工具栏，仅包含常用绘制工具
 */
export const customPositionConfig: ScreenshotOptions = {
  format: "png",
  toolbarPosition: "left",
  toolbarConfig: {
    showToolbar: true,
    availableTools: ["rectangle", "arrow", "text"], // 仅包含常用工具
    showFormatSelector: true,
  },
  historyConfig: {
    maxHistorySize: 5, // 减少历史记录数量
  },
  completeCallback: (data) => {
    console.log('自定义位置截图完成:', data.base64);
  }
};

/**
 * 使用示例：
 * 
 * ```typescript
 * import { useScreenshot } from './composables/screenshot';
 * import { simpleToolbarConfig } from './composables/screenshot/config-examples';
 * 
 * // 使用简化工具栏配置
 * const screenshot = useScreenshot(simpleToolbarConfig);
 * 
 * // 开始截图
 * screenshot.initScreenshot();
 * ```
 */
