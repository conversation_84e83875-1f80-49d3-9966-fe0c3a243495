import { ref, type Ref } from 'vue'
import type {
  CutInfo,
  Point,
  ResizeDirection,
  ResizeHandle,
  DrawTool
} from './types'
import { defaultConfig, RESIZE_HANDLE_SIZE, MIN_SELECTION_SIZE } from './config'
import { clamp } from './utils'

/**
 * 选框管理功能
 */
export function useSelection(
  screenshotContainer: Ref<HTMLElement | null>,
  canvas: Ref<HTMLCanvasElement | null>,
  currentTool: Ref<DrawTool>,
  onSelectionChange?: (cutInfo: CutInfo) => void
) {
  // 选框状态
  const cutInfo = ref<CutInfo | null>(null)

  // 拖拽状态
  let isDragging = false
  let isResizing = false
  let dragStartPoint: Point = { x: 0, y: 0 }
  let resizeDirection = ''
  let originalCutInfo: CutInfo | null = null
  let dragOffset: Point = { x: 0, y: 0 }

  /**
   * 创建选择框
   */
  const createSelectionBox = (info: CutInfo) => {
    if (!screenshotContainer.value) return

    // 移除旧的选择框
    clearSelection()

    // 创建选择框
    const selectionBox = document.createElement('div')
    selectionBox.className = 'selection-box'
    selectionBox.style.cssText = `
      position: absolute;
      left: ${info.startX}px;
      top: ${info.startY}px;
      width: ${info.width}px;
      height: ${info.height}px;
      border: 2px solid ${defaultConfig.cutBoxBdColor};
      background: transparent;
      pointer-events: none;
      box-sizing: border-box;
      cursor: move;
    `

    screenshotContainer.value.appendChild(selectionBox)

    // 创建遮罩层
    createSelectionMask(info)

    // 创建调整点
    createResizeHandles(info)

    // 更新状态
    cutInfo.value = info

    // 通知选择框变化
    if (onSelectionChange) {
      onSelectionChange(info)
    }
  }

  /**
   * 创建选择区域遮罩（微信截图效果）
   */
  const createSelectionMask = (info: CutInfo) => {
    if (!screenshotContainer.value) return

    // 移除旧的遮罩层
    const oldMask = screenshotContainer.value.querySelector('.selection-mask')
    if (oldMask) oldMask.remove()

    // 创建新的遮罩层
    const mask = document.createElement('div')
    mask.className = 'selection-mask'
    mask.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, ${defaultConfig.maskOpacity});
      pointer-events: none;
      z-index: 1;
    `

    // 使用CSS clip-path创建镂空效果（微信截图效果）
    const clipPath = `polygon(
      0% 0%, 
      0% 100%, 
      ${info.startX}px 100%, 
      ${info.startX}px ${info.startY}px, 
      ${info.startX + info.width}px ${info.startY}px, 
      ${info.startX + info.width}px ${info.startY + info.height}px, 
      ${info.startX}px ${info.startY + info.height}px, 
      ${info.startX}px 100%, 
      100% 100%, 
      100% 0%
    )`

    mask.style.clipPath = clipPath
    screenshotContainer.value.appendChild(mask)
  }

  /**
   * 创建八个调整点
   */
  const createResizeHandles = (info: CutInfo) => {
    if (!screenshotContainer.value) return

    const handles: ResizeHandle[] = [
      {
        position: 'nw',
        x: info.startX - RESIZE_HANDLE_SIZE / 2,
        y: info.startY - RESIZE_HANDLE_SIZE / 2,
        cursor: 'nwse-resize' // 双向箭头
      },
      {
        position: 'n',
        x: info.startX + info.width / 2 - RESIZE_HANDLE_SIZE / 2,
        y: info.startY - RESIZE_HANDLE_SIZE / 2,
        cursor: 'ns-resize' // 双向箭头
      },
      {
        position: 'ne',
        x: info.startX + info.width - RESIZE_HANDLE_SIZE / 2,
        y: info.startY - RESIZE_HANDLE_SIZE / 2,
        cursor: 'nesw-resize' // 双向箭头
      },
      {
        position: 'w',
        x: info.startX - RESIZE_HANDLE_SIZE / 2,
        y: info.startY + info.height / 2 - RESIZE_HANDLE_SIZE / 2,
        cursor: 'ew-resize' // 双向箭头
      },
      {
        position: 'e',
        x: info.startX + info.width - RESIZE_HANDLE_SIZE / 2,
        y: info.startY + info.height / 2 - RESIZE_HANDLE_SIZE / 2,
        cursor: 'ew-resize' // 双向箭头
      },
      {
        position: 'sw',
        x: info.startX - RESIZE_HANDLE_SIZE / 2,
        y: info.startY + info.height - RESIZE_HANDLE_SIZE / 2,
        cursor: 'nesw-resize' // 双向箭头
      },
      {
        position: 's',
        x: info.startX + info.width / 2 - RESIZE_HANDLE_SIZE / 2,
        y: info.startY + info.height - RESIZE_HANDLE_SIZE / 2,
        cursor: 'ns-resize' // 双向箭头
      },
      {
        position: 'se',
        x: info.startX + info.width - RESIZE_HANDLE_SIZE / 2,
        y: info.startY + info.height - RESIZE_HANDLE_SIZE / 2,
        cursor: 'nwse-resize' // 双向箭头
      }
    ]

    handles.forEach(handle => {
      const handleElement = document.createElement('div')
      handleElement.className = `resize-handle resize-handle-${handle.position}`
      handleElement.style.cssText = `
        position: absolute;
        left: ${handle.x}px;
        top: ${handle.y}px;
        width: ${RESIZE_HANDLE_SIZE}px;
        height: ${RESIZE_HANDLE_SIZE}px;
        background: ${defaultConfig.cutBoxBdColor};
        border: 1px solid #fff;
        cursor: ${handle.cursor};
        z-index: 10;
        box-sizing: border-box;
        border-radius: 1px;
      `

      // 添加鼠标事件
      handleElement.addEventListener('mousedown', e =>
        startResize(e, handle.position)
      )

      if (screenshotContainer.value) {
        screenshotContainer.value.appendChild(handleElement)
      }
    })
  }

  /**
   * 更新选择框显示
   */
  const updateSelection = (info: CutInfo) => {
    if (!screenshotContainer.value) return

    // 更新选择框
    const selectionBox = screenshotContainer.value.querySelector(
      '.selection-box'
    ) as HTMLElement
    if (selectionBox) {
      selectionBox.style.left = `${info.startX}px`
      selectionBox.style.top = `${info.startY}px`
      selectionBox.style.width = `${info.width}px`
      selectionBox.style.height = `${info.height}px`
    }

    // 更新遮罩
    createSelectionMask(info)

    // 更新调整点位置
    updateResizeHandles(info)

    // 更新状态
    cutInfo.value = info

    // 通知选择框变化
    if (onSelectionChange) {
      onSelectionChange(info)
    }
  }

  /**
   * 更新调整点位置
   */
  const updateResizeHandles = (info: CutInfo) => {
    if (!screenshotContainer.value) return

    const positions = [
      {
        selector: '.resize-handle-nw',
        x: info.startX - RESIZE_HANDLE_SIZE / 2,
        y: info.startY - RESIZE_HANDLE_SIZE / 2
      },
      {
        selector: '.resize-handle-n',
        x: info.startX + info.width / 2 - RESIZE_HANDLE_SIZE / 2,
        y: info.startY - RESIZE_HANDLE_SIZE / 2
      },
      {
        selector: '.resize-handle-ne',
        x: info.startX + info.width - RESIZE_HANDLE_SIZE / 2,
        y: info.startY - RESIZE_HANDLE_SIZE / 2
      },
      {
        selector: '.resize-handle-w',
        x: info.startX - RESIZE_HANDLE_SIZE / 2,
        y: info.startY + info.height / 2 - RESIZE_HANDLE_SIZE / 2
      },
      {
        selector: '.resize-handle-e',
        x: info.startX + info.width - RESIZE_HANDLE_SIZE / 2,
        y: info.startY + info.height / 2 - RESIZE_HANDLE_SIZE / 2
      },
      {
        selector: '.resize-handle-sw',
        x: info.startX - RESIZE_HANDLE_SIZE / 2,
        y: info.startY + info.height - RESIZE_HANDLE_SIZE / 2
      },
      {
        selector: '.resize-handle-s',
        x: info.startX + info.width / 2 - RESIZE_HANDLE_SIZE / 2,
        y: info.startY + info.height - RESIZE_HANDLE_SIZE / 2
      },
      {
        selector: '.resize-handle-se',
        x: info.startX + info.width - RESIZE_HANDLE_SIZE / 2,
        y: info.startY + info.height - RESIZE_HANDLE_SIZE / 2
      }
    ]

    positions.forEach(pos => {
      if (!screenshotContainer.value) return
      const handle = screenshotContainer.value.querySelector(
        pos.selector
      ) as HTMLElement
      if (handle) {
        handle.style.left = `${pos.x}px`
        handle.style.top = `${pos.y}px`
      }
    })
  }

  /**
   * 清除选择区域
   */
  const clearSelection = () => {
    if (!screenshotContainer.value) return

    // 清除选择框
    const selectionBox =
      screenshotContainer.value.querySelector('.selection-box')
    if (selectionBox) selectionBox.remove()

    // 清除调整点
    const resizeHandles =
      screenshotContainer.value.querySelectorAll('.resize-handle')
    resizeHandles.forEach(handle => handle.remove())

    // 清除遮罩层
    const selectionMask =
      screenshotContainer.value.querySelector('.selection-mask')
    if (selectionMask) selectionMask.remove()

    // 重置状态
    cutInfo.value = null
  }

  /**
   * 开始调整大小
   */
  const startResize = (e: MouseEvent, direction: ResizeDirection) => {
    e.stopPropagation()
    e.preventDefault()

    if (!cutInfo.value || !canvas.value) return

    isResizing = true
    resizeDirection = direction

    const rect = canvas.value.getBoundingClientRect()
    dragStartPoint = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    }

    // 保存当前的选择区域信息
    originalCutInfo = { ...cutInfo.value }

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleResize)
    document.addEventListener('mouseup', stopResize)
  }

  /**
   * 处理调整大小
   */
  const handleResize = (e: MouseEvent) => {
    if (!isResizing || !originalCutInfo || !canvas.value) return

    const rect = canvas.value.getBoundingClientRect()
    const currentX = e.clientX - rect.left
    const currentY = e.clientY - rect.top

    const deltaX = currentX - dragStartPoint.x
    const deltaY = currentY - dragStartPoint.y

    // 根据调整方向计算新的选择区域
    const newCutInfo = calculateNewCutInfo(
      originalCutInfo,
      deltaX,
      deltaY,
      resizeDirection
    )

    // 确保最小尺寸
    if (
      newCutInfo.width < MIN_SELECTION_SIZE ||
      newCutInfo.height < MIN_SELECTION_SIZE
    )
      return

    // 更新选择区域
    updateSelection(newCutInfo)
  }

  /**
   * 停止调整大小
   */
  const stopResize = () => {
    isResizing = false
    resizeDirection = ''
    originalCutInfo = null

    // 移除全局事件监听
    document.removeEventListener('mousemove', handleResize)
    document.removeEventListener('mouseup', stopResize)
  }

  /**
   * 根据调整方向计算新的选择区域
   */
  const calculateNewCutInfo = (
    original: CutInfo,
    deltaX: number,
    deltaY: number,
    direction: string
  ): CutInfo => {
    let newStartX = original.startX
    let newStartY = original.startY
    let newWidth = original.width
    let newHeight = original.height

    switch (direction) {
      case 'nw': // 左上
        newStartX = original.startX + deltaX
        newStartY = original.startY + deltaY
        newWidth = original.width - deltaX
        newHeight = original.height - deltaY
        break
      case 'n': // 上中
        newStartY = original.startY + deltaY
        newHeight = original.height - deltaY
        break
      case 'ne': // 右上
        newStartY = original.startY + deltaY
        newWidth = original.width + deltaX
        newHeight = original.height - deltaY
        break
      case 'w': // 左中
        newStartX = original.startX + deltaX
        newWidth = original.width - deltaX
        break
      case 'e': // 右中
        newWidth = original.width + deltaX
        break
      case 'sw': // 左下
        newStartX = original.startX + deltaX
        newWidth = original.width - deltaX
        newHeight = original.height + deltaY
        break
      case 's': // 下中
        newHeight = original.height + deltaY
        break
      case 'se': // 右下
        newWidth = original.width + deltaX
        newHeight = original.height + deltaY
        break
    }

    return {
      startX: newStartX,
      startY: newStartY,
      width: newWidth,
      height: newHeight
    }
  }

  /**
   * 开始拖动选择框
   */
  const startDrag = (e: MouseEvent) => {
    if (!cutInfo.value || !canvas.value) {
      return
    }
    console.log('开始拖动选框:', cutInfo.value, canvas.value)

    const rect = canvas.value.getBoundingClientRect()
    const currentPoint = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    }

    isDragging = true
    dragStartPoint = currentPoint

    // 计算鼠标相对于选择框左上角的偏移
    dragOffset = {
      x: currentPoint.x - cutInfo.value.startX,
      y: currentPoint.y - cutInfo.value.startY
    }

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleDrag)
    document.addEventListener('mouseup', stopDrag)
  }

  /**
   * 处理拖动
   */
  const handleDrag = (e: MouseEvent) => {
    if (!isDragging || !cutInfo.value || !canvas.value) return
    console.log('处理拖动:', e)

    const rect = canvas.value.getBoundingClientRect()
    const currentPoint = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    }

    // 计算新的位置（保持偏移量）
    const newStartX = currentPoint.x - dragOffset.x
    const newStartY = currentPoint.y - dragOffset.y

    // 限制在画布范围内
    const maxX = canvas.value.width - cutInfo.value.width
    const maxY = canvas.value.height - cutInfo.value.height

    const clampedX = clamp(newStartX, 0, maxX)
    const clampedY = clamp(newStartY, 0, maxY)

    // 更新选择区域
    const newCutInfo: CutInfo = {
      startX: clampedX,
      startY: clampedY,
      width: cutInfo.value.width,
      height: cutInfo.value.height
    }
    console.log('更新选择区域:', newCutInfo)
    updateSelection(newCutInfo)
  }

  /**
   * 停止拖动
   */
  const stopDrag = () => {
    isDragging = false

    // 移除全局事件监听
    document.removeEventListener('mousemove', handleDrag)
    document.removeEventListener('mouseup', stopDrag)
  }

  return {
    cutInfo,
    createSelectionBox,
    updateSelection,
    clearSelection,
    startDrag,
    isDragging: () => isDragging,
    isResizing: () => isResizing
  }
}
