/**
 * Canvas 工具函数
 */
import { MIME_TYPES, BACKGROUND_COLORS } from './constants'
import type { ImageFormat } from './types'

/**
 * 创建Canvas元素
 * @param width 宽度
 * @param height 高度
 * @param styles 可选的CSS样式
 * @returns Canvas元素
 */
export function createCanvas(
  width: number,
  height: number,
  styles?: string
): HTMLCanvasElement {
  const canvas = document.createElement('canvas')
  canvas.width = width
  canvas.height = height

  if (styles) {
    canvas.style.cssText = styles
  }

  return canvas
}

/**
 * 创建临时Canvas用于图片处理
 * @param width 宽度
 * @param height 高度
 * @returns Canvas元素和2D上下文
 */
export function createTempCanvas(
  width: number,
  height: number
): {
  canvas: HTMLCanvasElement
  ctx: CanvasRenderingContext2D
} {
  const canvas = createCanvas(width, height)
  const ctx = canvas.getContext('2d')

  if (!ctx) {
    throw new Error('无法获取Canvas 2D上下文')
  }

  return { canvas, ctx }
}

/**
 * 从Canvas获取图片数据
 * @param canvas Canvas元素
 * @param format 图片格式
 * @param quality 图片质量 (0-1)
 * @returns Base64数据URL
 */
export function getCanvasDataURL(
  canvas: HTMLCanvasElement,
  format: ImageFormat,
  quality = 0.9
): string {
  const mimeType = getMimeType(format)

  if (format === 'jpg' || format === 'jpeg') {
    return canvas.toDataURL(mimeType, quality)
  }

  return canvas.toDataURL(mimeType)
}

/**
 * 从Canvas获取Blob数据
 * @param canvas Canvas元素
 * @param format 图片格式
 * @param quality 图片质量 (0-1)
 * @returns Promise<Blob>
 */
export function getCanvasBlob(
  canvas: HTMLCanvasElement,
  format: ImageFormat,
  quality = 0.9
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const mimeType = getMimeType(format)

    canvas.toBlob(
      blob => {
        if (blob) {
          resolve(blob)
        } else {
          reject(new Error('无法生成Blob数据'))
        }
      },
      mimeType,
      format === 'jpg' || format === 'jpeg' ? quality : undefined
    )
  })
}

/**
 * 获取MIME类型
 * @param format 图片格式
 * @returns MIME类型字符串
 */
export function getMimeType(format: ImageFormat): string {
  switch (format) {
    case 'png':
      return MIME_TYPES.PNG
    case 'jpg':
    case 'jpeg':
      return MIME_TYPES.JPEG
    case 'webp':
      return MIME_TYPES.WEBP
    case 'svg':
      return MIME_TYPES.SVG
    default:
      return MIME_TYPES.PNG
  }
}

/**
 * 裁剪Canvas图片
 * @param sourceCanvas 源Canvas
 * @param x 裁剪起始X坐标
 * @param y 裁剪起始Y坐标
 * @param width 裁剪宽度
 * @param height 裁剪高度
 * @returns 裁剪后的Canvas
 */
export function cropCanvas(
  sourceCanvas: HTMLCanvasElement,
  x: number,
  y: number,
  width: number,
  height: number
): HTMLCanvasElement {
  const { canvas: croppedCanvas, ctx } = createTempCanvas(width, height)

  const sourceCtx = sourceCanvas.getContext('2d')
  if (!sourceCtx) {
    throw new Error('无法获取源Canvas上下文')
  }

  // 获取源图片数据
  const imageData = sourceCtx.getImageData(x, y, width, height)

  // 绘制到新Canvas
  ctx.putImageData(imageData, 0, 0)

  return croppedCanvas
}

/**
 * 复制Canvas内容到另一个Canvas
 * @param sourceCanvas 源Canvas
 * @param targetCanvas 目标Canvas
 * @param sx 源起始X坐标
 * @param sy 源起始Y坐标
 * @param sWidth 源宽度
 * @param sHeight 源高度
 * @param dx 目标X坐标
 * @param dy 目标Y坐标
 * @param dWidth 目标宽度（可选）
 * @param dHeight 目标高度（可选）
 */
export function copyCanvasContent(
  sourceCanvas: HTMLCanvasElement,
  targetCanvas: HTMLCanvasElement,
  sx = 0,
  sy = 0,
  sWidth?: number,
  sHeight?: number,
  dx = 0,
  dy = 0,
  dWidth?: number,
  dHeight?: number
): void {
  const targetCtx = targetCanvas.getContext('2d')
  if (!targetCtx) {
    throw new Error('无法获取目标Canvas上下文')
  }

  const actualSWidth = sWidth ?? sourceCanvas.width
  const actualSHeight = sHeight ?? sourceCanvas.height
  const actualDWidth = dWidth ?? actualSWidth
  const actualDHeight = dHeight ?? actualSHeight

  targetCtx.drawImage(
    sourceCanvas,
    sx,
    sy,
    actualSWidth,
    actualSHeight,
    dx,
    dy,
    actualDWidth,
    actualDHeight
  )
}

/**
 * 清空Canvas
 * @param canvas Canvas元素
 * @param fillColor 填充颜色（可选）
 */
export function clearCanvas(
  canvas: HTMLCanvasElement,
  fillColor?: string
): void {
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  ctx.clearRect(0, 0, canvas.width, canvas.height)

  if (fillColor) {
    ctx.fillStyle = fillColor
    ctx.fillRect(0, 0, canvas.width, canvas.height)
  }
}

/**
 * 设置Canvas背景色
 * @param canvas Canvas元素
 * @param color 背景颜色
 */
export function setCanvasBackground(
  canvas: HTMLCanvasElement,
  color: string = BACKGROUND_COLORS.WHITE
): void {
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  ctx.save()
  ctx.fillStyle = color
  ctx.fillRect(0, 0, canvas.width, canvas.height)
  ctx.restore()
}
