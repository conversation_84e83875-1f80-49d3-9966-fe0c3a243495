/**
 * 截图工具常量定义
 */

// CSS 类名常量
export const CSS_CLASSES = {
  SCREENSHOT_CONTAINER: 'screenshot-container',
  SCREENSHOT_TOOLBAR: 'screenshot-toolbar',
  SELECTION_BOX: 'selection-box',
  RESIZE_HANDLE: 'resize-handle',
  ACTION_BUTTON: 'action-button',
  TOOL_BUTTON: 'tool-button'
} as const

// 光标样式常量
export const CURSOR_STYLES = {
  DEFAULT: 'default',
  CROSSHAIR: 'crosshair',
  NOT_ALLOWED: 'not-allowed',
  MOVE: 'move',
  NWSE_RESIZE: 'nwse-resize',
  NS_RESIZE: 'ns-resize',
  NESW_RESIZE: 'nesw-resize',
  EW_RESIZE: 'ew-resize'
} as const

// MIME 类型常量
export const MIME_TYPES = {
  PNG: 'image/png',
  JPG: 'image/jpeg',
  JPEG: 'image/jpeg',
  WEBP: 'image/webp',
  SVG: 'image/svg+xml'
} as const

// 绘制工具常量
export const DRAWING_TOOLS = [
  'rectangle',
  'circle',
  'arrow',
  'brush',
  'mosaic',
  'text'
] as const

// 控制台消息常量
export const CONSOLE_MESSAGES = {
  SCREENSHOT_START: '开始截图 - 捕获当前视口...',
  VIEWPORT_INFO: '视口信息: 滚动位置({0}, {1}), 尺寸({2}x{3})',
  SNAPSHOT_COMPLETE: '视口快照生成完成，准备创建canvas编辑界面...',
  CANVAS_DRAWN: '快照已绘制到canvas，准备选区编辑...',
  CANVAS_ADDED: 'Canvas编辑界面已添加到DOM，容器z-index: 9999',
  DEFAULT_SELECTION_CREATED: '默认选择框已创建:',
  DRAG_START_IN_SELECTION: '在选择框内点击，开始拖动',
  SCREENSHOT_COMPLETE: '截图完成并已复制到剪切板',
  SCREENSHOT_DOWNLOADED: '截图已下载: {0}',
  SCREENSHOT_SAVED_TO_CLIPBOARD: '截图已保存到剪切板',
  SCREENSHOT_CANCELLED: '截图操作已取消',
  ERROR_MISSING_ELEMENTS: '无法完成截图：缺少必要的元素',
  ERROR_CONFIRM_FAILED: '确认截图失败:',
  ERROR_DOWNLOAD_FAILED: '下载截图失败:',
  ERROR_COMPLETE_FAILED: '完成截图时出错:',
  TOAST_SHOW: '显示提示: {0}',
  CLIPBOARD_SUCCESS: '截图已保存到剪切板',
  CLIPBOARD_ERROR: '保存到剪切板失败'
} as const

// Z-Index 常量
export const Z_INDEX = {
  SCREENSHOT_CONTAINER: 9999,
  TOOLBAR: 10000,
  SELECTION_BOX: 10001,
  RESIZE_HANDLE: 10002
} as const

// 事件类型常量
export const EVENT_TYPES = {
  MOUSEDOWN: 'mousedown',
  MOUSEMOVE: 'mousemove',
  MOUSEUP: 'mouseup',
  CLICK: 'click',
  KEYDOWN: 'keydown'
} as const

// 键盘按键常量
export const KEYBOARD_KEYS = {
  ESCAPE: 'Escape',
  CTRL_Z: 'z'
} as const

// 默认样式常量
export const DEFAULT_STYLES = {
  CONTAINER: `
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.3);
    z-index: ${Z_INDEX.SCREENSHOT_CONTAINER};
    cursor: ${CURSOR_STYLES.CROSSHAIR};
  `,
  CANVAS: `
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 1px solid #ccc;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  `
} as const

// 文件名模板常量
export const FILENAME_TEMPLATE = 'screenshot-{timestamp}.{format}'

// 背景颜色常量
export const BACKGROUND_COLORS = {
  WHITE: '#ffffff',
  TRANSPARENT: 'transparent'
} as const
