<template>
  <div class="open-icon" @click="changeCollapsed"></div>
  <div
    class="preview-aside"
    :class="isCollapsed ? 'fadeInRight' : 'fadeOutRight'"
  >
    <div class="aside-header">
      <div class="title">
        <i class="collapsed" @click="changeCollapsed"></i>预报图
      </div>
      <div class="btns text-right">
        <qx-button @click="onSave">保存</qx-button>
        <qx-button class="primary" @click="onSubmit">提交</qx-button>
      </div>
    </div>

    <qx-no-data v-if="rightList.length === 0 && !isLoading" />
    <div v-loading="isLoading" class="aside-list">
      <div
        v-for="item in rightList"
        :key="item.id"
        class="aside-item"
        :class="item.id === current ? 'active' : ''"
        @click="nodeClick(item)"
      >
        <n-checkbox
          v-if="current === item.id"
          v-model:checked="isChecked"
          class="qx-checkbox"
        >
          &nbsp;
        </n-checkbox>
        <div class="img-wrap">
          <img :src="item.fileUrlColorful" alt="" />
        </div>

        <div class="descript">
          {{ item.name || '--' }}
          <span class="download-icon" @click.stop="download(item)">下载</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { QxButton } from 'src/components/QxButton'
import { ref, onMounted, inject, onBeforeUnmount } from 'vue'
import Api from 'src/requests/forecast'
import gisUtils from 'src/utils/gis'
import { useMessage } from 'naive-ui'
import eventBus from 'src/utils/eventBus'
import GeoJSON from 'ol/format/GeoJSON.js'
import moment from 'moment'
import Polygon from 'ol/geom/Polygon.js'
import 'ol-ext/render/Cspline.js'
const message = useMessage()
const getMap = inject('getMap')
const isCollapsed = ref(true)
const current = ref('')
const isChecked = ref(false)
const rightList = ref([])
const tempInfo = ref({})
let checkeditem = {}
const isLoading = ref(false)
const props = defineProps({
  currentProduct: {
    type: Object,
    default: () => {}
  }
})

//展开收起切换
function changeCollapsed() {
  isCollapsed.value = !isCollapsed.value
}

const emit = defineEmits(['click', 'save', 'submit'])

// 选中
function nodeClick(item) {
  isChecked.value = true

  if (checkeditem.id === item.id) {
    current.value = current.value ? '' : item.id
  } else {
    checkeditem = item
    current.value = item.id
  }
}
function download(item) {
  if (item.fileUrlColorful.startsWith('data:image')) {
    const base64String = item.fileUrlColorful.split(',')[1] // 去掉 data:image/jpeg;base64, 前缀
    const byteCharacters = atob(base64String)
    const byteNumbers = new Array(byteCharacters.length)
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i)
    }
    const byteArray = new Uint8Array(byteNumbers)
    const blob = new Blob([byteArray], { type: 'image/jpeg' })

    // 创建一个临时的 a 标签来触发下载
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = item.name + '.jpg' // 设置下载的文件名
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link) // 清理
  } else {
    const link = document.createElement('a')
    link.href = item.fileUrlColorful
    link.download = item.name + '.jpg' // 设置下载的文件名
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link) // 清理
  }
}
// 保存
function onSave() {
  debugger
  getMap(map => {
    let leftLon = tempInfo.value.leftLongitude
    let leftLat = tempInfo.value.leftLatitude
    let rightLon = tempInfo.value.rightLongitude
    let rightLat = tempInfo.value.rightLatitude
    const extent = [leftLon, rightLat, rightLon, leftLat]
    try {
      map.getView().fit(extent, { padding: [5, 650, 10, 30] })
    } catch (error) {
      console.error(error)
    }

    map.once('rendercomplete', function () {
      const leftTopPosition = map.getPixelFromCoordinate([leftLon, leftLat])
      // 地理坐标转换屏幕坐标
      const bottomRightPosition = map.getPixelFromCoordinate([
        rightLon,
        rightLat
      ])
      // 计算框选矩形的宽度以及高度像素
      const width = Math.abs(bottomRightPosition[0] - leftTopPosition[0])
      const height = Math.abs(bottomRightPosition[1] - leftTopPosition[1])
      // 计算框选矩形的左上角屏幕坐标,放置用户反过来绘制
      const minx =
        leftTopPosition[0] <= bottomRightPosition[0]
          ? leftTopPosition[0]
          : bottomRightPosition[0]
      const miny =
        leftTopPosition[1] <= bottomRightPosition[1]
          ? leftTopPosition[1]
          : bottomRightPosition[1]

      const mapCanvas = document.createElement('canvas')
      mapCanvas.width = width
      mapCanvas.height = height
      const mapContext = mapCanvas.getContext('2d')

      console.log(minx, miny, width, height, 'minx, miny, width, height')
      Array.prototype.forEach.call(
        map.getViewport().querySelectorAll('.ol-layer canvas, canvas.ol-layer'),
        function (canvas) {
          if (canvas.width > 0) {
            const opacity =
              canvas.parentNode.style.opacity || canvas.style.opacity
            mapContext.globalAlpha = opacity === '' ? 1 : Number(opacity)
            let matrix
            const transform = canvas.style.transform
            if (transform) {
              // Get the transform parameters from the style's transform matrix
              matrix = transform
                .match(/^matrix\(([^\(]*)\)$/)[1]
                .split(',')
                .map(Number)
            } else {
              matrix = [
                parseFloat(canvas.style.width) / canvas.width,
                0,
                0,
                parseFloat(canvas.style.height) / canvas.height,
                0,
                0
              ]
            }
            // Apply the transform to the export map context
            CanvasRenderingContext2D.prototype.setTransform.apply(
              mapContext,
              matrix
            )
            const backgroundColor = canvas.parentNode.style.backgroundColor
            if (backgroundColor) {
              mapContext.fillStyle = backgroundColor
              mapContext.fillRect(minx, miny, width, height)
            }
            mapContext.drawImage(canvas, -minx, -miny)
          }
        }
      )
      mapContext.globalAlpha = 1
      mapContext.setTransform(1, 0, 0, 1, 0, 0)
      // const link = document.getElementById('image-download')
      // link.href = mapCanvas.toDataURL()
      // link.click()
      const fileUrlColorful = mapCanvas.toDataURL('image/png')
      const ctx = mapCanvas.getContext('2d')
      const imgData = ctx.getImageData(0, 0, mapCanvas.width, mapCanvas.height)
      const data = imgData.data

      for (let i = 0; i < data.length; i += 4) {
        const r = data[i]
        const g = data[i + 1]
        const b = data[i + 2]

        // 计算灰度值
        const gray = 0.3 * r + 0.59 * g + 0.11 * b

        // 设置为灰度值
        data[i] = data[i + 1] = data[i + 2] = gray
      }

      ctx.putImageData(imgData, 0, 0)

      const href = mapCanvas.toDataURL('image/png') // base64格式数据
      // const link = document.createElement('a')
      // link.href = mapCanvas.toDataURL()
      // link.download = 'screenshot.png'
      // link.click()
      const layers = map.getAllLayers()
      let waveLayer = null
      let luoquLayer = null
      layers.forEach(layer => {
        const properties = layer.getProperties()
        if (properties && properties.layerType === '模式预报海浪') {
          waveLayer = layer
        }
        if (properties && properties.name === 'lqgj') {
          luoquLayer = layer
        }
      })
      let graphicJson = ''
      let features = []
      if (waveLayer || luoquLayer) {
        if (waveLayer) {
          features = waveLayer.getSource().getFeatures()
        }

        const lqfeatures = luoquLayer?.getSource()?.getFeatures()
        if (lqfeatures) {
          features.push(...lqfeatures)
        }
        const opt = {
          tension: 0.5, // tension
          pointsPerSeg: 10,
          normalize: false
        }
        features.forEach(f => {
          const lstring = f.getGeometry()?.getCoordinates()
          const points = []
          lstring.forEach(item => {
            points.push(item)
          })
          const geometry = new Polygon(points)
          const csp = geometry.cspline(opt)
          f.setGeometry(csp)
        })
        debugger
        // NOTE: 20250925 修复海浪落区绘制赋色掩盖问题
        const geojsonFormat = new GeoJSON()
        let geojsonObject = geojsonFormat.writeFeatures(features)
        // 现在 geojsonObject 是一个 GeoJSON 对象，你可以将其转换为字符串或者直接使用
        // graphicJson = JSON.stringify(geojsonObject)
        if (luoquLayer) {
          const geojsonObj = JSON.parse(geojsonObject)
          Array.isArray(geojsonObj.features) &&
            geojsonObj.features.sort((pre, cur) => {
              return pre.properties && cur.properties
                ? Number(pre.properties.value) - Number(cur.properties.value)
                : 0
            })
          geojsonObject = JSON.stringify(geojsonObj)
        }
        debugger
        graphicJson = JSON.stringify(geojsonObject)
      }
      Api.saveForecastImg({
        // forecastTaskId: props.currentProduct.id,
        graphicJson: graphicJson,
        graphicTemplateId: props.currentProduct.id,
        fileUrl: href,
        fileUrlColorful: fileUrlColorful,
        name:
          moment().format('YYYYMMDD') + '_' + localStorage.getItem('msybElemet')
      })
        .then(res => {
          console.log(res)
          message.success('保存成功')
          getRightList()
        })
        .catch(err => {
          console.log(err)
        })
    })
    map.renderSync()
  })
}

// 提交
function onSubmit() {
  // emit('save', current.value)
  if (current.value) {
    Api.submitForecast(checkeditem)
      .then(res => {
        message.success('提交成功')
      })
      .catch(() => {
        message.error('提交失败')
      })
  } else {
    message.warning('请选择一个预报图')
  }
}
function getRightList() {
  isLoading.value = true
  Api.getListByForecastId({
    graphicTemplateId: props.currentProduct.id
  })
    .then(res => {
      rightList.value = res
    })
    .finally(() => {
      isLoading.value = false
    })
    .catch(() => {})
}
onMounted(() => {
  // console.log(props.currentProduct, '**********')
  getRightList()

  eventBus.on('tempInfo', params => {
    tempInfo.value = params
  })
})
onBeforeUnmount(() => {
  eventBus.off('tempInfo')
})
</script>

<style lang="scss">
.open-icon {
  width: 24px;
  height: 24px;
  background-color: #fff;
  background-image: url(src/assets/images/forecast/aside-icon.png);
  background-size: 100% 100%;
  transform: rotate(180deg);
  position: absolute;
  right: 0px;
  top: 30px;
  z-index: 1;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.preview-aside {
  position: absolute;
  right: 0px;
  top: 10px;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  width: 230px;
  background: #fff;
  z-index: 1;
  animation: aside-leave 0.3s ease-in-out forwards;
  height: calc(100% - 100px);
  display: flex;
  flex-direction: column;
  .aside-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    padding: 13px 6px 8px;
  }
  .title {
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 600;
    font-size: 18px;
    color: #000000;
    margin-bottom: 14px;
    line-height: 24px;
    display: flex;
    align-items: center;
    .collapsed {
      width: 24px;
      height: 24px;
      background: url(src/assets/images/forecast/aside-icon.png) no-repeat;
      background-size: 100% 100%;
      display: inline-block;
      cursor: pointer;
    }
  }
  .aside-list {
    box-sizing: border-box;
    padding: 10px 7px;
    flex: 1;
    overflow-y: auto;
    .aside-item {
      width: 100%;
      height: 149px;
      background: #cee1f8;
      border-radius: 4px;
      box-sizing: border-box;
      padding: 3px;
      margin-bottom: 10px;
      position: relative;
      overflow: hidden;
      &:nth-last-child(1) {
        margin-bottom: 0;
      }
      .img-wrap {
        width: 100%;
        text-align: center;
      }
      img {
        max-width: 100%;
        height: 123px;
      }
      .descript {
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        background: #cee1f8;
        font-weight: 400;
        font-size: 14px;
        color: #222222;
        line-height: 16px;
        box-sizing: border-box;
        padding: 6px 14px;
        .download-icon {
          margin-left: 10px;
          color: #1c81f8;
          cursor: pointer;
          text-decoration: underline;
        }
      }
      .qx-checkbox {
        position: absolute;
        left: 10px;
        top: 10px;
      }
      &.active {
        background: rgba(28, 129, 248, 1);
        .descript {
          background: rgba(28, 129, 248, 1);
          color: #ffffff;
        }
      }
    }
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.fadeInRight {
  animation-name: fadeInRight;
}

@keyframes fadeOutRight {
  from {
    opacity: 1;
    width: 230px;
    display: block;
  }

  to {
    opacity: 0;
    width: 0;
    display: none;
    transform: translate3d(100%, 0, 0);
  }
}

.fadeOutRight {
  animation-name: fadeOutRight;
}
</style>
