<template>
  <div class="query-item">
    <div class="query-title">数据源：</div>
    <n-select
      v-model:value="dataSource"
      class="query-info"
      :options="options"
      label-field="dataSource"
      value-field="dataSource"
    />
  </div>
  <div class="query-item">
    <div class="query-title">起报时间：</div>
    <n-date-picker
      v-model:formatted-value="timestamp"
      class="query-info"
      type="datetime"
      clearable
      default-time="08:00:00"
    />
  </div>
  <div class="query-item">
    <div class="query-title">要素选择：</div>
    <n-select
      v-model:value="element"
      :options="elementsOptions"
      @update:value="handleUpdateValue"
    />
  </div>
  <div v-if="element === 'wave'" class="query-item">
    <div class="query-title">数据形式：</div>
    <n-checkbox-group
      v-model:value="dataType"
      class="query-info"
      @update:value="handleUpdateDataType"
    >
      <n-checkbox value="2" label="等值线" />
      <n-checkbox value="3" label="填色" />
    </n-checkbox-group>
  </div>
  <div class="query-bottom">
    <qx-button v-show="element === 'wave' && haveHLLayer" @click="addModify">{{
      isEdit ? '编辑' : '保存'
    }}</qx-button>
    <qx-button v-show="element === 'wave' && !isEdit" @click="cancleEdit"
      >取消</qx-button
    >
    <qx-button class="my-btn" @click="getData">生成落区</qx-button>
    <qx-button @click="onClear">清空</qx-button>
  </div>
  <QxTimeLine
    v-if="showTimeLine"
    ref="timeLineRef"
    class="msyb-time-line"
    @time-click="timeClick"
  ></QxTimeLine>
  <div v-if="legendUrl" class="legend-wrap">
    <img :src="legendUrl" alt="" />
  </div>
</template>

<script setup lang="ts" name="MSYB">
import { ref, onMounted, inject, onUnmounted, nextTick } from 'vue'
import { QxButton } from 'src/components/QxButton'
import Api from 'src/requests/toolRequest'
import GeoJSON from 'ol/format/GeoJSON.js'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import { Circle as CircleStyle, Fill, Stroke, Style, Text } from 'ol/style.js'
import Modify from 'ol/interaction/Modify.js'
import Collection from 'ol/Collection'
// import olExt from 'ol-ext/dist/ol-ext'
import MultiPoint from 'ol/geom/MultiPoint'
import Polygon from 'ol/geom/Polygon.js'
import 'ol-ext/render/Cspline.js'
import GeoTIFFSource from 'ol/source/GeoTIFF.js'
import * as GeoTIFF from 'geotiff'
import TileLayer from 'ol/layer/WebGLTile.js'
import { Point } from 'ol/geom'
import Feature from 'ol/Feature'
import gisUtils from 'src/utils/gis'
import { useMessage } from 'naive-ui'
import type { SelectOption } from 'naive-ui'
import moment from 'moment'
import Analysis from 'src/requests/analysis'
import dataSourceSign from './dataSoureSign.json'
import dataMaintenance from 'src/requests/dataMaintenance'
import { QxTimeLine } from 'src/components/QxTimeLine'
import ToolApi from 'src/requests/toolRequest'
import { removeLowerValueFeature } from 'src/components/OpenlayersMap/components/MSYB-hook/useGeoJSON'
const message = useMessage()
const getMap = inject<(map: any) => void>('getMap')
const dataSource = ref<null | string>(null)
const timestamp = ref(null)
const dataType = ref<any>(['2', '3'])
let options = ref<any>([])
let layers: any = []
let lonLat = [128, 64, 32, 16, 8, 4, 2, 1, 1, 1]
const isEdit = ref(true) // 是否处于编辑状态
const haveHLLayer = ref(false) // 是否有海浪图层
let vectorMark: any = null // 格点图层
let fillMapLayer: any = null // 填色图层
let element = ref('')
const elementsOptions = [
  {
    label: '海风',
    value: 'wind'
  },
  {
    label: '海浪',
    value: 'wave'
  },
  {
    label: '海流',
    value: 'current'
  },
  {
    label: '海温',
    value: 'temp'
  },
  {
    label: '盐度',
    value: 'salt'
  },
  {
    label: '风暴潮',
    value: 'storm'
  }
]
/** 点击表格数据展示对应的图层 */

let colorRamp = ref<number[]>([])
const legendUrl = ref('')
const showTimeLine = ref(false)
const timeLineRef = ref()
const tiffData = ref<any>([])
const jsonData = ref<any>([])
const tiffStyle = ref<any>([])
const curIndex = ref(0)
async function getData() {
  showTimeLine.value = true
  legendUrl.value = ''
  tiffData.value = []
  jsonData.value = []
  tiffStyle.value = []
  if (getMap) {
    getMap((map: any) => {
      vectorSource.refresh()
      if (fillMapLayer) {
        map.removeLayer(fillMapLayer)
        fillMapLayer = null
      }
    })
  }
  const sign: any = dataSource.value + '-' + element.value
  type DataSourceSignKeys =
    | 'NMEFC-wind'
    | 'NMEFC-wave'
    | 'NMEFC-current'
    | 'NMEFC-temp'
    | 'NMEFC-salt'
    | 'NMEFC-storm'
    | '智能网格-wind'
    | '智能网格-wave'
    | '智能网格-current'
    | '智能网格-temp'
    | '智能网格-salt'
    | '智能网格-storm'
  const param: any = dataSourceSign[sign as DataSourceSignKeys]
  console.log(param, 'params---')
  console.log(element.value, 'value---')
  if (element.value === 'wave') {
    haveHLLayer.value = true
    let prod: any = ''
    if (dataSource.value === '智能网格') {
      prod = 'grid-wave5_swh_0_tiff'
    } else {
      prod = 'regWave_swh_0_tiff'
    }
    Analysis.getIdentifybyProductId(prod)
      .then(async (res: any) => {
        const sourceSign = res.sourceIdentify
        const layerSign = res.layerIdentify
        const productId = res.productId
        const item = res.item
        // 获取样式
        const style: any = await Analysis.getLayerStyle(
          item,
          sourceSign,
          layerSign,
          'NULL'
        )
        tiffStyle.value = style
        legendUrl.value = `${config.ettpService}${style[0]?.legend}` || ''
        Analysis.getTiff({
          startTime: moment(timestamp.value).format('YYYY-MM-DD 00:00:00'),
          endTime: moment(timestamp.value).format('YYYY-MM-DD 23:59:59'),
          productId: productId,
          _tz: 'GMT'
        })
          .then((product: any) => {
            // console.log(product)
            tiffData.value = product
            const timeList: any = []
            product.forEach((item: any) => {
              timeList.push(item.forecastTime)
            })
            timeLineRef.value.reRenderTimeLine(timeList)
            nextTick(() => {
              if (product.length > 0) {
                timeLineRef.value.changeTime(
                  { label: product[0].forecastTime },
                  0
                )
              }
            })
          })
          .catch(() => {})
      })
      .catch(() => {})
    if (haveHLLayer.value) {
      Api.getReAnalysis(param, {
        beginTime: timestamp.value,
        endTime: moment(timestamp.value).format('YYYY-MM-DD 23:59:59'),
        orderBy: 'forecast_time',
        sort: 'asc',
        _tz: 'GMT'
      })
        .then((res: any) => {
          jsonData.value = res
          // timeLineRef.value.changeTime({ label: res[0].data_time }, 0)
        })
        .catch(() => {})
    }
  } else {
    haveHLLayer.value = false

    Analysis.getIdentifybyProductId(param)
      .then(async (res: any) => {
        console.log(res, 'res=====')
        //数据源标识/图层标识/高度层(没有默认传NULL) 传参顺序
        const sourceSign = res.sourceIdentify
        const layerSign = res.layerIdentify
        const productId = res.productId
        const item = res.item
        // 获取样式
        const style: any = await Analysis.getLayerStyle(
          item,
          sourceSign,
          layerSign,
          'NULL'
        )
        tiffStyle.value = style
        legendUrl.value = `${config.ettpService}${style[0]?.legend}` || ''
        //startTime=2025-01-12 12:00:00&endTime=2025-01-12 12:00:00&productId=wind_windspeed_0_tiff&_tz=GMT
        Analysis.getTiff({
          startTime: timestamp.value,
          endTime: moment(timestamp.value).format('YYYY-MM-DD 23:59:59'),
          productId: productId,
          _tz: 'GMT'
        })
          .then((tifDatas: any) => {
            const timeList: any = []
            tiffData.value = tifDatas
            if (tifDatas.length > 0) {
              tifDatas.forEach((item: any) => {
                timeList.push(item.forecastTime)
              })
              nextTick(() => {
                if (tifDatas.length > 0) {
                  timeLineRef.value.changeTime(
                    { label: tifDatas[0].forecastTime },
                    0
                  )
                }
              })
            }
            timeLineRef.value.reRenderTimeLine(timeList)
          })
          .catch(() => {})
          .catch(() => {})
      })
      .catch(() => {})
  }
}
function timeClick(time: any, index: any) {
  curIndex.value = index
  if (getMap) {
    getMap((map: any) => {
      vectorSource.refresh()
      if (fillMapLayer) {
        map.removeLayer(fillMapLayer)
        fillMapLayer = null
      }
    })
  }
  if (haveHLLayer.value) {
    if (dataType.value.includes('3')) {
      const url = config.onlyOfficeServerUrl + tiffData.value[index].filePath
      createLayer(url, tiffStyle.value[0])
    }
    if (dataType.value.includes('2')) {
      addGeojson(jsonData.value[index].file_path)
    }
  } else {
    const url = config.onlyOfficeServerUrl + tiffData.value[index].filePath
    createLayer(url, tiffStyle.value[0])
  }
}
function handleUpdateDataType() {
  if (getMap) {
    getMap((map: any) => {
      vectorSource.refresh()
      if (fillMapLayer) {
        map.removeLayer(fillMapLayer)
        fillMapLayer = null
      }
      if (dataType.value.includes('3')) {
        const url =
          config.onlyOfficeServerUrl + tiffData.value[curIndex.value].filePath
        createLayer(url, tiffStyle.value[0])
      }
      if (dataType.value.includes('2')) {
        addGeojson(jsonData.value[curIndex.value].file_path)
      }
    })
  }
}
function onClear(type = 'all') {
  if (getMap) {
    getMap((map: any) => {
      // 删除海浪图层
      map.removeLayer(vectorLayer)
      pointSource.refresh()
      if (fillMapLayer) {
        map.removeLayer(fillMapLayer)
        fillMapLayer = null
      }
    })
  }
  element.value = ''
  dataSource.value = ''
  timestamp.value = null
  if (type == 'all') {
    dataType.value = []
    dataSource.value = null
    timestamp.value = null
  }
}
function createLayer(url: string, colorData: any) {
  const sourceOptions: any = {
    url: url,
    max: null,
    min: null,
    bands: ['1'],
    nodata: colorData.invalidValue
  }
  const source = new GeoTIFFSource({
    sources: [sourceOptions],
    normalize: false, // 归一化
    interpolate: false, // 插值
    wrapX: false,
    // @ts-ignore
    crossOrigin: 'anonymous'
  })
  fillMapLayer = new TileLayer({
    source: source,
    zIndex: 4
  })
  const styleOptions = gisUtils.getTifStyle(colorData)
  fillMapLayer.setStyle({
    color: styleOptions
  })
  getMap &&
    getMap((map: any) => {
      map.addLayer(fillMapLayer)
    })
}

// 栅格点
function setPointsList(url: string) {
  if (getMap) {
    getMap((map: any) => {
      let resultSources: any = new VectorSource({ wrapX: false })
      vectorMark = new VectorLayer({
        source: resultSources,
        zIndex: 10,
        style: new Style({
          fill: new Fill({
            color: 'rgba(0, 0, 0, 0)'
          }),
          stroke: new Stroke({ color: '#0000cd', width: 2 })
        })
      })
      map.addLayer(vectorMark)
      const pointStyle = function (feature: any) {
        return new Style({
          zIndex: 1010,
          text: new Text({
            fill: new Fill({
              color: '#fff'
            }),
            stroke: new Stroke({
              color: '#000',
              width: 2
            }),
            text: feature.get('val'),
            font: '15px Calibri,sans-serif blod'
          })
        })
      }

      const pointSource: any = new GeoTIFFSource({
        sources: [
          {
            // visible red, band 1 in the style expression above
            nodata: 65535,
            // url: 'src/components/OpenlayersMap/components/swh_spa.tif',
            url,
            min: 0,
            max: 255,
            bands: [1]
          }
        ],
        normalize: true,
        interpolate: false,
        wrapX: false
      })
      let geoPointtifLayer = new TileLayer({
        source: pointSource,
        zIndex: 101,
        style: {
          color: [
            'case',
            ['between', ['band', 1], 0, 255],
            ['color', 0, 0, 0, 0],
            ['color', 0, 0, 0, 0]
          ]
        }
      })
      map.addLayer(geoPointtifLayer)

      let extent0: any = null
      let extent1: any = null
      const zoom = 2
      let isFinally = true
      let value = lonLat[zoom]
      const extent = map.getView().calculateExtent(map.getSize())
      extent0 = Math.floor(extent[0])
      extent1 = Math.floor(extent[1])
      map.on('moveend', () => {
        const zoom = map.getView().getZoom().toFixed(0)
        value = lonLat[zoom]
        const extent = map.getView().calculateExtent(map.getSize())
        extent0 = Math.floor(extent[0] - (extent[0] % value))
        extent1 = Math.floor(extent[1] - (extent[1] % value))
        drawPonit()
      })
      geoPointtifLayer.on('postrender', (evt: any) => {
        // this.map.on("rendercomplete", (evt) => {
        if (isFinally) {
          const save_to_center = map.getView().getCenter()
          const save_to_zoom = map.getView().getZoom().toFixed(0)
          const center = [save_to_center[0] + 0.01, save_to_center[1] + 0.01]
          map.getView().animate({ center: center }, { zoom: save_to_zoom })
          const zoom = map.getView().getZoom().toFixed(0)
          value = lonLat[zoom]
          const extent = map.getView().calculateExtent(map.getSize())
          extent0 = Math.floor(extent[0] - (extent[0] % value))
          extent1 = Math.floor(extent[1] - (extent[1] % value))
          drawPonit()
          isFinally = false
        }
      })
      function drawPonit() {
        const xyArray = []
        for (let x = extent0; x < extent[2]; x += value) {
          for (let y = extent1; y < extent[3]; y += value) {
            xyArray.push([x, y])
          }
        }

        let drawPoint
        let feature

        const projection = map.getView().getProjection()
        // // 创建源投影和目标投影
        // const sourceProjection = new Projection({
        //   code: "EPSG:4326" // 源投影的代码
        // });

        // const targetProjection = new Projection({
        //   code: projection // 目标投影的代码
        // });

        const features = []
        for (const index in xyArray) {
          drawPoint = new Point(xyArray[index])
          feature = new Feature(drawPoint)
          feature.set('val', '')
          feature.setStyle(pointStyle)
          features.push(feature)
        }
        vectorMark.getSource().clear()
        vectorMark.getSource().addFeatures(features)

        let pixelLonlat
        let pointFeature
        if (xyArray.length <= 0) return
        for (const index in xyArray) {
          pixelLonlat = map.getPixelFromCoordinate(xyArray[index])
          const data: any = geoPointtifLayer.getData(pixelLonlat)
          if (data != null) {
            pointFeature = resultSources.getFeaturesAtCoordinate(xyArray[index])
            if (pointFeature.length !== 0) {
              if (data[0] !== 0) {
                //   // 设置点信息
                pointFeature[0].set('val', data[0] + '')
              }
            }
          }
        }
      }
    })
  }
}

// 海浪数据源
const vectorSource = new VectorSource()
// 海浪图层
const vectorLayer = new VectorLayer({
  source: vectorSource,
  zIndex: 5
})
function cancleEdit() {
  isEdit.value = true
  vectorSource.refresh()
  if (getMap) {
    getMap((map: any) => {
      map.removeInteraction(mod)
    })
  }
  modifyFeatures.clear()
  tempWaveFeature.value.forEach((item: any) => {
    vectorSource.addFeature(item)
  })
}
// 用于保存编辑海浪时取消的时候还原原来的图层
const tempWaveFeature: any = ref(null)
const countryStyle = new Style({
  stroke: new Stroke({
    color: 'black',
    width: 2
  }),
  text: new Text({
    font: '16px Calibri,sans-serif',
    fill: new Fill({
      color: '#000'
    }),
    stroke: new Stroke({
      color: '#fff',
      width: 3
    }),
    placement: 'line'
  })
})
const style = [countryStyle]
function lineStyles(feature: any) {
  const label = feature.get('value') + ''
  countryStyle?.getText()?.setText(label)
  return style
}
const stylefn = (f: any) => {
  const opt = {
    tension: 0.5, // tension
    pointsPerSeg: 10,
    normalize: false
  }
  // const csp = f.getGeometry().cspline(opt)

  const label = f.get('value') + ''
  countryStyle?.getText()?.setText(label)
  const editPoint: any = f.getGeometry()?.getCoordinates()
  const pointGeometry: any = []
  let geometry: any = null
  const properties = f.getProperties()

  // if (properties.points) {
  //   pointGeometry = properties.points
  //   geometry = new Polygon([properties.points])
  // } else {

  // }
  const lstring = f.getGeometry()?.getCoordinates()
  const points: any = []
  lstring.forEach((item: any) => {
    points.push(item)
  })
  geometry = new Polygon(points)
  editPoint.forEach((item: any) => {
    // const factor = item.length / 100 > 1 ? Math.floor(item.length / 100) : 1
    // console.log(factor, item.length, '***factor****')
    const downsampledArray = downsampleArray(item, 1)
    downsampledArray.forEach(item1 => {
      pointGeometry.push(item1)
    })
  })
  const csp = geometry.cspline(opt)

  return [
    new Style({
      stroke: new Stroke({ color: 'red', width: 1 }),
      geometry: csp
    }),
    new Style({
      image: new CircleStyle({
        stroke: new Stroke({ color: 'red', width: 4 }),
        radius: 2
      }),
      geometry: new MultiPoint(pointGeometry)
    })
  ]
}
function addGeojson(u: any) {
  vectorSource.refresh()
  // @ts-ignore
  const url = config.fileService + u
  fetch(url)
    .then((res: any) => res.json())
    .then((json: any) => {
      removeLowerValueFeature(json, 2.5)
      const featuresJson = new GeoJSON().readFeatures(json)
      const features: any = []
      featuresJson.forEach((item: any) => {
        const opt: any = {
          tension: 0.5, // tension
          pointsPerSeg: 10,
          normalize: false
        }
        const editPoint: any = item.getGeometry().getCoordinates()
        const pointGeometry: any = []
        editPoint.forEach((item: any) => {
          const factor = item.length / 50 > 1 ? Math.floor(item.length / 50) : 1
          const downsampledArray = downsampleArray(item, factor)
          downsampledArray.forEach(item1 => {
            pointGeometry.push(item1)
          })
        })
        const geometry: any = new Polygon([pointGeometry])
        const csp = geometry.cspline(opt)
        item.setGeometry(csp)
        item.set('points', pointGeometry)
        item.setStyle(lineStyles)
        vectorSource.addFeature(item)
        features.push(item)
      })
      const geojsonFormat = new GeoJSON()
      const geojsonObject = geojsonFormat.writeFeatures(features)

      // 现在 geojsonObject 是一个 GeoJSON 对象，你可以将其转换为字符串或者直接使用
      const graphicJson = JSON.stringify(geojsonObject)
      // 获取与陆地展示的点
      const formData = new FormData()
      formData.append('geoJsonString', graphicJson)
      ToolApi.getLandPoint(formData)
        .then(res => {
          addLandPoint(res)
        })
        .catch(() => {})
    })
    .catch(() => {})
  // features.push(new Feature(new LineString(lstring)))
}
const pointSource = new VectorSource()
const pointLayer = new VectorLayer({
  source: pointSource,
  declutter: true, //防止覆盖
  zIndex: 8
})
function addLandPoint(points: any) {
  points.forEach((item: any) => {
    const feature = new Feature(new Point(item.coordinate))
    const iconStyle = new Style({
      text: new Text({
        font: '16px Calibri,sans-serif',
        text: item.value,
        fill: new Fill({
          color: '#000'
        }),
        stroke: new Stroke({
          color: '#fff',
          width: 3
        })
      })
    })
    feature.setStyle(iconStyle)
    pointSource.addFeature(feature)
  })
}
function addModify() {
  isEdit.value = !isEdit.value
  if (!isEdit.value) {
    // 编辑
    editFeature()
  } else {
    //保存
    saveFeature()
  }
}
let mod: any = null
const modifyFeatures: any = new Collection()
function editFeature() {
  pointSource.refresh()
  try {
    const features = vectorSource.getFeatures()
    tempWaveFeature.value = []
    features.forEach(item => {
      const properties = item.getProperties()
      const curF = item.clone()
      tempWaveFeature.value.push(curF)
      let feature: any = null
      if (properties.points) {
        feature = new Feature(new Polygon([properties.points]))
      } else {
        const lstring = (item.getGeometry() as Polygon).getCoordinates()
        feature = new Feature(new Polygon(lstring))
      }

      for (const key in properties) {
        if (key != 'geometry') {
          feature.set(key, properties[key])
        }
      }
      modifyFeatures.push(feature)
    })
    vectorSource.clear()
    modifyFeatures.forEach((item: any) => {
      vectorSource.addFeature(item)
      item.setStyle(stylefn)
    })
    // vectorLayer.setStyle(stylefn)
    if (getMap) {
      getMap((map: any) => {
        mod = new Modify({ features: modifyFeatures })
        map.addInteraction(mod)
      })
    }
  } catch (err: any) {
    console.log(err)
  }
}
function downsampleArray<T>(arr: T[], factor: number): T[] {
  if (factor <= 0) {
    throw new Error('Factor must be greater than 0')
  }

  const result: T[] = []
  arr.forEach((element, index) => {
    if (index % factor === 0) {
      result.push(element)
    }
  })

  return result
}
function saveFeature() {
  vectorSource.refresh()
  pointSource.refresh()
  const features: any = []
  modifyFeatures.forEach((item: any) => {
    const opt = {
      tension: 0.5, // tension
      pointsPerSeg: 10,
      normalize: false
    }
    const editPoint: any = item.getGeometry()?.getCoordinates()
    const pointGeometry: any = []
    editPoint.forEach((item: any) => {
      const factor = item.length / 50 > 1 ? Math.floor(item.length / 50) : 1
      const downsampledArray = downsampleArray(item, factor)
      downsampledArray.forEach(item1 => {
        pointGeometry.push(item1)
      })
    })
    const geometry: any = new Polygon([pointGeometry])
    const csp = geometry.cspline(opt)
    item.setGeometry(csp)
    item.set('points', pointGeometry)
    item.setStyle(lineStyles)
    vectorSource.addFeature(item)
    features.push(item)
  })
  const geojsonFormat = new GeoJSON()
  const geojsonObject = geojsonFormat.writeFeatures(features)

  // 现在 geojsonObject 是一个 GeoJSON 对象，你可以将其转换为字符串或者直接使用
  const graphicJson = JSON.stringify(geojsonObject)
  // 获取与陆地展示的点
  const formData = new FormData()
  formData.append('geoJsonString', graphicJson)
  ToolApi.getLandPoint(formData)
    .then(res => {
      console.log(res)
      addLandPoint(res)
    })
    .catch(() => {})
  if (getMap) {
    getMap((map: any) => {
      map.removeInteraction(mod)
    })
  }
  modifyFeatures.clear()
}
const curElement = ref('')
function handleUpdateValue(value: string, option: any) {
  // TODO: 设置要素名
  console.log(
    9999,
    `MSYB.handleUpdateValue: value=${value} options=${options.value}`
  )
  localStorage.setItem('msybElemet', value)
  curElement.value = option.label
}
onMounted(() => {
  if (getMap) {
    getMap((map: any) => {
      map.addLayer(vectorLayer)
      map.addLayer(pointLayer)
      vectorLayer.setProperties({
        layerType: '模式预报海浪'
      })
    })
  }
  Api.getDataSourceList()
    .then(res => {
      options.value = res
    })
    .catch(() => {})
})
onUnmounted(() => {
  if (getMap) {
    getMap((map: any) => {
      // 移除海浪图层
      map.removeLayer(vectorLayer)
      map.removeLayer(pointLayer)
      if (fillMapLayer) {
        map.removeLayer(fillMapLayer)
        fillMapLayer = null
      }
    })
  }
})
</script>

<style lang="scss" scoped>
.query-item {
  display: flex;
  align-items: center;
  margin: 7px 0px;
  .query-title {
    white-space: nowrap;
    width: 80px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 300;
    font-size: 14px;
    color: #222222;
    line-height: 16px;
  }
  .query-info {
    width: 225px;
    flex: 1;
  }
}
.my-btn {
  width: 100px;
  height: 32px;
  background: #1c81f8;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 16px;
}
.query-bottom {
  height: 42px;
  display: flex;
  justify-content: end;
  align-items: end;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 30px;
}

.legend-wrap {
  position: fixed;
  z-index: 3;
  right: 8px;
  bottom: 30px;
  height: 59px;
  width: 540px;
  img {
    max-width: 100%;
    height: 100%;
  }
}
.msyb-time-line {
  left: 26px;
}
</style>
