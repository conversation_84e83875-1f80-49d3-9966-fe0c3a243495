<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-11-05 16:51:01
 * @LastEditors: 樊海玲 <EMAIL>
 * @LastEditTime: 2025-02-26 14:58:39
 * @FilePath: \hainan-jianzai-web\src\components\ElementStatistics\components\NumericalPrediction.vue
 * @Description: 数值预报查询检索
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="query-item">
    <div class="query-title">海洋要素：</div>
    <div class="query-info">
      <div
        class="radio-btn"
        :class="{ active: element === 'HN_SZYB_HMF_' }"
        @click="element = 'HN_SZYB_HMF_'"
      >
        海面风
      </div>
      <div
        class="radio-btn"
        :class="{ active: element === 'HN_SZYB_HL_' }"
        @click="element = 'HN_SZYB_HL_'"
      >
        海浪
      </div>
      <div
        class="radio-btn"
        :class="{ active: element === 'HN_SZYB_HLL_' }"
        @click="element = 'HN_SZYB_HLL_'"
      >
        环流
      </div>
      <div
        class="radio-btn"
        :class="{ active: element === 'HN_SZYB_FBC_' }"
        @click="element = 'HN_SZYB_FBC_'"
      >
        风暴潮
      </div>
    </div>
  </div>
  <div class="query-item">
    <div class="query-title">展示方式：</div>
    <div class="query-info">
      <n-select
        v-model:value="showType"
        :options="options"
        size="small"
        clearable
      />
    </div>
  </div>
  <div class="query-item">
    <div class="query-title">时间范围：</div>
    <div class="query-info">
      <n-date-picker
        v-model:formatted-value="range"
        value-format="yyyy-MM-dd HH:mm:ss"
        type="datetimerange"
        size="small"
        clearable
        :default-time="['00:00:00', '23:59:59']"
      />
    </div>
  </div>
  <div class="query-item">
    <div class="query-title">空间范围：</div>
    <div class="query-info">
      <n-input
        v-model:value="locationJson"
        placeholder="请输入"
        clearable
        size="small"
        @update:value="updateFeature"
      />

      <Scan class="icon-scan" @click="drawHandler" />
    </div>
  </div>
  <div class="center-btn">
    <qx-button class="query-btn" @click="queryInfo">查询</qx-button>
  </div>
  <div v-show="showType === 'TIF'" class="alis-mouse-position">
    <div class="legend">
      <div class="text">
        {{
          element === 'HN_SZYB_HMF_'
            ? '海面风'
            : element === 'HN_SZYB_HL_'
            ? '海浪'
            : element === 'HN_SZYB_HLL_'
            ? '环流'
            : '风暴潮'
        }}
      </div>
      <div class="color-ramp">
        <span v-for="item in colorRamp" :key="item">{{ item.toFixed(1) }}</span>
      </div>
    </div>
  </div>
  <QxTimeLine
    v-if="showTimeLine"
    ref="timeLineRef"
    @time-click="timeClick"
  ></QxTimeLine>
</template>

<script setup>
import {
  ref,
  inject,
  onMounted,
  onBeforeUnmount,
  onUnmounted,
  nextTick
} from 'vue'
import { Scan } from '@vicons/ionicons5'
import { Modify, Select } from 'ol/interaction.js'
import { Circle, Fill, Icon, Stroke, Style, Text } from 'ol/style.js'
import VectorLayer from 'ol/layer/Vector.js'
import VectorSource from 'ol/source/Vector.js'
import { GeoJSON } from 'ol/format'
import { Draw } from 'ol/interaction'
import { useMessage } from 'naive-ui'
import { QxButton } from 'src/components/QxButton'
import { hexToRgbs } from 'src/utils/hexToRgb.js'
import { WebGLTile } from 'ol/layer'
import GeoTIFFSource from 'ol/source/GeoTIFF'
import Popup from './ol-popup.js'
import { click } from 'ol/events/condition.js'
import Rainbow from 'src/utils/rainbowvis.js'
import ToolApi from 'src/requests/toolRequest'
import stationImg from 'src/assets/images/station.png'
import fengbaocgaoColor from './Fengbaochao_TIF.json'
import haifengColor from './HaiFeng_TIF.json'
import huanliuColor from './Huanliu_TIF.json'
import hailangColor from './HaiLang_TIF.json'
import './ol-popup.css'
import { QxTimeLine } from 'src/components/QxTimeLine'
const element = ref('HN_SZYB_HMF_') // 海洋要素
const timeLineRef = ref()
const message = useMessage()
const getMap = inject('getMap')
const showType = ref('JSON') //展示方式
const options = [
  {
    label: '一维',
    value: 'JSON'
  },
  {
    label: '二维',
    value: 'TIF'
  }
]
const range = ref(null) //时间范围
const locationJson = ref('') //空间范围
/** 空间范围修改功能 */
const source = new VectorSource()
const layer = new VectorLayer({
  source: source,
  zIndex: 8
})
function updateFeature(value) {
  source.clear()
  if (value.trim() !== '') {
    const feature = new GeoJSON().readFeatures(value)
    feature[0].setStyle(
      new Style({
        fill: new Fill({
          color: 'rgba(0, 0, 255, 0.1)'
        }),
        stroke: new Stroke({
          color: '#ff0000'
        })
      })
    )
    source.addFeature(feature[0])
  }
}
let draw = null
function drawHandler() {
  getMap(map => {
    if (draw) {
      map.removeInteraction(draw)
    }
    draw = new Draw({
      type: 'Polygon', //绘制的几何图形的几何类型
      source
    })
    map.addInteraction(draw)

    draw.on('drawstart', drawstart)
    draw.on('drawend', drawend)
  })
}
function drawstart(evt) {
  source.refresh()

  message.warning('开始绘制,双击击结束')
}
function drawend(event) {
  getMap(map => {
    const json = new GeoJSON().writeFeature(event.feature)
    locationJson.value = json
    map.removeInteraction(draw)
  })
}
/** 地图图层 */
const predictionData = ref([])
const curElement = ref('')
let oneDimensionalLayer = null // 一维图层
let twoDimensionalLayer = null // 二维图层
var popup = new Popup()
const showTimeLine = ref(false)
function queryInfo() {
  getMap(map => {
    if (oneDimensionalLayer) {
      map.removeLayer(oneDimensionalLayer)
    }
    if (twoDimensionalLayer) {
      map.removeLayer(twoDimensionalLayer)
    }
    if (selectClick) {
      map.removeInteraction(selectClick)
    }
  })
  popup.hide()
  const params = {
    orderBy: 'data_time',
    sort: 'asc'
  }

  if (range.value.length) {
    params.beginTime = range.value[0]
    params.endTime = range.value[1]
  }

  ToolApi.getReAnalysis(element.value + showType.value, params)
    .then(res => {
      predictionData.value = res
      const timeList = []
      res.forEach(item => {
        timeList.push(item.data_time)
      })
      if (res.length) {
        showTimeLine.value = true
        nextTick(() => {
          console.log(timeList, 'list------')
          timeLineRef.value.reRenderTimeLine(timeList)
          timeLineRef.value.changeTime({ label: res[0].data_time }, 0)
        })
      }
    })
    .catch(() => {})
  // nextTick(() => {
  //   timeLineRef.value.reRenderTimeLine([
  //     '2024-05-01 01:00:00',
  //     '2024-05-01 02:00:00'
  //   ])
  //   if (showType.value == 1) {
  //     addOneDimensionalLayer()
  //   }
  //   if (showType.value == 2) {
  //     addTwoDimensionalLayer()
  //   }
  // })
}
let selectClick = null
const jet = [
  '#00007F',
  '#000091',
  '#0000A3',
  '#0000B6',
  '#0000C8',
  '#0000DA',
  '#0000EC',
  '#0000FE',
  '#0000FF',
  '#0010FF',
  '#0020FF',
  '#0030FF',
  '#0040FF',
  '#0050FF',
  '#0060FF',
  '#0070FF',
  '#0084FF',
  '#0094FF',
  '#00A4FF',
  '#00B4FF',
  '#00C4FF',
  '#00D4FF',
  '#00E4F7',
  '#0CF4EA',
  '#18FFDD',
  '#25FFD0',
  '#32FFC3',
  '#3FFFB7',
  '#4CFFAA',
  '#59FF9D',
  '#66FF90',
  '#73FF83',
  '#83FF73',
  '#90FF66',
  '#9DFF59',
  '#AAFF4C',
  '#B7FF3F',
  '#C3FF32',
  '#D0FF25',
  '#DDFF18',
  '#EAFF0C',
  '#F7F400',
  '#FFE500',
  '#FFD700',
  '#FFC800',
  '#FFB900',
  '#FFAA00',
  '#FF9B00',
  '#FF8900',
  '#FF7A00',
  '#FF6B00',
  '#FF5C00',
  '#FF4D00',
  '#FF3F00',
  '#FF3000',
  '#FF2100',
  '#FE1200',
  '#EC0300',
  '#DA0000',
  '#C80000',
  '#B60000',
  '#A30000',
  '#910000',
  '#7F0000'
]
// 添加一维图层
function addOneDimensionalLayer(url) {
  // var rainbow = new Rainbow() // by default, range is 0 to 100
  // rainbow.setSpectrum(jet)
  // rainbow.setNumberRange(10, 34);
  // const color = "#" + rainbow.colourAt(10);
  popup.hide()
  const source = new VectorSource({
    url: url,
    format: new GeoJSON()
  })

  oneDimensionalLayer = new VectorLayer({
    source: source,
    style: new Style({
      image: new Icon({
        anchor: [0.5, 1],
        src: stationImg,
        scale: [0.6, 0.6]
      })
    })
  })
  selectClick = new Select({
    condition: click,
    features: source.getFeaturesCollection(),
    style: new Style({
      image: new Icon({
        anchor: [0.5, 1],
        src: stationImg,
        scale: [1, 1]
      })
    })
  })
  getMap(map => {
    map.addLayer(oneDimensionalLayer)
    map.addInteraction(selectClick)
    selectClick.on('select', function (e) {
      popup.hide()
      if (e.selected.length > 0) {
        const feature = e.selected[0]
        const info = feature.getProperties()
        let mapper = [
          'name',
          'windspeed',
          'winddir',
          'waveHeight',
          'waveDir',
          'wavePeriod',
          'CurrentSpeed',
          'CurrentDir',
          'Waterheight'
        ]
        Object.keys(info).forEach(key => {
          if (mapper.includes(key) && info[key] == 999.9) {
            info[key] = null
          }
        })
        let html = ''
        if (info.name) {
          if (element.value === 'HN_SZYB_HMF_') {
            html = `<div class="infoResult">
            <p class="title-popup">名称：${info.name}</p>
            <p class="title-popup">风速：${
              info.windspeed ? info.windspeed + 'm/s' : '--'
            }</p>
            <p class="title-popup">风向：${
              info.winddir ? info.winddir + '°' : '--'
            }</p>
            </div>`
          }
          if (element.value === 'HN_SZYB_HL_') {
            //波高（m）、波向（°）和波周期（s）
            html = `<div class="infoResult">
            <p class="title-popup">名称：${info.name}</p>
            <p class="title-popup">波高：${
              info.waveHeight ? info.waveHeight + 'm' : '--'
            }</p>
            <p class="title-popup">波向：${
              info.waveDir ? info.waveDir + '°' : '--'
            }</p>
            <p class="title-popup">波周期：${
              info.wavePeriod ? info.wavePeriod + 's' : '--'
            }</p>
            </div>`
          }
          if (element.value === 'HN_SZYB_HLL_') {
            html = `<div class="infoResult">
            <p class="title-popup">名称：${info.name}</p>
            <p class="title-popup">流速：${
              info.CurrentSpeed ? info.CurrentSpeed + 'm' : '--'
            }</p>
            <p class="title-popup">流向：${
              info.CurrentDir ? info.CurrentDir + '°' : '--'
            }</p>
            </div>`
          }
          if (element.value === 'HN_SZYB_FBC_') {
            html = `<div class="infoResult">
            <p class="title-popup">名称：${info.name}</p>
            <p class="title-popup">风暴潮增水：${
              info.Waterheight ? info.Waterheight + 'm' : '--'
            }</p>
            </div>`
          }

          popup.show(e.mapBrowserEvent.coordinate, html)
        }
      }
    })
  })
}

const selected = new Style({
  fill: new Fill({
    color: 'rgba(255,255,255,0.3)'
  }),
  stroke: new Stroke({
    color: 'rgba(255, 255, 255, 0.7)',
    width: 2
  })
})

// select interaction working on "click"

const colorRamp = ref([]) // colorBar数字部分
// 将数组平均分成几份
function divideRangeIntoParts(min, max, parts) {
  if (max <= min) {
    throw new Error('max must be greater than min')
  }

  let range = max - min
  let step = range / (parts - 1) // parts - 1 是为了确保包含 max 值
  let result = []

  for (let i = 0; i < parts; i++) {
    let value = min + i * step
    result.push(value)
  }

  return result
}
// 添加二维图层
async function addTwoDimensionalLayer(url) {
  getMap(map => {
    if (oneDimensionalLayer) {
      map.removeLayer(oneDimensionalLayer)
    }
    if (twoDimensionalLayer) {
      map.removeLayer(twoDimensionalLayer)
    }
  })
  let colorData = null
  if (element.value === 'HN_SZYB_HMF_') {
    colorData = haifengColor
    colorRamp.value = divideRangeIntoParts(0, 25, 5)
  }
  if (element.value === 'HN_SZYB_HL_') {
    colorData = hailangColor
    colorRamp.value = divideRangeIntoParts(0, 4, 5)
  }
  if (element.value === 'HN_SZYB_HLL_') {
    colorData = huanliuColor
    colorRamp.value = divideRangeIntoParts(0, 2, 5)
  }
  if (element.value === 'HN_SZYB_FBC_') {
    colorData = fengbaocgaoColor
    colorRamp.value = divideRangeIntoParts(-1, 1, 5)
  }
  createLayer(url, colorData)
}
function getTifStyle(colorData) {
  let obj = {}
  if (colorData) {
    const { colorAttr, invalidValue, renderType } = colorData
    const color = []
    const opacity = []
    const quantity = []
    colorAttr.forEach(item => {
      color.push(item.color)
      opacity.push(item.opacity - 0)
      quantity.push(item.quantity - 0)
    })
    obj = hexToRgbs(color, quantity, opacity, renderType, invalidValue)
    return obj
  } else {
    return obj
  }
}

function createLayer(url, colorData) {
  const sourceOptions = {
    url: url,
    max: null,
    min: null,
    bands: ['1'],
    nodata: colorData.invalidValue
  }
  const source = new GeoTIFFSource({
    sources: [sourceOptions],
    normalize: false, // 归一化
    interpolate: false, // 插值
    wrapX: false,
    crossOrigin: 'anonymous'
  })
  twoDimensionalLayer = new WebGLTile({
    source: source
  })
  const styleOptions = getTifStyle(colorData)
  twoDimensionalLayer.setStyle({
    color: styleOptions
  })
  getMap(map => {
    map.addLayer(twoDimensionalLayer)
  })
}
/** 时间轴部分 */
function timeClick(time, index) {
  getMap(map => {
    if (oneDimensionalLayer) {
      map.removeLayer(oneDimensionalLayer)
    }
    if (twoDimensionalLayer) {
      map.removeLayer(twoDimensionalLayer)
    }
    if (selectClick) {
      map.removeInteraction(selectClick)
    }
  })
  const url = config.onlyOfficeServerUrl + predictionData.value[index].file_path
  if (showType.value == 'JSON') {
    addOneDimensionalLayer(url)
  }
  if (showType.value == 'TIF') {
    addTwoDimensionalLayer(url) // addTwoDimensionalLayer()
  }
}
onMounted(() => {
  getMap(map => {
    map.addLayer(layer)
    // map.addOverlay(popup)
    map.addOverlay(popup)
    map.on('loadstart', function () {
      map.getTargetElement().classList.add('spinner')
    })
    map.on('loadend', function () {
      map.getTargetElement().classList.remove('spinner')
    })
  })
})
onUnmounted(() => {
  getMap(map => {
    if (oneDimensionalLayer) {
      map.removeLayer(oneDimensionalLayer)
    }
    if (twoDimensionalLayer) {
      map.removeLayer(twoDimensionalLayer)
    }
    map.removeOverlay(popup)
    map.removeLayer(layer)
    if (draw) {
      map.removeInteraction(draw)
    }
    if (selectClick) {
      map.removeInteraction(selectClick)
    }
  })
})
</script>

<style lang="scss" scoped>
.query-item {
  display: flex;
  align-items: center;
  margin: 7px 0px;
  .query-title {
    white-space: nowrap;
    width: 70px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 300;
    font-size: 14px;
    color: #222222;
    line-height: 16px;
  }
  .query-info {
    width: 346px;
    display: flex;
    align-items: center;
    .radio-btn {
      background-color: rgb(231, 241, 253);
      padding: 5px 8px;
      margin: 0px 5px;
      border-radius: 4px;
      color: #222222;
      flex-shrink: 0;
      cursor: default;
      &:hover {
        background-color: rgb(76, 129, 250);
        color: #fff;
      }
      &.active {
        background-color: rgb(76, 129, 250);
        color: #fff;
      }
    }
    .icon-scan {
      width: 20px;
      height: 20px;
      margin-left: 10px;
      cursor: pointer;
    }
  }
}
.center-btn {
  height: 40px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  .query-btn {
    background: #1c81f8;
    color: #fff;
    &:hover {
      background: linear-gradient(180deg, #fd950e 0%, #f97400 100%);
    }
  }
}
.alis-mouse-position {
  position: fixed;
  z-index: 800;
  right: 20px;
  bottom: 18px;
  width: 370px;
  // padding: 15px;
  color: #fff;
  background: #eff4fc;
  border-radius: 4px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #666;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  .legend {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // margin-bottom: 15px;
    width: 100%;
    height: 100%;
    .text {
      white-space: nowrap;
      margin-right: 8px;
    }
    .color-ramp {
      flex: 1;
      position: relative;
      height: 15px;
      border-radius: 8px;
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      color: #fff;
      font-size: 12px;
      padding: 0px 6px;
      background: linear-gradient(
        90deg,
        #00007f,
        #000093,
        #0000a9,
        #0000c1,
        #0000d7,
        #0000ed,
        #0000ff,
        #0009ff,
        #001eff,
        #0032ff,
        #0045ff,
        #005aff,
        #006eff,
        #0083ff,
        #0096ff,
        #00aaff,
        #00bfff,
        #00d2ff,
        #01e7f4,
        #11fae4,
        #20ffd5,
        #31ffc3,
        #41ffb4,
        #52ffa3,
        #62ff93,
        #72ff83,
        #82ff73,
        #92ff63,
        #a1ff54,
        #b2ff42,
        #c2ff33,
        #d4ff21,
        #e3ff12,
        #f3f902,
        #ffe500,
        #ffd300,
        #ffbf00,
        #ffae00,
        #ff9b00,
        #ff8800,
        #ff7600,
        #ff6300,
        #ff5000,
        #ff3f00,
        #ff2b00,
        #ff1a00,
        #ef0600,
        #d90000,
        #c30000,
        #ab0000,
        #950000,
        #7f0000
      );
    }
  }
}
</style>
