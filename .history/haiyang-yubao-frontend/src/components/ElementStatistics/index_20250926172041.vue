<template>
  <div class="btns-statistics">
    <div
      v-for="item in toolList"
      :key="item.id"
      class="btns-statistics-item"
      :class="activeVal === item.type ? 'toolAct' : ''"
      :title="item.name"
      @click="operation(item)"
    >
      <img :src="getUrl(item)" />
      <!-- <div class="tool-item-img" :style="{'background-image':`url(${activeVal == item.type?item.activeUrl:item.url})`}"></div> -->
    </div>
    <div v-show="toolShow" class="btns-statistics-container">
      <div class="btns-statistics-title">
        <div class="title-container">
          <div class="title-name">{{ toolTitle }}</div>
          <Close class="icon-close" @click="close" />
        </div>
      </div>
      <div class="btns-statistics-info">
        <LiveMonitoring v-if="activeVal == 'skgcsjcxtj'"></LiveMonitoring>
        <MarineMeteorology v-if="activeVal == 'hyqxgcdmtt'"></MarineMeteorology>
        <ReAnalysis v-if="activeVal == 'zfxsjcxtj'"></ReAnalysis>
        <NumericalPrediction
          v-if="activeVal == 'szybcxjc'"
        ></NumericalPrediction>
      </div>
    </div>
    <!-- <MapScreenshot v-if="imgFlag" @close="closeImg"></MapScreenshot> -->
    <AdvancedScreenshot v-if="imgFlag" @close="closeImg"></AdvancedScreenshot>
  </div>
</template>

<script setup lang="ts">
import { Close } from '@vicons/ionicons5'
import {
  LiveMonitoring,
  MarineMeteorology,
  ReAnalysis,
  NumericalPrediction,
  MapScreenshot,
  AdvancedScreenshot
} from './components/index'
import eventBus from 'src/utils/eventBus'
import { ref, onMounted, onBeforeUnmount } from 'vue'

const activeVal = ref('')
const toolList = ref<any>([
  {
    id: 'aa',
    type: 'skgcsjcxtj',
    name: '实况观测数据查询统计',
    url: '/src/assets/images/tools/18.png',
    activeUrl: '/src/assets/images/tools/active18.png'
  },
  {
    id: 'bb',
    type: 'hyqxgcdmtt',
    name: '海洋气象观测地面填图',
    url: '/src/assets/images/tools/19.png',
    activeUrl: '/src/assets/images/tools/active19.png'
  },
  {
    id: 'cc',
    type: 'zfxsjcxtj',
    name: '再分析数据查询统计',
    url: '/src/assets/images/tools/20.png',
    activeUrl: '/src/assets/images/tools/active20.png'
  }
  // {
  //   id: 'dd',
  //   type: 'szybcxjc',
  //   name: '数值预报查询检索',
  //   url: '/src/assets/images/tools/21.png',
  //   activeUrl: '/src/assets/images/tools/active21.png'
  // }
  // {
  //   id: 'ee',
  //   type: 'jt',
  //   name: '截图',
  //   url: '/src/assets/images/tools/14.png',
  //   activeUrl: '/src/assets/images/tools/active14.png'
  // }
])
// const getMap = inject<(map: any) => void>('getMap')
const req1 = import.meta.glob('src/assets/images/tools/*.*', { eager: true })
const req: any = { ...req1 }
let imagesMap: any = {}
// 循环所有图片，将图片名设置成键，值为导入该图片的地址
for (const key in req) {
  // let name = key.replace(/(\.\/images\/|\..*)/g, '')
  let name = key.split('/').slice(-1)[0].split('.')[0]

  // 抛出图片大对象后，文件页面直接引入后将图片的具体名称作为属性就能导入该图片
  imagesMap[name] = req[key].default
}

function getUrl(item: any) {
  let name = item.url.split('/').slice(-1)[0].split('.')[0]
  if (activeVal.value === item.type) {
    name = item.activeUrl.split('/').slice(-1)[0].split('.')[0]
  }
  return imagesMap[name]
}
function operation(item: any) {
  if (item.type === activeVal.value) {
    activeVal.value = ''
    toolShow.value = false
  } else {
    activeVal.value = item.type
    toolTitle.value = item.name
    toolShow.value = true
    if (item.type === 'jt') {
      imgFlag.value = true
    }
  }

  eventBus.emit('toolClick')
}
const toolShow = ref(false)
const toolTitle = ref('')
function close() {
  activeVal.value = ''
  toolShow.value = false
}
/** 截图部分 */
const imgFlag = ref(true)
function closeImg() {
  imgFlag.value = false
  activeVal.value = ''
  console.log('截图结束')
}

onMounted(() => {
  eventBus.on('analysisClick', () => {
    console.log(123)
    close()
  })
})
onBeforeUnmount(() => {
  eventBus.off('analysisClick')
})
</script>

<style lang="scss" scoped>
.btns-statistics {
  position: absolute;
  bottom: 75px;
  left: 10px;
  z-index: 99;
  background: #fff;
  border-radius: 8px;
  .btns-statistics-item {
    width: 35px;
    height: 35px;
    border-bottom: 1px solid #dfdfdf;
    &:nth-last-child(1) {
      border: none;
    }
    .btns-statistics-item-img,
    img {
      width: 100%;
      height: 100%;
      background-size: 100% 100%;
    }
  }
  .layer-control {
    box-sizing: border-box;
    padding: 4px;
  }
}
.btns-statistics-container {
  position: absolute;
  bottom: 0;
  left: 41px;
  width: 390px;
  .btns-statistics-title {
    width: 100%;
    height: 60px;
    background: url('src/assets/images/common/dialog-head-bg.png') no-repeat;
    background-size: 100% 100%;
    .title-container {
      display: flex;
      align-items: end;
      height: 33px;
      margin-left: 34px;
      margin-right: 22px;
      justify-content: space-between;
      .title-name {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 16px;
        color: #104cc0;
        line-height: 19px;
      }
    }

    .icon-close {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
  }
  .btns-statistics-info {
    padding: 10px 15px 10px 15px;
    background: #fff;
    min-height: 520px;
  }
}
</style>
