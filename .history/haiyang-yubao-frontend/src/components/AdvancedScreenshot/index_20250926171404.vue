<template>
  <div></div>
</template>

<script setup>
import {
  onMounted,
  ref,
  onUnmounted,
  getCurrentInstance,
  inject,
  nextTick
} from 'vue'

defineOptions({
  name: 'AdvancedScreenshot'
})

const getOverallMap = inject('getMap')
const { proxy } = getCurrentInstance()
const emit = defineEmits(['close'])
let eg = null
// 截图功能
function initExportImg() {
  eg = new window.ExportGif({
    el: '#vite-app', // 容器名称
    croppingTagName: 'canvas', // 裁剪对象查询名称
    btns: ['pic'],
    placement: 'bottom-left',
    width: '500px',
    height: '400px',
    left: '500px'
  })
  nextTick(() => {
    const dom = document.getElementsByClassName('export-rect')[0]
    dom.style.left = 900 + 'px'
  })
  const selectColorFeature = document.createElement('div')
  selectColorFeature.classList.add('select-title-color')
  Object.assign(selectColorFeature.style, {
    marginBottom: '10px'
  })

  const thematicMapFeature = document.createElement('label')
  thematicMapFeature.classList.add('select-title-color')
  Object.assign(thematicMapFeature.style, {
    flex: 1,
    padding: '10px'
  })

  const inputElements = document.getElementsByTagName('input')
  for (var i = 0; i < inputElements.length; i++) {
    inputElements[i].style.webkitAppearance = 'radio'
  }

  eg.bindEvent('beforeCreatePicCanvas', beforeCreatePicCanvas)
  eg.bindEvent('beforeExportPic', beforeExportPic)
  eg.bindEvent('exportPicDone', exportPicDone)
  eg.bindEvent('close', close)
  eg.open()
}
function beforeCreatePicCanvas() {
  if (window.map) window.map.render()
}
function beforeExportPic(data) {
  const titleColor = '#fff'
  const { ctx, scale, width, height, next } = data
  let layerTitle = ''
  ctx.textBaseline = 'middle'
  getOverallMap(map => {
    const layers = map.getAllLayers()
    layers.forEach(item => {
      const propertiers = item.getProperties()
      if (propertiers.type === 'product') {
        layerTitle = item.getProperties().title
      }
    })
    // 添加产品标题
    const text = layerTitle

    ctx.font = `${scale * 20}px Nexa-XBold`
    const {
      actualBoundingBoxAscent: titleActualBoundingBoxAscent,
      actualBoundingBoxDescent: titleActualBoundingBoxDescent,
      actualBoundingBoxRight: titleActualBoundingBoxRight,
      actualBoundingBoxLeft: titleActualBoundingBoxLeft
    } = ctx.measureText(text)

    const titleFontHeight =
      titleActualBoundingBoxAscent + titleActualBoundingBoxDescent
    const titleWidth = titleActualBoundingBoxRight + titleActualBoundingBoxLeft
    ctx.fillStyle = '#525252'
    ctx.fillRect(
      (width - titleWidth / scale) * scale,
      6 * scale,
      titleWidth,
      (titleFontHeight + 5) * scale
    )

    ctx.font = `${scale * 20}px Nexa-XBold`
    ctx.fillStyle = titleColor
    ctx.fillText(
      text,
      (width - titleWidth / scale) * scale,
      (10 + titleFontHeight / 2) * scale
    )

    // 添加图例
    const legendList = [proxy.$parent.legendImage]
    const leftBottomY = height * scale - 10
    const legendNameOffsetX = 10
    const imgPromiseList = []
    if (legendList.length > 0) {
      for (let i = 0; i < legendList.length; i++) {
        const legendPromise = new Promise((resolve, reject) => {
          const imgLegend = new Image()
          imgLegend.src = legendList[i]
          imgLegend.crossOrigin = 'anonymous'
          imgLegend.onload = function () {
            const imgWidth = imgLegend.width
            const imgHeight = imgLegend.height
            const baseWidth = 200
            const scaleHeight = (baseWidth / imgWidth) * imgHeight
            let imgY = leftBottomY - scaleHeight
            const imgX = legendNameOffsetX + 10

            ctx.fillStyle = 'rgba(255,255,255,0.72)'
            ctx.fillRect(imgX, imgY, baseWidth, (scaleHeight + 5) * scale) // 添加图例背景颜色

            ctx.drawImage(this, imgX, imgY, baseWidth, scaleHeight) // 添加图例
            imgY += imgY
            resolve()
          }

          imgLegend.onerror = function () {
            reject()
          }
        })
        imgPromiseList.push(legendPromise)
      }
    }

    Promise.allSettled(imgPromiseList)
      .then(() => {})
      .catch(() => {})
      .finally(() => {
        next()
        unbindEvents()
      })
      .catch(e => {
        console.log(e)
      })
  })
}
function exportPicDone() {
  // unbindEvents()
}
function unbindEvents() {
  eg && eg.unbindEvent('beforeCreatePicCanvas', beforeCreatePicCanvas)
  eg && eg.unbindEvent('beforeExportPic', beforeExportPic)
  eg && eg.unbindEvent('exportPicDone', exportPicDone)
  eg && eg.unbindEvent('close', close)
  // eg && eg.destory()
  console.log(111111111)
  emit('close')
}
function close() {
  // unbindEvents()
  emit('close')
}
onMounted(() => {
  initExportImg()
})
onUnmounted(() => {
  eg && eg.destory()
  unbindEvents()
})
</script>

<style lang="scss" scoped></style>
