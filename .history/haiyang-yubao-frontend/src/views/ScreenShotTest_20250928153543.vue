<template>
  <div class="screenshot-test-page">
    <div class="page-header">
      <h1>🖼️ 自定义选区截图功能测试</h1>
      <p>这是一个用于测试自定义选区截图功能的演示页面</p>

      <!-- 版本切换标签 -->
      <div class="version-tabs">
        <button
          class="tab-btn"
          :class="{ active: activeVersion === 'v1' }"
          @click="switchVersion('v1')"
        >
          V1 基础版本
        </button>
        <button
          class="tab-btn"
          :class="{ active: activeVersion === 'v2' }"
          @click="switchVersion('v2')"
        >
          V2 编辑版本
        </button>
      </div>

      <div class="action-buttons">
        <button
          class="btn btn-primary"
          :disabled="isCapturing"
          @click="handleStartScreenShot"
        >
          {{ isCapturing ? '正在截图...' : '🚀 开始截图' }}
        </button>

        <button class="btn btn-secondary" @click="showDemo = !showDemo">
          {{ showDemo ? '隐藏演示' : '显示演示' }}
        </button>
      </div>
    </div>

    <!-- 演示内容区域 -->
    <div v-if="showDemo" class="demo-content">
      <div class="content-grid">
        <div class="demo-card">
          <h3>📊 数据展示</h3>
          <p>这里是一些模拟的数据内容，可以用来测试截图效果。</p>
          <div class="demo-chart">
            <div
              class="chart-bar"
              style="height: 60%; background: #ff6b6b"
            ></div>
            <div
              class="chart-bar"
              style="height: 80%; background: #4ecdc4"
            ></div>
            <div
              class="chart-bar"
              style="height: 40%; background: #45b7d1"
            ></div>
            <div
              class="chart-bar"
              style="height: 90%; background: #96ceb4"
            ></div>
            <div
              class="chart-bar"
              style="height: 70%; background: #feca57"
            ></div>
          </div>
        </div>

        <div class="demo-card">
          <h3>🎨 设计元素</h3>
          <p>包含各种颜色和样式的内容，用于验证截图的色彩还原度。</p>
          <div class="color-palette">
            <div class="color-item" style="background: #ff6b6b"></div>
            <div class="color-item" style="background: #4ecdc4"></div>
            <div class="color-item" style="background: #45b7d1"></div>
            <div class="color-item" style="background: #96ceb4"></div>
            <div class="color-item" style="background: #feca57"></div>
            <div class="color-item" style="background: #ff9ff3"></div>
          </div>
        </div>

        <div class="demo-card">
          <h3>📝 文本内容</h3>
          <p>
            这是一段测试文本，包含了<strong>粗体</strong>、<em>斜体</em>和<u>下划线</u>等样式。
          </p>
          <blockquote>
            "这是一个引用文本示例，用于测试截图功能的文本渲染效果。"
          </blockquote>
          <ul>
            <li>✅ 支持自定义选区</li>
            <li>✅ 支持拖拽移动</li>
            <li>✅ 支持调整大小</li>
            <li>✅ 支持多种格式</li>
          </ul>
        </div>

        <div class="demo-card">
          <h3>🔧 功能配置</h3>
          <div class="config-section">
            <div class="config-item">
              <label>图片质量:</label>
              <input
                v-model.number="config.quality"
                type="range"
                min="0.1"
                max="1"
                step="0.1"
                class="quality-slider"
              />
              <span>{{ ((config.quality || 0.9) * 100).toFixed(0) }}%</span>
            </div>

            <div class="config-item">
              <label>默认格式:</label>
              <select v-model="config.defaultFormat" class="format-select">
                <option value="png">PNG</option>
                <option value="jpeg">JPEG</option>
                <option value="webp">WebP</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="instructions">
        <h4>📋 使用说明</h4>
        <div class="instruction-grid">
          <div class="instruction-item">
            <div class="step-number">1</div>
            <div class="step-content">
              <h5>启动截图</h5>
              <p>点击"开始截图"按钮启动截图模式</p>
            </div>
          </div>

          <div class="instruction-item">
            <div class="step-number">2</div>
            <div class="step-content">
              <h5>选择区域</h5>
              <p>拖拽选择要截图的区域</p>
            </div>
          </div>

          <div class="instruction-item">
            <div class="step-number">3</div>
            <div class="step-content">
              <h5>调整选区</h5>
              <p>通过角落手柄调整大小，拖拽内部移动位置</p>
            </div>
          </div>

          <div class="instruction-item">
            <div class="step-number">4</div>
            <div class="step-content">
              <h5>保存图片</h5>
              <p>选择格式后点击保存按钮</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 截图结果显示 -->
    <div v-if="lastResult && hasResultData(lastResult)" class="result-section">
      <h4>📸 最后截图结果</h4>
      <div class="result-info">
        <p><strong>格式:</strong> {{ lastResult.format.toUpperCase() }}</p>
        <p>
          <strong>尺寸:</strong> {{ lastResult.selection.width }} ×
          {{ lastResult.selection.height }}
        </p>
        <p>
          <strong>位置:</strong> ({{ lastResult.selection.x }},
          {{ lastResult.selection.y }})
        </p>
        <p v-if="isV2Result(lastResult)">
          <strong>包含编辑:</strong> {{ lastResult.hasEdits ? '是' : '否' }}
        </p>
      </div>
    </div>

    <!-- V1截图覆盖层 -->
    <ScreenShotOverlay
      v-if="activeVersion === 'v1'"
      :is-active="isActive"
      :is-capturing="isCapturing"
      :selection="selection"
      :selected-format="selectedFormat"
      @mousedown="handleMouseDown"
      @cancel="cancelScreenShot"
      @save="handleSaveScreenShot"
      @format-change="setImageFormat"
    />

    <!-- V2截图覆盖层 -->
    <ScreenShotOverlayV2
      v-if="activeVersion === 'v2'"
      :is-active="isActiveV2"
      :is-capturing="isCapturingV2"
      :selection="selectionV2"
      :drag-state="dragStateV2"
      :edit-state="editStateV2"
      :selected-format="selectedFormatV2"
      @mousedown="handleMouseDownV2"
      @cancel="cancelScreenShotV2"
      @save="handleSaveScreenShotV2"
      @set-tool="setCurrentToolV2"
      @undo="undoV2"
      @redo="redoV2"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import {
  useScreenShot,
  ScreenShotOverlay
} from 'src/utils/vue-hooks/useScreenShot/V1'
import {
  useScreenShotV2,
  ScreenShotOverlayV2
} from 'src/utils/vue-hooks/useScreenShot/V2'
import type {
  ScreenShotOptions,
  ScreenShotResult
} from 'src/utils/vue-hooks/useScreenShot/V1'
import type { ScreenShotV2Result } from 'src/utils/vue-hooks/useScreenShot/V2'

// 当前激活的版本
const activeVersion = ref<'v1' | 'v2'>('v2') // 默认显示V2版本

// 显示演示内容
const showDemo = ref(true)

// V1配置选项
const config = reactive<ScreenShotOptions>({
  quality: 0.9,
  defaultFormat: 'png',
  allowMove: true,
  allowResize: true,
  minSize: { width: 50, height: 50 }
})

// 最后的截图结果
const lastResult = ref<ScreenShotResult | ScreenShotV2Result | null>(null)

// 类型守卫函数
const hasResultData = (
  result: ScreenShotResult | ScreenShotV2Result
): result is ScreenShotResult => {
  return 'format' in result && 'selection' in result
}

const isV2Result = (
  result: ScreenShotResult | ScreenShotV2Result
): result is ScreenShotV2Result => {
  return 'hasEdits' in result || 'editHistory' in result
}

// V1截图Hook
const {
  isActive,
  isCapturing,
  selection,
  selectedFormat,
  startScreenShot,
  cancelScreenShot,
  saveScreenShot,
  setImageFormat,
  handleMouseDown
} = useScreenShot(config)

// V2截图Hook
const {
  isActive: isActiveV2,
  isCapturing: isCapturingV2,
  selection: selectionV2,
  selectedFormat: selectedFormatV2,
  dragState: dragStateV2,
  editState: editStateV2,
  startScreenShot: startScreenShotV2,
  cancelScreenShot: cancelScreenShotV2,
  saveScreenShot: saveScreenShotV2,
  setCurrentTool: setCurrentToolV2,
  undo: undoV2,
  redo: redoV2,
  handleMouseDown: handleMouseDownV2
} = useScreenShotV2({
  enableEdit: true,
  defaultTool: 'select',
  availableTools: ['select', 'brush', 'rectangle', 'arrow', 'text', 'mosaic'],
  enableUndo: true,
  maxUndoSteps: 50
})

/**
 * 切换版本
 */
const switchVersion = (version: 'v1' | 'v2'): void => {
  // 如果当前有活跃的截图，先取消
  if (activeVersion.value === 'v1' && isActive.value) {
    cancelScreenShot()
  } else if (activeVersion.value === 'v2' && isActiveV2.value) {
    cancelScreenShotV2()
  }

  activeVersion.value = version
  lastResult.value = null
}

/**
 * 开始截图
 */
const handleStartScreenShot = async (): Promise<void> => {
  try {
    if (activeVersion.value === 'v1') {
      await startScreenShot()
    } else {
      await startScreenShotV2()
    }
  } catch (error) {
    console.error('启动截图失败:', error)
    alert('截图失败，请重试')
  }
}

/**
 * V1保存截图
 */
const handleSaveScreenShot = async (): Promise<void> => {
  try {
    const result = await saveScreenShot()
    lastResult.value = result
    console.log('V1截图保存成功:', result)
  } catch (error) {
    console.error('保存截图失败:', error)
    alert('保存失败，请重试')
  }
}

/**
 * V2保存截图
 */
const handleSaveScreenShotV2 = async (): Promise<void> => {
  try {
    const result = await saveScreenShotV2()
    lastResult.value = result
    console.log('V2截图保存成功:', result)
  } catch (error) {
    console.error('保存截图失败:', error)
    alert('保存失败，请重试')
  }
}
</script>

<style scoped lang="scss">
.screenshot-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  h1 {
    color: #333;
    margin-bottom: 10px;
    font-size: 2.5rem;
  }

  p {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 20px;
  }
}

.version-tabs {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 20px;
  background: #f8f9fa;
  padding: 4px;
  border-radius: 8px;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
}

.tab-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
  color: #6c757d;

  &:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
  }

  &.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  }
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    &:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
    }
  }

  &.btn-secondary {
    background: #f8f9fa;
    color: #495057;
    border: 2px solid #dee2e6;

    &:hover {
      background: #e9ecef;
      border-color: #adb5bd;
    }
  }
}

.demo-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.demo-card {
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;

  h3 {
    margin-top: 0;
    color: #495057;
    font-size: 1.3rem;
  }

  p {
    color: #6c757d;
    line-height: 1.6;
  }
}

.demo-chart {
  display: flex;
  align-items: end;
  gap: 8px;
  height: 100px;
  margin-top: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
}

.chart-bar {
  flex: 1;
  border-radius: 4px 4px 0 0;
  min-height: 20px;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.color-palette {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  flex-wrap: wrap;
}

.color-item {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
}

blockquote {
  border-left: 4px solid #667eea;
  padding-left: 15px;
  margin: 15px 0;
  font-style: italic;
  color: #6c757d;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 0 8px 8px 0;
}

ul {
  list-style: none;
  padding: 0;

  li {
    padding: 5px 0;
    color: #495057;
  }
}

.config-section {
  margin-top: 15px;
}

.config-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 10px;

  label {
    min-width: 80px;
    color: #495057;
    font-weight: 500;
  }
}

.quality-slider {
  flex: 1;
  margin: 0 10px;
}

.format-select {
  padding: 6px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  background: #fff;
}

.instructions {
  border-top: 2px solid #e9ecef;
  padding-top: 30px;

  h4 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.4rem;
  }
}

.instruction-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.instruction-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.step-number {
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.step-content {
  h5 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 1.1rem;
  }

  p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.4;
  }
}

.result-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

  h4 {
    color: #495057;
    margin-bottom: 15px;
  }
}

.result-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  p {
    margin: 8px 0;
    color: #495057;
    font-size: 0.95rem;
  }
}
</style>
