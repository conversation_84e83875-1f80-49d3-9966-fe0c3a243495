###
 # @Author: x<PERSON><PERSON><PERSON> <EMAIL>
 # @Date: 2024-10-11 11:07:21
 # @LastEditors: xuli<PERSON>e <EMAIL>
 # @LastEditTime: 2025-04-29 14:34:04
 # @FilePath: \hainan-jianzai-web\.env.development
 # @Description: 
 # 
 # Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
### 
VITE_Base_Url='/'
VITE_Busi_Url=''

VITE_Host_Url='http://**********:8989'

### security-passport-server
VITE_AUTH_BASE_URL='/api/auth'
VITE_AUTH_BASE_API_URL='http://**********:26081'

### sys-api-server
VITE_SYS_BASE_URL='/api/basic'
VITE_SYS_BASE_API_URL='http://**********:23080'

### sys-admin-api
VITE_SYS_ADMIN_URL='/api/sysadmin'
VITE_SYS_ADMIN_API_URL='http://**********:25080'

### data-scrap-api
VITE_DATA_SCRAP_BASE_URL="/api/dataScrap"
VITE_DATA_SCRAP_BASE_API_URL='http://**********:28080'

### product-push-api
VITE_PRODUCT_PUSH_BASE_URL="/api/push"
VITE_PRODUCT_PUSH_BASE_API_URL='http://**********:24080'

### security-ucenter-server
VITE_PERS_BASE_URL="/api/pers"
VITE_PERS_BASE_API_URL="http://**********:26080"

VITE_MONITOR_BASE_URL="/api/monitor"
VITE_MONITOR_BASE_API_URL="http://**********:8091"

### schedule-api-server
VITE_DISPATCH_BASE_URL='/api/schedule'
VITE_DISPATCH_BASE_API_URL='http://**********:38080'

###product-push-api
VITE_BASE_PRODUCT_URL = '/api/push'
VITE_BASE_PRODUCT_API_URL = "http://***********1/push/api/push"

###data-scrap-mail
VITE_BASE_EMAIL_URL = '/api/mail'
VITE_BASE_EMAIL_API_URL = 'http://***********1/push/api/mail'

VITE_FORECAST_BASE_URL = '/api/data'
VITE_FORECAST_BASE_API_URL='http://10.2.27.58:8080'
# VITE_FORECAST_BASE_API_URL='http://**********:38081'

##'http://**********:38081'

VITE_PRODUCT_BASE_URL = '/api/product'
VITE_PRODUCT_BASE_API_URL='http://**********:8088'

VITE_TIF_BASE_API_URL='http://**********'


VITE_ETTP_SERVICE_BASE_API ='/api/meteo_ettp'
VITE_ETTP_SERVICE_BASE_API_URL='http://10.2.25.241:8675'

VITE_ETTP_BASE_API ='/api/ettp'
VITE_ETTP_BASE_API_URL='http://**********:38280'

