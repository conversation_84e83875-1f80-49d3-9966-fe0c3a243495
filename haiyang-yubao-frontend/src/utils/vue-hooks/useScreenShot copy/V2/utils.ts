import { snapdom } from '@zumer/snapdom';
import type { 
  ImageFormat, 
  SelectionArea, 
  EditOperation, 
  BrushPath, 
  RectangleData, 
  ArrowData, 
  TextData, 
  MosaicData,
  DrawPoint
} from './types';

/**
 * V2版本截图工具函数
 * 继承V1功能并添加编辑功能
 */
export class ScreenShotV2Utils {
  /**
   * 将页面转换为Canvas
   * @param element 要截图的元素，默认为document.body
   * @param options snapdom配置选项
   * @returns Promise<HTMLCanvasElement>
   */
  static async captureElement(
    element: HTMLElement = document.body,
    options: any = {}
  ): Promise<HTMLCanvasElement> {
    const defaultOptions = {
      scale: window.devicePixelRatio || 1,
      width: window.innerWidth,
      height: window.innerHeight,
      backgroundColor: '#ffffff',
      ...options
    };

    try {
      // 使用snapdom进行截图
      const result = await snapdom(element, defaultOptions);
      // snapdom返回的是CaptureResult对象，使用toCanvas方法获取canvas
      return result.toCanvas();
    } catch (error) {
      console.error('snapdom截图失败:', error);
      throw new Error('页面截图失败，请重试');
    }
  }

  /**
   * 裁剪Canvas指定区域
   * @param sourceCanvas 源Canvas
   * @param selection 选区信息
   * @returns HTMLCanvasElement 裁剪后的Canvas
   */
  static cropCanvas(sourceCanvas: HTMLCanvasElement, selection: SelectionArea): HTMLCanvasElement {
    const { x, y, width, height } = selection;
    
    // 创建新的Canvas用于存放裁剪结果
    const croppedCanvas = document.createElement('canvas');
    const ctx = croppedCanvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('无法获取Canvas 2D上下文');
    }

    // 设置裁剪后的Canvas尺寸
    croppedCanvas.width = width;
    croppedCanvas.height = height;

    // 从源Canvas裁剪指定区域到新Canvas
    ctx.drawImage(
      sourceCanvas,
      x, y, width, height,  // 源区域
      0, 0, width, height   // 目标区域
    );

    return croppedCanvas;
  }

  /**
   * 将Canvas转换为指定格式的DataURL
   * @param canvas Canvas元素
   * @param format 图片格式
   * @param quality 图片质量 (0-1)
   * @returns string DataURL
   */
  static canvasToDataURL(
    canvas: HTMLCanvasElement,
    format: ImageFormat = 'png',
    quality: number = 0.9
  ): string {
    const mimeType = `image/${format}`;
    return canvas.toDataURL(mimeType, quality);
  }

  /**
   * 下载图片
   * @param dataUrl 图片的DataURL
   * @param filename 文件名
   */
  static downloadImage(dataUrl: string, filename: string = 'screenshot'): void {
    const link = document.createElement('a');
    link.download = filename;
    link.href = dataUrl;
    
    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  /**
   * 限制选区在指定范围内
   * @param selection 选区信息
   * @param containerWidth 容器宽度
   * @param containerHeight 容器高度
   * @returns SelectionArea 修正后的选区
   */
  static constrainSelection(
    selection: SelectionArea,
    containerWidth: number,
    containerHeight: number
  ): SelectionArea {
    const { x, y, width, height } = selection;

    // 确保选区不超出容器边界
    const constrainedX = Math.max(0, Math.min(x, containerWidth - width));
    const constrainedY = Math.max(0, Math.min(y, containerHeight - height));
    const constrainedWidth = Math.min(width, containerWidth - constrainedX);
    const constrainedHeight = Math.min(height, containerHeight - constrainedY);

    return {
      x: constrainedX,
      y: constrainedY,
      width: Math.max(1, constrainedWidth),
      height: Math.max(1, constrainedHeight)
    };
  }

  /**
   * 检查点是否在矩形区域内
   * @param point 点坐标
   * @param rect 矩形区域
   * @returns boolean
   */
  static isPointInRect(
    point: { x: number; y: number },
    rect: SelectionArea
  ): boolean {
    return (
      point.x >= rect.x &&
      point.x <= rect.x + rect.width &&
      point.y >= rect.y &&
      point.y <= rect.y + rect.height
    );
  }

  /**
   * 获取调整大小的手柄区域
   * @param selection 选区信息
   * @param handleSize 手柄大小
   * @returns 手柄区域对象
   */
  static getResizeHandles(selection: SelectionArea, handleSize: number = 8) {
    const { x, y, width, height } = selection;
    
    return {
      topLeft: {
        x: x - handleSize / 2,
        y: y - handleSize / 2,
        width: handleSize,
        height: handleSize
      },
      topCenter: {
        x: x + width / 2 - handleSize / 2,
        y: y - handleSize / 2,
        width: handleSize,
        height: handleSize
      },
      topRight: {
        x: x + width - handleSize / 2,
        y: y - handleSize / 2,
        width: handleSize,
        height: handleSize
      },
      middleLeft: {
        x: x - handleSize / 2,
        y: y + height / 2 - handleSize / 2,
        width: handleSize,
        height: handleSize
      },
      middleRight: {
        x: x + width - handleSize / 2,
        y: y + height / 2 - handleSize / 2,
        width: handleSize,
        height: handleSize
      },
      bottomLeft: {
        x: x - handleSize / 2,
        y: y + height - handleSize / 2,
        width: handleSize,
        height: handleSize
      },
      bottomCenter: {
        x: x + width / 2 - handleSize / 2,
        y: y + height - handleSize / 2,
        width: handleSize,
        height: handleSize
      },
      bottomRight: {
        x: x + width - handleSize / 2,
        y: y + height - handleSize / 2,
        width: handleSize,
        height: handleSize
      }
    };
  }

  /**
   * 生成唯一ID
   * @returns string 唯一ID
   */
  static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 计算两点之间的距离
   * @param point1 点1
   * @param point2 点2
   * @returns number 距离
   */
  static getDistance(point1: DrawPoint, point2: DrawPoint): number {
    const dx = point1.x - point2.x;
    const dy = point1.y - point2.y;
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * 绘制画笔路径
   * @param ctx Canvas上下文
   * @param path 画笔路径
   */
  static drawBrushPath(ctx: CanvasRenderingContext2D, path: BrushPath): void {
    if (path.points.length < 2) return;

    ctx.save();
    ctx.globalAlpha = path.config.opacity;
    ctx.strokeStyle = path.config.color;
    ctx.lineWidth = path.config.size;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    ctx.beginPath();
    ctx.moveTo(path.points[0].x, path.points[0].y);

    for (let i = 1; i < path.points.length; i++) {
      ctx.lineTo(path.points[i].x, path.points[i].y);
    }

    ctx.stroke();
    ctx.restore();
  }

  /**
   * 绘制矩形
   * @param ctx Canvas上下文
   * @param rect 矩形数据
   */
  static drawRectangle(ctx: CanvasRenderingContext2D, rect: RectangleData): void {
    ctx.save();
    
    // 绘制填充
    if (rect.config.fillOpacity > 0) {
      ctx.globalAlpha = rect.config.fillOpacity;
      ctx.fillStyle = rect.config.fillColor;
      ctx.fillRect(rect.x, rect.y, rect.width, rect.height);
    }

    // 绘制边框
    ctx.globalAlpha = 1;
    ctx.strokeStyle = rect.config.strokeColor;
    ctx.lineWidth = rect.config.strokeWidth;
    ctx.strokeRect(rect.x, rect.y, rect.width, rect.height);
    
    ctx.restore();
  }

  /**
   * 绘制箭头
   * @param ctx Canvas上下文
   * @param arrow 箭头数据
   */
  static drawArrow(ctx: CanvasRenderingContext2D, arrow: ArrowData): void {
    const { startX, startY, endX, endY, config } = arrow;
    const angle = Math.atan2(endY - startY, endX - startX);
    const headLength = config.headSize;

    ctx.save();
    ctx.strokeStyle = config.color;
    ctx.fillStyle = config.color;
    ctx.lineWidth = config.width;
    ctx.lineCap = 'round';

    // 绘制箭头线
    ctx.beginPath();
    ctx.moveTo(startX, startY);
    ctx.lineTo(endX, endY);
    ctx.stroke();

    // 绘制箭头头部
    ctx.beginPath();
    ctx.moveTo(endX, endY);
    ctx.lineTo(
      endX - headLength * Math.cos(angle - Math.PI / 6),
      endY - headLength * Math.sin(angle - Math.PI / 6)
    );
    ctx.moveTo(endX, endY);
    ctx.lineTo(
      endX - headLength * Math.cos(angle + Math.PI / 6),
      endY - headLength * Math.sin(angle + Math.PI / 6)
    );
    ctx.stroke();

    ctx.restore();
  }

  /**
   * 绘制文本
   * @param ctx Canvas上下文
   * @param text 文本数据
   */
  static drawText(ctx: CanvasRenderingContext2D, text: TextData): void {
    ctx.save();
    ctx.fillStyle = text.config.color;
    ctx.font = `${text.config.fontWeight} ${text.config.fontSize}px ${text.config.fontFamily}`;
    ctx.textBaseline = 'top';
    ctx.fillText(text.text, text.x, text.y);
    ctx.restore();
  }

  /**
   * 应用马赛克效果
   * @param ctx Canvas上下文
   * @param mosaic 马赛克数据
   */
  static applyMosaic(ctx: CanvasRenderingContext2D, mosaic: MosaicData): void {
    const { x, y, width, height, config } = mosaic;
    const blockSize = config.blockSize;

    // 获取原始图像数据
    const imageData = ctx.getImageData(x, y, width, height);
    const data = imageData.data;

    // 应用马赛克效果
    for (let blockY = 0; blockY < height; blockY += blockSize) {
      for (let blockX = 0; blockX < width; blockX += blockSize) {
        // 计算块的平均颜色
        let r = 0, g = 0, b = 0, count = 0;
        
        for (let py = blockY; py < Math.min(blockY + blockSize, height); py++) {
          for (let px = blockX; px < Math.min(blockX + blockSize, width); px++) {
            const index = (py * width + px) * 4;
            r += data[index];
            g += data[index + 1];
            b += data[index + 2];
            count++;
          }
        }

        r = Math.floor(r / count);
        g = Math.floor(g / count);
        b = Math.floor(b / count);

        // 将平均颜色应用到整个块
        for (let py = blockY; py < Math.min(blockY + blockSize, height); py++) {
          for (let px = blockX; px < Math.min(blockX + blockSize, width); px++) {
            const index = (py * width + px) * 4;
            data[index] = r;
            data[index + 1] = g;
            data[index + 2] = b;
          }
        }
      }
    }

    // 将处理后的图像数据放回Canvas
    ctx.putImageData(imageData, x, y);
  }
}
