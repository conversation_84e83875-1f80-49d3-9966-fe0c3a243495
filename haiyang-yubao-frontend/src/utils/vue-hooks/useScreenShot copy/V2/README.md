# 自定义选区截图功能 V2 版本

V2版本是在V1基础版本上扩展的高级编辑版本，包含了丰富的编辑工具和功能。

## 🎯 主要特性

### 基础功能（继承自V1）
- ✅ 自定义选区截图
- ✅ 拖拽移动选区
- ✅ 8个方向调整选区大小
- ✅ 多种图片格式支持（PNG、JPEG、WebP）
- ✅ 高清晰度截图（支持高DPI屏幕）
- ✅ 自动文件下载

### 新增编辑功能
- 🎨 **画笔工具**：自由绘制，支持颜色、粗细、透明度调节
- 📐 **矩形工具**：绘制矩形框，支持边框和填充样式
- ➡️ **箭头工具**：绘制指向箭头，支持颜色和粗细调节
- 📝 **文本工具**：添加文本标注，支持字体、大小、颜色设置
- 🔲 **马赛克工具**：对敏感区域进行马赛克处理
- ↩️ **撤销/重做**：支持无限次撤销和重做操作

### 用户界面
- 🎯 **遮罩层效果**：选区外半透明遮罩
- 🛠️ **底部工具栏**：现代化的工具选择界面
- 📏 **选区信息显示**：实时显示选区尺寸
- 🎮 **调整手柄**：8个方向的可视化调整手柄

## 📦 安装和使用

### 基本使用

```vue
<template>
  <div>
    <!-- 触发截图的按钮 -->
    <button @click="startScreenShot">开始截图</button>
    
    <!-- V2截图覆盖层 -->
    <ScreenShotOverlayV2
      :is-active="isActive"
      :is-capturing="isCapturing"
      :selection="selection"
      :drag-state="dragState"
      :edit-state="editState"
      :selected-format="selectedFormat"
      @mousedown="handleMouseDown"
      @cancel="cancelScreenShot"
      @save="saveScreenShot"
      @set-tool="setCurrentTool"
      @undo="undo"
      @redo="redo"
    />
  </div>
</template>

<script setup lang="ts">
import { useScreenShotV2, ScreenShotOverlayV2 } from 'src/utils/vue-hooks/useScreenShot/V2';

const {
  // 状态
  isActive,
  isCapturing,
  selection,
  selectedFormat,
  dragState,
  editState,
  
  // 方法
  startScreenShot,
  cancelScreenShot,
  saveScreenShot,
  setCurrentTool,
  undo,
  redo,
  handleMouseDown
} = useScreenShotV2({
  enableEdit: true,
  defaultTool: 'select',
  availableTools: ['select', 'brush', 'rectangle', 'arrow', 'text', 'mosaic'],
  enableUndo: true,
  maxUndoSteps: 50
});
</script>
```

### 高级配置

```typescript
const screenShotOptions = {
  // 基础配置
  quality: 0.9,                    // 图片质量 (0.1-1.0)
  defaultFormat: 'png',            // 默认格式
  allowMove: true,                 // 允许移动选区
  allowResize: true,               // 允许调整大小
  minSize: { width: 50, height: 50 }, // 最小选区尺寸
  
  // V2新增配置
  enableEdit: true,                // 启用编辑功能
  defaultTool: 'select',           // 默认工具
  availableTools: [                // 可用工具列表
    'select', 'brush', 'rectangle', 
    'arrow', 'text', 'mosaic'
  ],
  enableUndo: true,                // 启用撤销/重做
  maxUndoSteps: 50,               // 最大撤销步数
  
  // 工具配置
  toolConfig: {
    brush: {
      color: '#ff0000',
      size: 3,
      opacity: 1
    },
    rectangle: {
      strokeColor: '#ff0000',
      fillColor: '#ff0000',
      strokeWidth: 2,
      fillOpacity: 0.2
    },
    arrow: {
      color: '#ff0000',
      width: 2,
      headSize: 10
    },
    text: {
      color: '#000000',
      fontSize: 16,
      fontFamily: 'Arial, sans-serif',
      fontWeight: 'normal'
    },
    mosaic: {
      blockSize: 10,
      intensity: 1
    }
  }
};

const { ... } = useScreenShotV2(screenShotOptions);
```

## 🎨 编辑工具详解

### 1. 选择工具 (select)
- 默认工具，用于移动和调整选区
- 显示8个调整手柄
- 支持拖拽移动整个选区

### 2. 画笔工具 (brush)
- 自由绘制线条
- 支持压感（如果设备支持）
- 可配置颜色、粗细、透明度

### 3. 矩形工具 (rectangle)
- 绘制矩形框
- 支持边框和填充样式
- 可配置边框颜色、填充颜色、透明度

### 4. 箭头工具 (arrow)
- 绘制指向箭头
- 支持颜色和粗细调节
- 箭头头部大小可配置

### 5. 文本工具 (text)
- 添加文本标注
- 支持字体、大小、颜色设置
- 支持多行文本

### 6. 马赛克工具 (mosaic)
- 对敏感区域进行马赛克处理
- 可配置马赛克块大小和强度
- 不可逆操作

## 🔧 API 参考

### useScreenShotV2 Hook

#### 返回值

```typescript
interface UseScreenShotV2Return {
  // 状态
  isActive: Ref<boolean>;           // 是否激活截图模式
  isCapturing: Ref<boolean>;        // 是否正在截图
  selection: SelectionArea;         // 选区信息
  selectedFormat: Ref<ImageFormat>; // 选中的图片格式
  dragState: DragState;            // 拖拽状态
  editState: EditState;            // 编辑状态
  
  // 方法
  startScreenShot(): Promise<void>;           // 开始截图
  cancelScreenShot(): void;                   // 取消截图
  saveScreenShot(): Promise<ScreenShotV2Result>; // 保存截图
  setCurrentTool(tool: EditTool): void;       // 设置当前工具
  undo(): void;                              // 撤销操作
  redo(): void;                              // 重做操作
  handleMouseDown(event: MouseEvent): void;   // 鼠标按下处理
  
  // 工具方法
  resetSelection(): void;                     // 重置选区
  finishCurrentEdit(): void;                  // 完成当前编辑
}
```

### 类型定义

```typescript
// 编辑工具类型
type EditTool = 'select' | 'brush' | 'rectangle' | 'arrow' | 'text' | 'mosaic' | 'eraser';

// 编辑状态
interface EditState {
  currentTool: EditTool;        // 当前工具
  isDrawing: boolean;           // 是否正在绘制
  currentPath: any;             // 当前路径
  history: EditOperation[];     // 操作历史
  historyIndex: number;         // 历史索引
  toolConfig: EditToolConfig;   // 工具配置
}

// 截图结果
interface ScreenShotV2Result extends ScreenShotResult {
  editHistory?: EditOperation[]; // 编辑历史
  hasEdits?: boolean;           // 是否包含编辑
}
```

## 🎯 使用场景

1. **产品截图**：为产品文档添加标注和说明
2. **Bug报告**：标记问题区域，添加箭头和文字说明
3. **教程制作**：为教程添加高亮和注释
4. **隐私保护**：对敏感信息进行马赛克处理
5. **设计评审**：在设计稿上添加修改建议

## 🔄 版本对比

| 功能 | V1 基础版 | V2 编辑版 |
|------|-----------|-----------|
| 自定义选区 | ✅ | ✅ |
| 拖拽移动 | ✅ | ✅ |
| 调整大小 | ✅ | ✅ |
| 多格式支持 | ✅ | ✅ |
| 画笔工具 | ❌ | ✅ |
| 矩形工具 | ❌ | ✅ |
| 箭头工具 | ❌ | ✅ |
| 文本工具 | ❌ | ✅ |
| 马赛克工具 | ❌ | ✅ |
| 撤销/重做 | ❌ | ✅ |
| 遮罩层 | ❌ | ✅ |
| 工具栏 | 简单 | 丰富 |

## 📝 注意事项

1. **性能考虑**：编辑操作会增加内存使用，建议限制撤销步数
2. **浏览器兼容性**：需要现代浏览器支持Canvas API
3. **文件大小**：编辑后的图片可能比原始截图更大
4. **马赛克不可逆**：马赛克操作无法通过撤销恢复原始内容

## 🚀 未来计划

- [ ] 更多编辑工具（圆形、椭圆、自由选择等）
- [ ] 图层管理功能
- [ ] 滤镜效果
- [ ] 批量编辑
- [ ] 云端保存
- [ ] 协作编辑
