/**
 * 截图功能V2版本导出文件
 * 包含编辑功能：画笔、框选、标记箭头、马赛克等
 */

// 导出类型定义
export type * from './types';

// 导出工具函数
export { ScreenShotV2Utils } from './utils';

// 导出主要Hook
export { useScreenShotV2 } from './useScreenShotV2';

// 导出组件
export { default as ScreenShotOverlayV2 } from './ScreenShotOverlayV2.vue';
export { default as ScreenShotDemoV2 } from './ScreenShotDemoV2.vue';

// 默认导出Hook
export { useScreenShotV2 as default } from './useScreenShotV2';
