/**
 * 截图功能V2版本类型定义
 * 包含编辑功能：画笔、框选、标记箭头、马赛克等
 */

// 继承V1的基础类型
export type { ImageFormat, SelectionArea, DragState } from '../V1/types'

// 重新导入V1的接口用于继承
import type { ScreenShotOptions, ScreenShotResult } from '../V1/types'

/**
 * 编辑工具类型
 */
export type EditTool =
  | 'select' // 选择工具
  | 'brush' // 画笔
  | 'rectangle' // 矩形框选
  | 'arrow' // 箭头标记
  | 'text' // 文本标注
  | 'mosaic' // 马赛克
  | 'eraser' // 橡皮擦

/**
 * 画笔配置
 */
export interface BrushConfig {
  /** 画笔颜色 */
  color: string
  /** 画笔粗细 */
  size: number
  /** 画笔透明度 */
  opacity: number
}

/**
 * 矩形框选配置
 */
export interface RectangleConfig {
  /** 边框颜色 */
  strokeColor: string
  /** 填充颜色 */
  fillColor: string
  /** 边框粗细 */
  strokeWidth: number
  /** 填充透明度 */
  fillOpacity: number
}

/**
 * 箭头配置
 */
export interface ArrowConfig {
  /** 箭头颜色 */
  color: string
  /** 箭头粗细 */
  width: number
  /** 箭头头部大小 */
  headSize: number
}

/**
 * 文本配置
 */
export interface TextConfig {
  /** 文本颜色 */
  color: string
  /** 字体大小 */
  fontSize: number
  /** 字体族 */
  fontFamily: string
  /** 字体粗细 */
  fontWeight: 'normal' | 'bold'
}

/**
 * 马赛克配置
 */
export interface MosaicConfig {
  /** 马赛克块大小 */
  blockSize: number
  /** 马赛克强度 */
  intensity: number
}

/**
 * 编辑工具配置
 */
export interface EditToolConfig {
  brush: BrushConfig
  rectangle: RectangleConfig
  arrow: ArrowConfig
  text: TextConfig
  mosaic: MosaicConfig
}

/**
 * 编辑操作记录
 */
export interface EditOperation {
  /** 操作ID */
  id: string
  /** 操作类型 */
  type: EditTool
  /** 操作数据 */
  data: any
  /** 操作时间戳 */
  timestamp: number
}

/**
 * 绘制路径点
 */
export interface DrawPoint {
  x: number
  y: number
  pressure?: number // 压感（可选）
}

/**
 * 画笔路径
 */
export interface BrushPath {
  points: DrawPoint[]
  config: BrushConfig
}

/**
 * 矩形数据
 */
export interface RectangleData {
  x: number
  y: number
  width: number
  height: number
  config: RectangleConfig
}

/**
 * 箭头数据
 */
export interface ArrowData {
  startX: number
  startY: number
  endX: number
  endY: number
  config: ArrowConfig
}

/**
 * 文本数据
 */
export interface TextData {
  x: number
  y: number
  text: string
  config: TextConfig
}

/**
 * 马赛克区域数据
 */
export interface MosaicData {
  x: number
  y: number
  width: number
  height: number
  config: MosaicConfig
}

/**
 * V2版本截图选项
 */
export interface ScreenShotV2Options extends ScreenShotOptions {
  /** 是否启用编辑功能 */
  enableEdit?: boolean
  /** 默认编辑工具 */
  defaultTool?: EditTool
  /** 可用的编辑工具列表 */
  availableTools?: EditTool[]
  /** 编辑工具配置 */
  toolConfig?: Partial<EditToolConfig>
  /** 是否启用撤销/重做 */
  enableUndo?: boolean
  /** 最大撤销步数 */
  maxUndoSteps?: number
}

/**
 * 编辑状态
 */
export interface EditState {
  /** 当前选中的工具 */
  currentTool: EditTool
  /** 是否正在绘制 */
  isDrawing: boolean
  /** 当前绘制的路径/形状 */
  currentPath: any
  /** 编辑操作历史 */
  history: EditOperation[]
  /** 当前历史位置 */
  historyIndex: number
  /** 工具配置 */
  toolConfig: EditToolConfig
}

/**
 * V2版本截图结果
 */
export interface ScreenShotV2Result extends ScreenShotResult {
  /** 编辑操作历史 */
  editHistory?: EditOperation[]
  /** 是否包含编辑内容 */
  hasEdits?: boolean
}
