/**
 * 截图功能V3版本导出文件
 * 基于js-web-screen-shot的完整功能实现
 * 使用@zumer/snapdom作为HTML转图片引擎
 */

// 导出主要Hook
export { useScreenShotV3 } from './useScreenShotV3'

// 导出工具类
export { ScreenShotV3Utils } from './utils'

// 导出组件
export { default as ScreenShotOverlayV3 } from './ScreenShotOverlayV3.vue'
export { default as ScreenShotDemoV3 } from './ScreenShotDemoV3.vue'

// 导出类型
export type {
  // 基础类型
  ImageFormat,
  SelectionArea,
  DragState,
  
  // V3特有类型
  EditTool,
  ToolPosition,
  MaskColor,
  CropBoxInfo,
  Position,
  CustomRightClickEvent,
  HiddenScrollBar,
  HiddenToolIco,
  
  // 回调类型
  CallbackResult,
  TriggerCallbackResult,
  CancelCallbackResult,
  SaveCallbackResult,
  
  // 配置和结果类型
  ScreenShotV3Options,
  EditOperation,
  EditState,
  ScreenShotV3Result,
  ScreenShotV3Instance
} from './types'

// 默认导出Hook
export { useScreenShotV3 as default } from './useScreenShotV3'
