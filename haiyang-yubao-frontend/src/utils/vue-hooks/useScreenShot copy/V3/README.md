# 截图功能V3版本

基于 [js-web-screen-shot](https://github.com/likaia/js-screen-shot) 的完整功能实现，使用 `@zumer/snapdom` 作为HTML转图片引擎，以Vue3 Hook的形式提供。

## 🎯 主要特性

### 截图引擎
- ✅ **@zumer/snapdom引擎**：替代html2canvas，提供更好的性能和兼容性
- ✅ **WebRTC屏幕共享**：支持实时屏幕捕获
- ✅ **自定义图片源**：支持传入自定义图片进行编辑
- ✅ **跨域图片处理**：支持跨域图片的截图和编辑

### 编辑工具
- 🎯 **选择工具**：移动和调整选区，8个方向调整手柄
- 📐 **矩形工具**：绘制矩形框，支持边框和填充样式
- ⭕ **圆形工具**：绘制圆形，支持颜色和样式配置
- ➡️ **箭头工具**：绘制指向箭头，支持等比例和递增箭头
- 🎨 **画笔工具**：自由绘制，支持颜色、粗细、透明度调节
- 🔲 **马赛克工具**：对敏感区域进行马赛克处理
- 📝 **文本工具**：添加文本标注，支持字体、大小、颜色设置
- ➖ **分割线工具**：绘制分割线

### 高级功能
- ↩️ **撤销/重做**：支持无限次撤销和重做操作（可配置最大次数）
- 📋 **剪切板支持**：自动将截图写入系统剪切板
- ⌨️ **快捷键支持**：Esc取消、Enter确认、Ctrl+Z撤销
- 🎮 **工具栏定制**：支持隐藏特定工具，自定义工具栏位置
- 🎨 **界面定制**：自定义蒙层颜色、边框颜色、工具栏位置
- 📱 **移动端兼容**：支持触屏设备操作

### 配置选项
- 🔧 **丰富的配置项**：超过30个配置选项，满足各种使用场景
- 🖼️ **多种图片格式**：支持PNG、JPEG、WebP、BMP格式
- 📏 **尺寸控制**：支持自定义画布尺寸、最小选区尺寸
- 🎯 **初始裁剪框**：支持设置初始选区位置和大小
- 🔄 **自动截图**：支持单击截全屏、自动完成截图

## 📦 安装依赖

确保已安装 `@zumer/snapdom`：

```bash
pnpm add @zumer/snapdom
```

## 🚀 基础使用

### 1. 导入Hook和组件

```vue
<template>
  <div>
    <button @click="startScreenShot">开始截图</button>
    
    <ScreenShotOverlayV3
      :is-active="isActive"
      :is-capturing="isCapturing"
      :selection="selection"
      :drag-state="dragState"
      :edit-state="editState"
      :can-undo="canUndo"
      :can-redo="canRedo"
      @mousedown="handleMouseDown"
      @cancel="cancelScreenShot"
      @save="saveScreenShot"
      @set-tool="setCurrentTool"
      @undo="undo"
      @redo="redo"
    />
  </div>
</template>

<script setup lang="ts">
import { useScreenShotV3, ScreenShotOverlayV3 } from 'src/utils/vue-hooks/useScreenShot/V3'

const {
  isActive,
  isCapturing,
  selection,
  dragState,
  editState,
  canUndo,
  canRedo,
  startScreenShot,
  cancelScreenShot,
  saveScreenShot,
  setCurrentTool,
  handleMouseDown,
  undo,
  redo
} = useScreenShotV3({
  enableWebRtc: false,
  quality: 0.9,
  writeBase64: true,
  completeCallback: (result) => {
    console.log('截图完成:', result)
  }
})
</script>
```

### 2. 使用默认导出

```vue
<script setup lang="ts">
import useScreenShot from 'src/utils/vue-hooks/useScreenShot/V3'

const { startScreenShot, saveScreenShot } = useScreenShot()
</script>
```

## ⚙️ 配置选项

### 基础配置

```typescript
interface ScreenShotV3Options {
  // 截图引擎配置
  enableWebRtc?: boolean          // 是否启用WebRTC模式
  screenFlow?: MediaStream        // 自定义屏幕流
  imgSrc?: string                 // 自定义图片源
  loadCrossImg?: boolean          // 是否加载跨域图片
  proxyUrl?: string               // 代理服务器地址
  
  // 图片配置
  quality?: number                // 图片质量 (0.1-1.0)
  defaultFormat?: ImageFormat     // 默认图片格式
  
  // 选区配置
  allowMove?: boolean             // 是否允许移动选区
  allowResize?: boolean           // 是否允许调整选区大小
  minSize?: { width: number; height: number }  // 最小选区尺寸
  cropBoxInfo?: CropBoxInfo       // 初始裁剪框
  
  // 界面配置
  level?: number                  // 容器层级
  cutBoxBdColor?: string          // 选区边框颜色
  maskColor?: MaskColor           // 蒙层颜色
  toolPosition?: ToolPosition     // 工具栏位置
  hiddenToolIco?: HiddenToolIco   // 隐藏的工具图标
  
  // 功能配置
  maxUndoNum?: number             // 最大撤销次数
  clickCutFullScreen?: boolean    // 单击截全屏
  writeBase64?: boolean           // 写入剪切板
  wrcWindowMode?: boolean         // 窗口截图模式
  
  // 回调函数
  completeCallback?: (result: CallbackResult) => void
  closeCallback?: () => void
  triggerCallback?: (result: TriggerCallbackResult) => void
  cancelCallback?: (result: CancelCallbackResult) => void
  saveCallback?: (result: SaveCallbackResult) => void
}
```

### 高级配置示例

```typescript
const config: ScreenShotV3Options = {
  // 启用WebRTC屏幕共享
  enableWebRtc: true,
  wrcWindowMode: true,
  wrcReplyTime: 1000,
  
  // 自定义界面
  maskColor: { r: 0, g: 0, b: 0, a: 0.8 },
  cutBoxBdColor: '#ff0000',
  toolPosition: 'left',
  
  // 隐藏部分工具
  hiddenToolIco: {
    separateLine: true,
    save: true
  },
  
  // 设置初始选区
  cropBoxInfo: { x: 100, y: 100, w: 400, h: 300 },
  clickCutFullScreen: true,
  
  // 回调处理
  completeCallback: ({ base64, cutInfo }) => {
    console.log('截图完成:', { base64, cutInfo })
  },
  triggerCallback: ({ code, msg }) => {
    if (code === 0) {
      console.log('截图加载完成')
    }
  }
}
```

## 🎨 编辑工具详解

### 1. 选择工具 (select)
- 默认工具，用于移动和调整选区
- 显示8个调整手柄（上下左右和四个角）
- 支持拖拽移动整个选区

### 2. 矩形工具 (square)
- 绘制矩形框
- 支持边框和填充样式
- 可配置颜色和透明度

### 3. 圆形工具 (round)
- 绘制圆形或椭圆
- 支持边框和填充样式
- 可配置颜色和透明度

### 4. 箭头工具 (rightTop)
- 绘制指向箭头
- 支持等比例箭头和递增变粗箭头
- 可配置颜色和粗细

### 5. 画笔工具 (brush)
- 自由绘制线条
- 支持压感（如果设备支持）
- 可配置颜色、粗细、透明度

### 6. 马赛克工具 (mosaicPen)
- 对敏感区域进行马赛克处理
- 可配置马赛克块大小和强度
- 不可逆操作

### 7. 文本工具 (text)
- 添加文本标注
- 支持字体、大小、颜色设置
- 支持多行文本

### 8. 分割线工具 (separateLine)
- 绘制分割线
- 支持颜色和粗细配置

## 🔧 API方法

### 实例方法

```typescript
const {
  // 状态
  isActive,              // 是否激活截图模式
  isCapturing,           // 是否正在截图
  selection,             // 选区信息
  editState,             // 编辑状态
  canUndo,               // 是否可撤销
  canRedo,               // 是否可重做
  
  // 方法
  startScreenShot,       // 开始截图
  cancelScreenShot,      // 取消截图
  saveScreenShot,        // 保存截图
  setCurrentTool,        // 设置当前工具
  undo,                  // 撤销
  redo,                  // 重做
  
  // 实例方法
  getCanvasController,   // 获取Canvas控制器
  destroyComponents,     // 销毁组件
  completeScreenshot,    // 完成截图
  getCutBoxInfo          // 获取裁剪框信息
} = useScreenShotV3(options)
```

### 工具类方法

```typescript
import { ScreenShotV3Utils } from 'src/utils/vue-hooks/useScreenShot/V3'

// 使用snapdom截图
const canvas = await ScreenShotV3Utils.captureScreen(element, options)

// 从WebRTC流截图
const canvas = await ScreenShotV3Utils.captureFromStream(stream, options)

// 裁剪Canvas
const croppedCanvas = ScreenShotV3Utils.cropCanvas(sourceCanvas, cropInfo)

// 转换为数据URL
const dataUrl = ScreenShotV3Utils.canvasToDataURL(canvas, 'png', 0.9)

// 下载图片
ScreenShotV3Utils.downloadImage(dataUrl, 'screenshot.png')

// 写入剪切板
const success = await ScreenShotV3Utils.writeToClipboard(canvas)
```

## ⌨️ 快捷键

- `Esc`：取消截图
- `Enter`：确认保存截图
- `Ctrl/Cmd + Z`：撤销操作
- `Ctrl/Cmd + Shift + Z`：重做操作

## 🌐 浏览器兼容性

- **WebRTC模式**：需要HTTPS环境或localhost
- **剪切板功能**：需要HTTPS环境或localhost
- **移动端**：建议使用非WebRTC模式
- **跨域图片**：需要服务器支持CORS

## 📱 移动端使用

```typescript
const config = {
  enableWebRtc: false,  // 移动端建议关闭WebRTC
  // 其他配置...
}
```

在HTML中添加：
```html
<meta name="viewport" content="user-scalable=no">
```

## 🔄 与其他版本对比

| 功能 | V1 | V2 | V3 |
|------|----|----|----| 
| 基础截图 | ✅ | ✅ | ✅ |
| 编辑工具 | ❌ | 6种 | 8种 |
| WebRTC支持 | ❌ | ❌ | ✅ |
| 撤销重做 | ❌ | ✅ | ✅ |
| 快捷键 | ❌ | ✅ | ✅ |
| 工具栏定制 | ❌ | ❌ | ✅ |
| 移动端兼容 | ❌ | ❌ | ✅ |
| 配置选项 | 5个 | 15个 | 30+个 |

V3版本是最完整的实现，适合需要专业截图编辑功能的应用场景。
