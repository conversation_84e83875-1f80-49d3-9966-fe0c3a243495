/**
 * 截图功能V3版本类型定义
 * 基于js-web-screen-shot的完整功能实现
 * 使用@zumer/snapdom作为HTML转图片引擎
 */

// 继承V1的基础类型
export type { ImageFormat, SelectionArea, DragState } from '../V1/types'

// 重新导入V1的接口用于继承
import type { ScreenShotOptions, ScreenShotResult } from '../V1/types'

/**
 * 编辑工具类型
 */
export type EditTool =
  | 'select' // 选择工具
  | 'square' // 矩形绘制
  | 'round' // 圆形绘制
  | 'rightTop' // 箭头绘制
  | 'brush' // 画笔工具
  | 'mosaicPen' // 马赛克工具
  | 'text' // 文本工具
  | 'separateLine' // 分割线
  | 'eraser' // 橡皮擦

/**
 * 工具栏位置
 */
export type ToolPosition = 'left' | 'center' | 'right'

/**
 * 蒙层颜色
 */
export interface MaskColor {
  r: number
  g: number
  b: number
  a: number
}

/**
 * 裁剪框信息
 */
export interface CropBoxInfo {
  x: number
  y: number
  w: number
  h: number
}

/**
 * 位置信息
 */
export interface Position {
  left?: number
  top?: number
}

/**
 * 自定义右键事件
 */
export interface CustomRightClickEvent {
  state: boolean
  handleFn?: () => void
}

/**
 * 隐藏滚动条配置
 */
export interface HiddenScrollBar {
  state: boolean
  fillState?: boolean
  color?: string
  fillWidth?: number
  fillHeight?: number
}

/**
 * 隐藏工具图标配置
 */
export interface HiddenToolIco {
  square?: boolean // 矩形绘制
  round?: boolean // 圆形绘制
  rightTop?: boolean // 箭头绘制
  brush?: boolean // 涂鸦
  mosaicPen?: boolean // 马赛克工具
  text?: boolean // 文本工具
  separateLine?: boolean // 分割线
  save?: boolean // 下载图片
  undo?: boolean // 撤销工具
  confirm?: boolean // 保存图片
}

/**
 * 回调函数类型
 */
export interface CallbackResult {
  base64: string
  cutInfo: {
    startX: number
    startY: number
    width: number
    height: number
  }
}

export interface TriggerCallbackResult {
  code: number
  msg: string
  displaySurface: string | null
  displayLabel: string | null
}

export interface CancelCallbackResult {
  code: number
  msg: string
  errorInfo: string
}

export interface SaveCallbackResult {
  code: number
  msg: string
}

/**
 * V3版本截图选项
 */
export interface ScreenShotV3Options extends ScreenShotOptions {
  /** 是否启用webrtc模式 */
  enableWebRtc?: boolean
  /** 设备提供的屏幕流数据 */
  screenFlow?: MediaStream
  /** 截图完成回调函数 */
  completeCallback?: (result: CallbackResult) => void
  /** 截图关闭回调函数 */
  closeCallback?: () => void
  /** 截图响应回调函数 */
  triggerCallback?: (result: TriggerCallbackResult) => void
  /** 取消分享回调函数 */
  cancelCallback?: (result: CancelCallbackResult) => void
  /** 保存截图回调函数 */
  saveCallback?: (result: SaveCallbackResult) => void
  /** 截图容器层级 */
  level?: number
  /** 裁剪区域边框像素点颜色 */
  cutBoxBdColor?: string
  /** 最大可撤销次数 */
  maxUndoNum?: number
  /** 画布宽度 */
  canvasWidth?: number
  /** 画布高度 */
  canvasHeight?: number
  /** 截图容器位置 */
  position?: Position
  /** 单击截全屏启用状态 */
  clickCutFullScreen?: boolean
  /** 需要隐藏的截图工具栏图标 */
  hiddenToolIco?: HiddenToolIco
  /** 截图组件加载完毕后，是否显示截图内容至canvas画布内 */
  showScreenData?: boolean
  /** 自定义容器的右键点击事件 */
  customRightClickEvent?: CustomRightClickEvent
  /** 截图内容 */
  imgSrc?: string
  /** 是否加载跨域图片 */
  loadCrossImg?: boolean
  /** 代理服务器地址 */
  proxyUrl?: string
  /** 需要进行截图的容器 */
  screenShotDom?: HTMLElement
  /** 是否使用等比例箭头 */
  useRatioArrow?: boolean
  /** 是否开启图片自适应 */
  imgAutoFit?: boolean
  /** 初始裁剪框 */
  cropBoxInfo?: CropBoxInfo
  /** webrtc模式捕捉屏幕时的响应时间 */
  wrcReplyTime?: number
  /** webrtc模式下是否需要对图像进行裁剪 */
  wrcImgPosition?: CropBoxInfo
  /** 截图容器是否可滚动 */
  noScroll?: boolean
  /** 蒙层颜色 */
  maskColor?: MaskColor
  /** 工具栏展示位置 */
  toolPosition?: ToolPosition
  /** 是否将截图内容写入剪切板 */
  writeBase64?: boolean
  /** 是否启用窗口截图模式 */
  wrcWindowMode?: boolean
  /** 是否隐藏滚动条 */
  hiddenScrollBar?: HiddenScrollBar
  /** Mac系统标题栏高度 */
  menuBarHeight?: number
}

/**
 * 编辑操作历史
 */
export interface EditOperation {
  id: string
  type: EditTool
  data: any
  timestamp: number
}

/**
 * 编辑状态
 */
export interface EditState {
  currentTool: EditTool
  history: EditOperation[]
  currentIndex: number
  isDrawing: boolean
  isSelecting: boolean
  startPoint: { x: number; y: number } | null
  tempCanvas: HTMLCanvasElement | null
}

/**
 * V3版本截图结果
 */
export interface ScreenShotV3Result extends ScreenShotResult {
  /** 编辑操作历史 */
  editHistory?: EditOperation[]
  /** 是否包含编辑内容 */
  hasEdits?: boolean
  /** 裁剪信息 */
  cutInfo?: {
    startX: number
    startY: number
    width: number
    height: number
  }
}

/**
 * 截图实例API
 */
export interface ScreenShotV3Instance {
  /** 获取截图容器的DOM */
  getCanvasController: () => HTMLCanvasElement | null
  /** 销毁截图容器 */
  destroyComponents: () => void
  /** 完成截图 */
  completeScreenshot: () => void
  /** 获取裁剪框信息 */
  getCutBoxInfo: () => {
    startX: number
    startY: number
    width: number
    height: number
  } | null
}
