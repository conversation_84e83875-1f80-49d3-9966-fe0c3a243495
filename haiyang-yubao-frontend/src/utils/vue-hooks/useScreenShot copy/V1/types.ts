/**
 * 截图功能相关类型定义
 */

/**
 * 图片格式类型
 */
export type ImageFormat = 'png' | 'jpeg' | 'webp'

/**
 * 选区位置信息
 */
export interface SelectionArea {
  /** 选区左上角X坐标 */
  x: number
  /** 选区左上角Y坐标 */
  y: number
  /** 选区宽度 */
  width: number
  /** 选区高度 */
  height: number
}

/**
 * 截图配置选项
 */
export interface ScreenShotOptions {
  /** 图片质量 (0-1) */
  quality?: number
  /** 默认图片格式 */
  defaultFormat?: ImageFormat
  /** 是否允许移动选区 */
  allowMove?: boolean
  /** 是否允许调整选区大小 */
  allowResize?: boolean
  /** 最小选区大小 */
  minSize?: {
    width: number
    height: number
  }
}

/**
 * 鼠标拖拽状态
 */
export interface DragState {
  /** 是否正在拖拽 */
  isDragging: boolean
  /** 拖拽类型：move-移动, resize-调整大小 */
  dragType: 'move' | 'resize' | null
  /** 调整大小的方向 */
  resizeDirection?: 'nw' | 'n' | 'ne' | 'e' | 'se' | 's' | 'sw' | 'w' | string
  /** 拖拽开始时的鼠标位置 */
  startMousePos: { x: number; y: number }
  /** 拖拽开始时的选区位置 */
  startSelection: SelectionArea
}

/**
 * 截图结果
 */
export interface ScreenShotResult {
  /** 截图的base64数据 */
  dataUrl: string
  /** 图片格式 */
  format: ImageFormat
  /** 选区信息 */
  selection: SelectionArea
}
