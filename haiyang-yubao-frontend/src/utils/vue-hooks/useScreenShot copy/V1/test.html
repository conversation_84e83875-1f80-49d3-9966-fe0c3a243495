<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>截图功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #007bff;
        }
        
        .card h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        .chart {
            height: 100px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            border-radius: 5px;
            margin: 15px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .test-button {
            display: block;
            width: 200px;
            margin: 20px auto;
            padding: 12px 24px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .test-button:hover {
            background: #218838;
        }
        
        .instructions {
            background: #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .instructions h4 {
            margin-top: 0;
            color: #495057;
        }
        
        .instructions ul {
            margin-bottom: 0;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖼️ 截图功能测试页面</h1>
            <p>这是一个用于测试自定义选区截图功能的演示页面</p>
        </div>
        
        <div class="content-grid">
            <div class="card">
                <h3>📊 数据展示</h3>
                <p>这里是一些模拟的数据内容，可以用来测试截图效果。</p>
                <div class="chart">模拟图表</div>
            </div>
            
            <div class="card">
                <h3>🎨 设计元素</h3>
                <p>包含各种颜色和样式的内容，用于验证截图的色彩还原度。</p>
                <div style="display: flex; gap: 10px; margin-top: 15px;">
                    <div style="width: 30px; height: 30px; background: #ff6b6b; border-radius: 50%;"></div>
                    <div style="width: 30px; height: 30px; background: #4ecdc4; border-radius: 50%;"></div>
                    <div style="width: 30px; height: 30px; background: #45b7d1; border-radius: 50%;"></div>
                    <div style="width: 30px; height: 30px; background: #96ceb4; border-radius: 50%;"></div>
                </div>
            </div>
            
            <div class="card">
                <h3>📝 文本内容</h3>
                <p>这是一段测试文本，包含了<strong>粗体</strong>、<em>斜体</em>和<u>下划线</u>等样式。</p>
                <blockquote style="border-left: 3px solid #007bff; padding-left: 15px; margin: 15px 0; font-style: italic;">
                    "这是一个引用文本示例"
                </blockquote>
            </div>
        </div>
        
        <button class="test-button" onclick="alert('这是一个测试按钮！')">
            🚀 测试按钮
        </button>
        
        <div class="instructions">
            <h4>📋 使用说明</h4>
            <ul>
                <li>在Vue组件中导入截图功能</li>
                <li>点击截图按钮启动截图模式</li>
                <li>拖拽选择要截图的区域</li>
                <li>可以通过拖拽角落调整选区大小</li>
                <li>可以拖拽选区内部移动位置</li>
                <li>选择图片格式后点击保存</li>
            </ul>
        </div>
        
        <div style="margin-top: 30px; text-align: center; color: #6c757d;">
            <p>💡 提示：这个页面包含了丰富的内容和样式，非常适合测试截图功能的各种场景。</p>
        </div>
    </div>
</body>
</html>
