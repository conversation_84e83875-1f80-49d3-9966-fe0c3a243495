import { ref, reactive, nextTick } from 'vue';
import type { 
  ScreenShotOptions, 
  SelectionArea, 
  DragState, 
  ImageFormat, 
  ScreenShotResult 
} from './types';
import { ScreenShotUtils } from './utils';

/**
 * 自定义选区截图Hook
 * @param options 截图配置选项
 * @returns 截图相关的状态和方法
 */
export function useScreenShot(options: ScreenShotOptions = {}) {
  // 默认配置
  const defaultOptions: Required<ScreenShotOptions> = {
    quality: 0.9,
    defaultFormat: 'png',
    allowMove: true,
    allowResize: true,
    minSize: { width: 50, height: 50 }
  };

  const config = { ...defaultOptions, ...options };

  // 响应式状态
  const isActive = ref(false);
  const isCapturing = ref(false);
  const selectedFormat = ref<ImageFormat>(config.defaultFormat);
  
  // 选区状态
  const selection = reactive<SelectionArea>({
    x: 100,
    y: 100,
    width: 300,
    height: 200
  });

  // 拖拽状态
  const dragState = reactive<DragState>({
    isDragging: false,
    dragType: null,
    resizeDirection: undefined,
    startMousePos: { x: 0, y: 0 },
    startSelection: { x: 0, y: 0, width: 0, height: 0 }
  });

  // 截图Canvas缓存
  let capturedCanvas: HTMLCanvasElement | null = null;

  /**
   * 开始截图流程
   */
  const startScreenShot = async (): Promise<void> => {
    try {
      isCapturing.value = true;
      
      // 等待DOM更新
      await nextTick();
      
      // 捕获当前页面
      capturedCanvas = await ScreenShotUtils.captureElement();
      
      // 激活选区界面
      isActive.value = true;
      
      // 重置选区到屏幕中央
      resetSelection();
      
    } catch (error) {
      console.error('截图失败:', error);
      throw error;
    } finally {
      isCapturing.value = false;
    }
  };

  /**
   * 重置选区到屏幕中央
   */
  const resetSelection = (): void => {
    const centerX = (window.innerWidth - 300) / 2;
    const centerY = (window.innerHeight - 200) / 2;
    
    selection.x = Math.max(0, centerX);
    selection.y = Math.max(0, centerY);
    selection.width = 300;
    selection.height = 200;
  };

  /**
   * 取消截图
   */
  const cancelScreenShot = (): void => {
    isActive.value = false;
    capturedCanvas = null;
    resetDragState();
  };

  /**
   * 保存截图
   */
  const saveScreenShot = async (): Promise<ScreenShotResult> => {
    if (!capturedCanvas) {
      throw new Error('没有可用的截图数据');
    }

    try {
      // 裁剪选中区域
      const croppedCanvas = ScreenShotUtils.cropCanvas(capturedCanvas, selection);
      
      // 转换为DataURL
      const dataUrl = ScreenShotUtils.canvasToDataURL(
        croppedCanvas, 
        selectedFormat.value, 
        config.quality
      );

      // 生成文件名并下载
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `screenshot-${timestamp}.${selectedFormat.value}`;
      ScreenShotUtils.downloadImage(dataUrl, filename);

      // 关闭截图界面
      cancelScreenShot();

      return {
        dataUrl,
        format: selectedFormat.value,
        selection: { ...selection }
      };

    } catch (error) {
      console.error('保存截图失败:', error);
      throw error;
    }
  };

  /**
   * 重置拖拽状态
   */
  const resetDragState = (): void => {
    dragState.isDragging = false;
    dragState.dragType = null;
    dragState.resizeDirection = undefined;
  };

  /**
   * 处理鼠标按下事件
   */
  const handleMouseDown = (event: MouseEvent): void => {
    if (!isActive.value) return;

    const mousePos = { x: event.clientX, y: event.clientY };
    
    // 检查是否点击了调整大小的手柄
    const handles = ScreenShotUtils.getResizeHandles(selection);
    
    if (config.allowResize && ScreenShotUtils.isPointInRect(mousePos, handles.topLeft)) {
      // 点击了左上角手柄
      startDrag('resize', 'top-left', mousePos);
    } else if (config.allowResize && ScreenShotUtils.isPointInRect(mousePos, handles.bottomRight)) {
      // 点击了右下角手柄
      startDrag('resize', 'bottom-right', mousePos);
    } else if (config.allowMove && ScreenShotUtils.isPointInRect(mousePos, selection)) {
      // 点击了选区内部，开始移动
      startDrag('move', undefined, mousePos);
    }
  };

  /**
   * 开始拖拽
   */
  const startDrag = (
    type: 'move' | 'resize', 
    direction: 'top-left' | 'bottom-right' | undefined,
    mousePos: { x: number; y: number }
  ): void => {
    dragState.isDragging = true;
    dragState.dragType = type;
    dragState.resizeDirection = direction;
    dragState.startMousePos = { ...mousePos };
    dragState.startSelection = { ...selection };

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  /**
   * 处理鼠标移动事件
   */
  const handleMouseMove = (event: MouseEvent): void => {
    if (!dragState.isDragging) return;

    const deltaX = event.clientX - dragState.startMousePos.x;
    const deltaY = event.clientY - dragState.startMousePos.y;

    if (dragState.dragType === 'move') {
      // 移动选区
      const newX = dragState.startSelection.x + deltaX;
      const newY = dragState.startSelection.y + deltaY;
      
      const constrainedSelection = ScreenShotUtils.constrainSelection(
        { ...selection, x: newX, y: newY },
        window.innerWidth,
        window.innerHeight
      );
      
      selection.x = constrainedSelection.x;
      selection.y = constrainedSelection.y;
      
    } else if (dragState.dragType === 'resize') {
      // 调整选区大小
      handleResize(deltaX, deltaY);
    }
  };

  /**
   * 处理选区大小调整
   */
  const handleResize = (deltaX: number, deltaY: number): void => {
    const { startSelection } = dragState;
    
    if (dragState.resizeDirection === 'top-left') {
      // 从左上角调整
      const newX = startSelection.x + deltaX;
      const newY = startSelection.y + deltaY;
      const newWidth = startSelection.width - deltaX;
      const newHeight = startSelection.height - deltaY;
      
      if (newWidth >= config.minSize.width && newHeight >= config.minSize.height) {
        selection.x = Math.max(0, newX);
        selection.y = Math.max(0, newY);
        selection.width = newWidth;
        selection.height = newHeight;
      }
      
    } else if (dragState.resizeDirection === 'bottom-right') {
      // 从右下角调整
      const newWidth = startSelection.width + deltaX;
      const newHeight = startSelection.height + deltaY;
      
      if (newWidth >= config.minSize.width && newHeight >= config.minSize.height) {
        const constrainedSelection = ScreenShotUtils.constrainSelection(
          { ...selection, width: newWidth, height: newHeight },
          window.innerWidth,
          window.innerHeight
        );
        
        selection.width = constrainedSelection.width;
        selection.height = constrainedSelection.height;
      }
    }
  };

  /**
   * 处理鼠标释放事件
   */
  const handleMouseUp = (): void => {
    resetDragState();
    
    // 移除全局鼠标事件监听
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  /**
   * 设置图片格式
   */
  const setImageFormat = (format: ImageFormat): void => {
    selectedFormat.value = format;
  };

  return {
    // 状态
    isActive,
    isCapturing,
    selection,
    selectedFormat,
    dragState,
    
    // 方法
    startScreenShot,
    cancelScreenShot,
    saveScreenShot,
    setImageFormat,
    handleMouseDown,
    
    // 工具方法
    resetSelection
  };
}
