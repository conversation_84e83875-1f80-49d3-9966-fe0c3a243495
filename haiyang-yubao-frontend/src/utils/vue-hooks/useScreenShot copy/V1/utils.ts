import { snapdom } from '@zumer/snapdom'
import type { ImageFormat, SelectionArea } from './types'

/**
 * 截图工具函数
 */
export class ScreenShotUtils {
  /**
   * 将页面转换为Canvas
   * @param element 要截图的元素，默认为document.body
   * @param options snapdom配置选项
   * @returns Promise<HTMLCanvasElement>
   */
  static async captureElement(
    element: HTMLElement = document.body,
    options: any = {}
  ): Promise<HTMLCanvasElement> {
    const defaultOptions = {
      scale: window.devicePixelRatio || 1,
      width: window.innerWidth,
      height: window.innerHeight,
      backgroundColor: '#ffffff',
      ...options
    }

    try {
      // 使用snapdom进行截图
      const result = await snapdom(element, defaultOptions)
      // snapdom返回的是CaptureResult对象，使用toCanvas方法获取canvas
      return result.toCanvas()
    } catch (error) {
      console.error('snapdom截图失败:', error)
      throw new Error('页面截图失败，请重试')
    }
  }

  /**
   * 裁剪Canvas指定区域
   * @param sourceCanvas 源Canvas
   * @param selection 选区信息
   * @returns HTMLCanvasElement 裁剪后的Canvas
   */
  static cropCanvas(
    sourceCanvas: HTMLCanvasElement,
    selection: SelectionArea
  ): HTMLCanvasElement {
    const { x, y, width, height } = selection

    // 创建新的Canvas用于存放裁剪结果
    const croppedCanvas = document.createElement('canvas')
    const ctx = croppedCanvas.getContext('2d')

    if (!ctx) {
      throw new Error('无法获取Canvas 2D上下文')
    }

    // 设置裁剪后的Canvas尺寸
    croppedCanvas.width = width
    croppedCanvas.height = height

    // 从源Canvas裁剪指定区域到新Canvas
    ctx.drawImage(
      sourceCanvas,
      x,
      y,
      width,
      height, // 源区域
      0,
      0,
      width,
      height // 目标区域
    )

    return croppedCanvas
  }

  /**
   * 将Canvas转换为指定格式的DataURL
   * @param canvas Canvas元素
   * @param format 图片格式
   * @param quality 图片质量 (0-1)
   * @returns string DataURL
   */
  static canvasToDataURL(
    canvas: HTMLCanvasElement,
    format: ImageFormat = 'png',
    quality = 0.9
  ): string {
    const mimeType = `image/${format}`
    return canvas.toDataURL(mimeType, quality)
  }

  /**
   * 下载图片
   * @param dataUrl 图片的DataURL
   * @param filename 文件名
   */
  static downloadImage(dataUrl: string, filename = 'screenshot'): void {
    const link = document.createElement('a')
    link.download = filename
    link.href = dataUrl

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  /**
   * 限制选区在指定范围内
   * @param selection 选区信息
   * @param containerWidth 容器宽度
   * @param containerHeight 容器高度
   * @returns SelectionArea 修正后的选区
   */
  static constrainSelection(
    selection: SelectionArea,
    containerWidth: number,
    containerHeight: number
  ): SelectionArea {
    const { x, y, width, height } = selection

    // 确保选区不超出容器边界
    const constrainedX = Math.max(0, Math.min(x, containerWidth - width))
    const constrainedY = Math.max(0, Math.min(y, containerHeight - height))
    const constrainedWidth = Math.min(width, containerWidth - constrainedX)
    const constrainedHeight = Math.min(height, containerHeight - constrainedY)

    return {
      x: constrainedX,
      y: constrainedY,
      width: Math.max(1, constrainedWidth),
      height: Math.max(1, constrainedHeight)
    }
  }

  /**
   * 检查点是否在矩形区域内
   * @param point 点坐标
   * @param rect 矩形区域
   * @returns boolean
   */
  static isPointInRect(
    point: { x: number; y: number },
    rect: SelectionArea
  ): boolean {
    return (
      point.x >= rect.x &&
      point.x <= rect.x + rect.width &&
      point.y >= rect.y &&
      point.y <= rect.y + rect.height
    )
  }

  /**
   * 获取调整大小的手柄区域
   * @param selection 选区信息
   * @param handleSize 手柄大小
   * @returns 手柄区域对象
   */
  static getResizeHandles(selection: SelectionArea, handleSize = 8) {
    const { x, y, width, height } = selection

    return {
      topLeft: {
        x: x - handleSize / 2,
        y: y - handleSize / 2,
        width: handleSize,
        height: handleSize
      },
      bottomRight: {
        x: x + width - handleSize / 2,
        y: y + height - handleSize / 2,
        width: handleSize,
        height: handleSize
      }
    }
  }
}
