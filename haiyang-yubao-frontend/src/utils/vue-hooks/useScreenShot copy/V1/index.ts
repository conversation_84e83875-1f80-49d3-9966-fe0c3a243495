/**
 * 自定义选区截图功能 V1
 * 
 * 功能特性：
 * - 支持自定义选区截图
 * - 支持拖拽移动和调整选区大小
 * - 支持多种图片格式（PNG、JPEG、WebP）
 * - 支持自定义图片质量
 * - 类似微信/QQ截图的交互体验
 */

// 导出类型定义
export type {
  ImageFormat,
  SelectionArea,
  ScreenShotOptions,
  DragState,
  ScreenShotResult
} from './types';

// 导出工具函数
export { ScreenShotUtils } from './utils';

// 导出主要Hook
export { useScreenShot } from './useScreenShot';

// 导出Vue组件
export { default as ScreenShotOverlay } from './ScreenShotOverlay.vue';
