# 自定义选区截图功能 V1

## 功能概述

实现了类似微信/QQ 截图的自定义选区截图功能，支持在浏览器页面内进行截图操作。

## 功能特性

- ✅ 自定义选区截图
- ✅ 拖拽移动选区位置
- ✅ 调整选区大小（左上角和右下角手柄）
- ✅ 支持多种图片格式（PNG、JPEG、WebP）
- ✅ 自定义图片质量
- ✅ 遮罩层效果
- ✅ 响应式工具栏
- ✅ 高清晰度输出

## 技术实现

- **Vue 3 Composition API**: 使用组合式 API 实现响应式状态管理
- **TypeScript**: 完整的类型定义和类型安全
- **html2canvas**: 将 HTML 页面转换为 Canvas
- **Canvas API**: 图片裁剪和格式转换

## 文件结构

```
V1/
├── types.ts              # 类型定义
├── utils.ts              # 工具函数
├── useScreenShot.ts      # 主要Hook逻辑
├── ScreenShotOverlay.vue # UI覆盖层组件
├── ScreenShotDemo.vue    # 演示组件
├── index.ts              # 导出文件
└── README.md             # 使用说明
```

## 快速开始

### 1. 基础使用

```vue
<template>
  <div>
    <button @click="startScreenShot">开始截图</button>

    <ScreenShotOverlay
      :is-active="isActive"
      :is-capturing="isCapturing"
      :selection="selection"
      :selected-format="selectedFormat"
      @mousedown="handleMouseDown"
      @cancel="cancelScreenShot"
      @save="saveScreenShot"
      @format-change="setImageFormat"
    />
  </div>
</template>

<script setup lang="ts">
import { useScreenShot, ScreenShotOverlay } from './V1'

const {
  isActive,
  isCapturing,
  selection,
  selectedFormat,
  startScreenShot,
  cancelScreenShot,
  saveScreenShot,
  setImageFormat,
  handleMouseDown
} = useScreenShot()
</script>
```

### 2. 自定义配置

```typescript
import { useScreenShot } from './V1'
import type { ScreenShotOptions } from './V1'

const options: ScreenShotOptions = {
  quality: 0.9, // 图片质量 (0-1)
  defaultFormat: 'png', // 默认格式
  allowMove: true, // 允许移动选区
  allowResize: true, // 允许调整大小
  minSize: {
    // 最小选区大小
    width: 50,
    height: 50
  }
}

const screenShot = useScreenShot(options)
```

## API 文档

### useScreenShot Hook

#### 参数

| 参数    | 类型              | 默认值 | 说明         |
| ------- | ----------------- | ------ | ------------ |
| options | ScreenShotOptions | {}     | 截图配置选项 |

#### 返回值

| 属性/方法        | 类型                              | 说明               |
| ---------------- | --------------------------------- | ------------------ |
| isActive         | Ref\<boolean\>                    | 是否激活截图界面   |
| isCapturing      | Ref\<boolean\>                    | 是否正在截图       |
| selection        | Reactive\<SelectionArea\>         | 选区信息           |
| selectedFormat   | Ref\<ImageFormat\>                | 当前选择的图片格式 |
| startScreenShot  | () => Promise\<void\>             | 开始截图           |
| cancelScreenShot | () => void                        | 取消截图           |
| saveScreenShot   | () => Promise\<ScreenShotResult\> | 保存截图           |
| setImageFormat   | (format: ImageFormat) => void     | 设置图片格式       |
| handleMouseDown  | (event: MouseEvent) => void       | 处理鼠标按下事件   |

### 类型定义

#### ScreenShotOptions

```typescript
interface ScreenShotOptions {
  quality?: number // 图片质量 (0-1)
  defaultFormat?: ImageFormat // 默认图片格式
  allowMove?: boolean // 是否允许移动选区
  allowResize?: boolean // 是否允许调整选区大小
  minSize?: {
    // 最小选区大小
    width: number
    height: number
  }
}
```

#### SelectionArea

```typescript
interface SelectionArea {
  x: number // 选区左上角X坐标
  y: number // 选区左上角Y坐标
  width: number // 选区宽度
  height: number // 选区高度
}
```

#### ScreenShotResult

```typescript
interface ScreenShotResult {
  dataUrl: string // 截图的base64数据
  format: ImageFormat // 图片格式
  selection: SelectionArea // 选区信息
}
```

#### ImageFormat

```typescript
type ImageFormat = 'png' | 'jpeg' | 'webp'
```

## 使用示例

### 完整示例

```vue
<template>
  <div class="app">
    <div class="content">
      <h1>我的应用</h1>
      <p>这里是一些内容...</p>

      <button @click="handleScreenShot" :disabled="isCapturing">
        {{ isCapturing ? '正在截图...' : '截图' }}
      </button>
    </div>

    <!-- 截图覆盖层 -->
    <ScreenShotOverlay
      :is-active="isActive"
      :is-capturing="isCapturing"
      :selection="selection"
      :selected-format="selectedFormat"
      @mousedown="handleMouseDown"
      @cancel="cancelScreenShot"
      @save="handleSave"
      @format-change="setImageFormat"
    />
  </div>
</template>

<script setup lang="ts">
import {
  useScreenShot,
  ScreenShotOverlay
} from '@/utils/vue-hooks/useScreenShot/V1'

// 使用截图功能
const {
  isActive,
  isCapturing,
  selection,
  selectedFormat,
  startScreenShot,
  cancelScreenShot,
  saveScreenShot,
  setImageFormat,
  handleMouseDown
} = useScreenShot({
  quality: 0.9,
  defaultFormat: 'png',
  allowMove: true,
  allowResize: true
})

// 开始截图
const handleScreenShot = async () => {
  try {
    await startScreenShot()
  } catch (error) {
    console.error('截图失败:', error)
  }
}

// 保存截图
const handleSave = async () => {
  try {
    const result = await saveScreenShot()
    console.log('截图保存成功:', result)
    // 可以在这里处理截图结果
  } catch (error) {
    console.error('保存失败:', error)
  }
}
</script>
```

## 注意事项

1. **浏览器兼容性**: 需要支持 Canvas API 和 html2canvas 的浏览器
2. **跨域问题**: 如果页面包含跨域图片，可能影响截图效果
3. **性能考虑**: 大页面截图可能需要较长时间，建议显示加载状态
4. **移动端**: 当前版本主要针对桌面端设计，移动端体验可能需要优化

## 常见问题

### Q: 截图模糊怎么办？

A: 可以调整`quality`参数或使用 PNG 格式以获得更好的清晰度。

### Q: 如何自定义选区样式？

A: 可以修改`ScreenShotOverlay.vue`中的 CSS 样式。

### Q: 能否截图整个页面？

A: 当前版本截图可视区域，如需截图整个页面，可以修改 html2canvas 的配置。

### Q: 如何处理截图失败？

A: 建议使用 try-catch 包装截图方法，并提供用户友好的错误提示。

## 更新日志

### V1.0.0

- 实现基础截图功能
- 支持自定义选区
- 支持拖拽和调整大小
- 支持多种图片格式
- 完整的 TypeScript 类型定义
