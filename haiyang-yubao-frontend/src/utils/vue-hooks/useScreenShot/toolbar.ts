import { ref, type Ref } from "vue";
import type {
  DrawTool,
  ImageFormat,
  ToolbarPosition,
  CutInfo,
  ToolbarConfig,
} from "./types";
import { defaultConfig } from "./config";

/**
 * 工具栏管理功能
 */
export function useToolbar(
  screenshotContainer: Ref<HTMLElement | null>,
  currentTool: Ref<DrawTool>,
  currentFormat: Ref<ImageFormat>,
  toolbarConfig: ToolbarConfig = {},
  callbacks?: {
    onUndo?: () => void;
    onRedo?: () => void;
    onConfirm?: () => void;
    onCancel?: () => void;
    onDownload?: () => void;
  }
) {
  const toolbarRef = ref<HTMLElement | null>(null);

  /**
   * 创建工具栏
   */
  const createToolbar = (): HTMLElement => {
    const toolbar = document.createElement("div");
    toolbar.className = "screenshot-toolbar";
    toolbar.style.cssText = `
      position: absolute;
      background: ${defaultConfig.toolbarBgColor};
      border-radius: 4px;
      padding: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      display: flex;
      align-items: center;
      gap: 8px;
      z-index: 1000;
      user-select: none;
    `;

    // 创建工具按钮（移除select工具，因为它是默认状态）
    const allTools = [
      { name: "rectangle", icon: "▭", title: "矩形" },
      { name: "circle", icon: "○", title: "圆形" },
      { name: "arrow", icon: "→", title: "箭头" },
      { name: "brush", icon: "✏", title: "画笔" },
      { name: "mosaic", icon: "▦", title: "马赛克" },
      { name: "text", icon: "T", title: "文字" },
    ];

    // 根据配置过滤可用工具
    const availableTools =
      toolbarConfig.availableTools || allTools.map((t) => t.name as DrawTool);
    const tools = allTools.filter((tool) =>
      availableTools.includes(tool.name as DrawTool)
    );

    tools.forEach((tool) => {
      const button = createToolButton(
        tool.name as DrawTool,
        tool.icon,
        tool.title
      );
      toolbar.appendChild(button);
    });

    // 添加分隔符
    toolbar.appendChild(createSeparator());

    // 根据配置决定是否显示格式选择器
    if (toolbarConfig.showFormatSelector !== false) {
      const formatSelector = createFormatSelector();
      toolbar.appendChild(formatSelector);

      // 添加分隔符
      toolbar.appendChild(createSeparator());
    }

    // 创建操作按钮
    const undoButton = createActionButton("↶", "撤销", "undo");
    const redoButton = createActionButton("↷", "重做", "redo");
    const confirmButton = createActionButton("✓", "确认", "confirm");
    const cancelButton = createActionButton("✕", "取消", "cancel");
    const downloadButton = createActionButton("💾", "下载", "download");

    toolbar.appendChild(undoButton);
    toolbar.appendChild(redoButton);
    toolbar.appendChild(confirmButton);
    toolbar.appendChild(cancelButton);
    toolbar.appendChild(downloadButton);

    return toolbar;
  };

  /**
   * 创建工具按钮
   */
  const createToolButton = (
    tool: DrawTool,
    icon: string,
    title: string
  ): HTMLElement => {
    const button = document.createElement("button");
    button.className = `tool-button tool-${tool}`;
    button.innerHTML = icon;
    button.title = title;
    button.style.cssText = `
      width: 32px;
      height: 32px;
      border: 1px solid #ddd;
      background: ${
        currentTool.value === tool ? "rgba(64, 158, 255, 0.8)" : "#fff"
      };
      color: ${defaultConfig.toolbarTextColor};
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      transition: all 0.2s;
    `;

    // 添加点击事件
    button.addEventListener("click", (e) => {
      e.stopPropagation();
      e.preventDefault();
      selectTool(tool);
    });

    // 添加悬停效果
    button.addEventListener("mouseenter", () => {
      if (currentTool.value !== tool) {
        button.style.background = "#f5f5f5";
      }
    });

    button.addEventListener("mouseleave", () => {
      // 鼠标离开时，重新应用正确的样式
      updateToolbarButtons();
    });

    return button;
  };

  /**
   * 创建操作按钮
   */
  const createActionButton = (
    icon: string,
    title: string,
    action: string
  ): HTMLElement => {
    const button = document.createElement("button");
    button.className = `action-button action-${action}`;
    button.innerHTML = icon;
    button.title = title;
    button.style.cssText = `
      width: 32px;
      height: 32px;
      border: 1px solid #ddd;
      background: #fff;
      color: ${defaultConfig.toolbarTextColor};
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      transition: all 0.2s;
    `;

    // 添加点击事件
    button.addEventListener("click", (e) => {
      e.preventDefault();
      e.stopPropagation();
      handleActionClick(action);
    });

    // 添加悬停效果
    button.addEventListener("mouseenter", () => {
      button.style.background = "#f5f5f5";
    });

    button.addEventListener("mouseleave", () => {
      button.style.background = "#fff";
    });

    return button;
  };

  /**
   * 创建格式选择器
   */
  const createFormatSelector = (): HTMLElement => {
    const container = document.createElement("div");
    container.style.cssText = `
      display: flex;
      align-items: center;
      gap: 4px;
    `;

    const label = document.createElement("span");
    label.textContent = "格式:";
    label.style.cssText = `
      font-size: 12px;
      color: ${defaultConfig.toolbarTextColor};
    `;

    const select = document.createElement("select");
    select.className = "format-selector";
    select.style.cssText = `
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 4px 8px;
      font-size: 12px;
      background: #fff;
      color: #333;
      cursor: pointer;
      min-width: 60px;
    `;

    const formats = [
      { value: "png", text: "PNG" },
      { value: "jpg", text: "JPG" },
      { value: "webp", text: "WebP" },
    ];

    formats.forEach((format) => {
      const option = document.createElement("option");
      option.value = format.value;
      option.textContent = format.text;
      option.selected = currentFormat.value === format.value;
      select.appendChild(option);
    });

    // 添加变化事件
    select.addEventListener("change", (e) => {
      e.stopPropagation();
      const target = e.target as HTMLSelectElement;
      currentFormat.value = target.value as ImageFormat;
    });

    container.appendChild(label);
    container.appendChild(select);

    return container;
  };

  /**
   * 创建分隔符
   */
  const createSeparator = (): HTMLElement => {
    const separator = document.createElement("div");
    separator.style.cssText = `
      width: 1px;
      height: 20px;
      background: #ddd;
      margin: 0 4px;
    `;
    return separator;
  };

  /**
   * 选择工具
   */
  const selectTool = (tool: DrawTool) => {
    // 如果点击的是当前已选中的工具，则取消选择
    if (currentTool.value === tool) {
      currentTool.value = "select";
      console.log(`取消工具选择，切换为选择模式`);
    } else {
      // 选择新工具，其他工具自动取消选择
      currentTool.value = tool;
      console.log(`选择工具: ${tool}`);
    }
    updateToolbarButtons();
  };

  /**
   * 处理操作按钮点击
   */
  const handleActionClick = (action: string) => {
    console.log(`操作按钮点击: ${action}`);

    switch (action) {
      case "undo":
        if (callbacks?.onUndo) {
          callbacks.onUndo();
        } else {
          console.log("执行撤销操作");
        }
        break;
      case "redo":
        if (callbacks?.onRedo) {
          callbacks.onRedo();
        } else {
          console.log("执行重做操作");
        }
        break;
      case "confirm":
        if (callbacks?.onConfirm) {
          callbacks.onConfirm();
        } else {
          console.log("执行确认操作");
        }
        break;
      case "cancel":
        if (callbacks?.onCancel) {
          callbacks.onCancel();
        } else {
          console.log("执行取消操作");
        }
        break;
      case "download":
        if (callbacks?.onDownload) {
          callbacks.onDownload();
        } else {
          console.log("执行下载操作");
        }
        break;
      default:
        console.log(`未知操作: ${action}`);
    }
  };

  /**
   * 更新工具栏按钮状态
   */
  const updateToolbarButtons = () => {
    if (!toolbarRef.value) return;

    const buttons = toolbarRef.value.querySelectorAll(".tool-button");
    buttons.forEach((button) => {
      const buttonElement = button as HTMLElement;
      // 从className中提取工具名，排除tool-button
      const classList = buttonElement.className.split(" ");
      const toolClass = classList.find(
        (cls) => cls.startsWith("tool-") && cls !== "tool-button"
      );
      const tool = toolClass?.replace("tool-", "");

      console.log(`更新按钮状态: ${tool}, 当前工具: ${currentTool.value}`);

      // 检查是否为当前选中的工具
      if (tool && tool === currentTool.value) {
        // 选中状态：蓝色背景，白色文字，加粗边框
        buttonElement.style.setProperty(
          "background",
          "rgba(64, 158, 255, 0.9)",
          "important"
        );
        buttonElement.style.setProperty("color", "#fff", "important");
        buttonElement.style.setProperty(
          "border",
          "2px solid #409eff",
          "important"
        );
        buttonElement.style.setProperty(
          "box-shadow",
          "0 2px 4px rgba(64, 158, 255, 0.3)",
          "important"
        );
        buttonElement.style.setProperty(
          "transform",
          "scale(1.05)",
          "important"
        );
        console.log(`应用选中状态到工具: ${tool}`);
      } else {
        // 非选中状态：白色背景，深色文字，浅色边框
        buttonElement.style.setProperty("background", "#fff", "important");
        buttonElement.style.setProperty("color", "#333", "important");
        buttonElement.style.setProperty(
          "border",
          "1px solid #ddd",
          "important"
        );
        buttonElement.style.setProperty("box-shadow", "none", "important");
        buttonElement.style.setProperty("transform", "scale(1)", "important");
        console.log(`应用非选中状态到工具: ${tool}`);
      }
    });

    // 更新格式选择器
    updateFormatSelector();
  };

  /**
   * 更新格式选择器显示
   */
  const updateFormatSelector = () => {
    if (!toolbarRef.value) return;

    const formatSelector = toolbarRef.value.querySelector(
      ".format-selector"
    ) as HTMLSelectElement;
    if (formatSelector) {
      formatSelector.value = currentFormat.value;
    }
  };

  /**
   * 显示工具栏
   */
  const showToolbar = (cutInfo: CutInfo) => {
    if (!screenshotContainer.value) return;

    // 如果配置为不显示工具栏，则直接返回
    if (toolbarConfig.showToolbar === false) return;

    // 移除旧工具栏
    hideToolbar();

    const toolbar = createToolbar();
    screenshotContainer.value.appendChild(toolbar);
    toolbarRef.value = toolbar;

    // 计算工具栏位置
    positionToolbar(toolbar, cutInfo);
  };

  /**
   * 隐藏工具栏
   */
  const hideToolbar = () => {
    if (toolbarRef.value) {
      toolbarRef.value.remove();
      toolbarRef.value = null;
    }
  };

  /**
   * 定位工具栏
   */
  const positionToolbar = (
    toolbar: HTMLElement,
    cutInfo: CutInfo,
    position: ToolbarPosition = "center"
  ) => {
    const toolbarRect = toolbar.getBoundingClientRect();
    const containerRect = screenshotContainer.value?.getBoundingClientRect();

    if (!containerRect) return;

    let left = 0;
    let top = cutInfo.startY + cutInfo.height + 10;

    // 计算水平位置
    switch (position) {
      case "left":
        left = cutInfo.startX;
        break;
      case "center":
        left = cutInfo.startX + (cutInfo.width - toolbarRect.width) / 2;
        break;
      case "right":
        left = cutInfo.startX + cutInfo.width - toolbarRect.width;
        break;
    }

    // 确保工具栏不超出容器边界
    const containerWidth = containerRect.width;
    const containerHeight = containerRect.height;
    const toolbarWidth = toolbarRect.width;
    const toolbarHeight = toolbarRect.height;

    // 水平边界检查
    const maxLeft = containerWidth - toolbarWidth - 10;
    left = Math.max(10, Math.min(left, maxLeft));

    // 垂直边界检查 - 如果工具栏超出底部，则显示在选择框上方
    if (top + toolbarHeight > containerHeight - 10) {
      top = cutInfo.startY - toolbarHeight - 10;
      // 如果上方也放不下，则放在选择框内部底部
      if (top < 10) {
        top = cutInfo.startY + cutInfo.height - toolbarHeight - 10;
      }
    }

    // 确保top不小于10
    top = Math.max(10, top);

    toolbar.style.left = `${left}px`;
    toolbar.style.top = `${top}px`;
  };

  /**
   * 更新工具栏位置（当选择框改变时调用）
   */
  const updateToolbarPosition = (
    cutInfo: CutInfo,
    position: ToolbarPosition = "center"
  ) => {
    if (toolbarRef.value) {
      positionToolbar(toolbarRef.value, cutInfo, position);
    }
  };

  return {
    toolbarRef,
    createToolbar,
    showToolbar,
    hideToolbar,
    selectTool,
    updateToolbarButtons,
    updateToolbarPosition,
    positionToolbar,
  };
}
