import type { ScreenshotConfig } from "./types";

/**
 * 截图工具默认配置
 */
export const defaultConfig: ScreenshotConfig = {
  /** 裁剪框边框颜色 */
  cutBoxBdColor: "#409eff",
  /** 工具栏背景色 */
  toolbarBgColor: "#ffffff",
  /** 工具栏文字颜色 */
  toolbarTextColor: "#333333",
  /** 遮罩层透明度 */
  maskOpacity: 0.6,
};

/**
 * 默认选框尺寸比例（相对于视口）
 */
export const DEFAULT_SELECTION_RATIO = 0.3;

/**
 * 调整点大小
 */
export const RESIZE_HANDLE_SIZE = 8;

/**
 * 最小选框尺寸
 */
export const MIN_SELECTION_SIZE = 10;
