import type { CutInfo, Point } from "./types";

/**
 * 计算两点之间的距离
 */
export function calculateDistance(point1: Point, point2: Point): number {
  return Math.sqrt(
    Math.pow(point2.x - point1.x, 2) + Math.pow(point2.y - point1.y, 2)
  );
}

/**
 * 限制数值在指定范围内
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

/**
 * 计算裁剪信息
 */
export function calculateCutInfo(
  startPoint: Point,
  currentPoint: Point
): CutInfo {
  const startX = Math.min(startPoint.x, currentPoint.x);
  const startY = Math.min(startPoint.y, currentPoint.y);
  const width = Math.abs(currentPoint.x - startPoint.x);
  const height = Math.abs(currentPoint.y - startPoint.y);

  return { startX, startY, width, height };
}

/**
 * 检查点是否在矩形内
 */
export function isPointInRect(point: Point, rect: CutInfo): boolean {
  return (
    point.x >= rect.startX &&
    point.x <= rect.startX + rect.width &&
    point.y >= rect.startY &&
    point.y <= rect.startY + rect.height
  );
}

/**
 * 获取Canvas相对坐标
 */
export function getCanvasRelativePoint(
  event: MouseEvent,
  canvas: HTMLCanvasElement
): Point {
  const rect = canvas.getBoundingClientRect();
  return {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top,
  };
}

/**
 * 创建默认选框（30%视口大小，居中显示）
 */
export function createDefaultSelection(
  viewportWidth: number,
  viewportHeight: number,
  ratio: number = 0.3
): CutInfo {
  const width = Math.floor(viewportWidth * ratio);
  const height = Math.floor(viewportHeight * ratio);
  const startX = Math.floor((viewportWidth - width) / 2);
  const startY = Math.floor((viewportHeight - height) / 2);

  return { startX, startY, width, height };
}

/**
 * 将base64转换为Blob
 */
export function base64ToBlob(base64: string, mimeType: string): Blob {
  const byteCharacters = atob(base64.split(",")[1]);
  const byteNumbers = new Array(byteCharacters.length);

  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }

  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mimeType });
}

/**
 * 下载文件
 */
export function downloadFile(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

/**
 * 复制到剪切板
 */
export async function copyToClipboard(blob: Blob): Promise<void> {
  try {
    if (navigator.clipboard && window.ClipboardItem) {
      const clipboardItem = new ClipboardItem({ [blob.type]: blob });
      await navigator.clipboard.write([clipboardItem]);
      console.log("图片已复制到剪切板");
    } else {
      console.warn("当前浏览器不支持剪切板API");
    }
  } catch (error) {
    console.error("复制到剪切板失败:", error);
  }
}
