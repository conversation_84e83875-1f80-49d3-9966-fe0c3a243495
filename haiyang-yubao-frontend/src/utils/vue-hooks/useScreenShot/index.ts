import { ref, reactive } from 'vue'
import { snapdom } from '@zumer/snapdom'
import type { ScreenshotOptions, ScreenshotState, CutInfo } from './types'
import { DEFAULT_SELECTION_RATIO } from './config'
import {
  createDefaultSelection,
  getCanvasRelativePoint,
  base64ToBlob,
  copyToClipboard,
  downloadFile
} from './utils'
import { useSelection } from './selection'
import { useToolbar } from './toolbar'
import { useDrawing } from './drawing'
import {
  CSS_CLASSES,
  DEFAULT_STYLES,
  DRAWING_TOOLS,
  BACKGROUND_COLORS
} from './constants'
import {
  createCanvas,
  createTempCanvas,
  getCanvasDataURL,
  getCanvasBlob,
  cropCanvas,
  setCanvasBackground
} from './canvas-utils'
import {
  bindCanvasEvents,
  bindContainerClickEvent,
  type EventHandlerConfig
} from './event-handlers'
import {
  logScreenshotStart,
  logViewportInfo,
  logSnapshotComplete,
  logCanvasDrawn,
  logCanvasAdded,
  logDefaultSelectionCreated,
  logScreenshotComplete,
  logScreenshotDownloaded,
  logScreenshotSavedToClipboard,
  logErrorMissingElements,
  logErrorConfirmFailed,
  logErrorDownloadFailed,
  logErrorCompleteFailed,
  generateTimestampFilename
} from './message-utils'
import { showSuccessToast, showErrorToast } from './toast'

/**
 * 截图Hook - 重构版本
 *
 * @example 仅显示格式选择、确认、取消、保存的配置
 * ```typescript
 * const screenshot = useScreenshot({
 *   format: "png",
 *   toolbarPosition: "center",
 *   toolbarConfig: {
 *     showToolbar: true,
 *     availableTools: [], // 不显示任何绘制工具
 *     showFormatSelector: true, // 显示格式选择器
 *   },
 *   completeCallback: (data) => {
 *     console.log('截图完成:', data.base64);
 *   }
 * });
 * ```
 */
export function useScreenshot(options: ScreenshotOptions = {}) {
  // 默认配置
  const defaultOptions: Required<ScreenshotOptions> = {
    format: 'png',
    mode: 'dom',
    toolbarPosition: 'center',
    toolbarConfig: {
      showToolbar: true,
      availableTools: [...DRAWING_TOOLS],
      showFormatSelector: true
    },
    historyConfig: {
      maxHistorySize: 10
    },
    clickMaskToClose: true,
    onlyCloseByButton: false,
    completeCallback: () => {},
    closeCallback: () => {}
  }

  const config = { ...defaultOptions, ...options }

  // 状态管理
  const state = reactive<ScreenshotState>({
    isCapturing: false,
    currentTool: 'select', // 默认为select，但不会在工具栏中显示为选中
    currentFormat: config.format,
    cutInfo: null,
    drawHistory: [],
    historyIndex: -1,
    canUndo: false,
    canRedo: false
  })

  // DOM引用
  const screenshotContainer = ref<HTMLElement | null>(null)
  const canvas = ref<HTMLCanvasElement | null>(null)
  const currentTool = ref(state.currentTool)
  const currentFormat = ref(state.currentFormat)

  // 先声明函数引用
  let toolbar: any
  let selection: any
  let drawing: any

  /**
   * 保存当前canvas状态到历史记录
   */
  const saveToHistory = () => {
    if (!canvas.value) return

    const ctx = canvas.value.getContext('2d')
    if (!ctx) return

    const imageData = ctx.getImageData(
      0,
      0,
      canvas.value.width,
      canvas.value.height
    )

    // 如果当前不在历史记录末尾，删除后面的记录
    if (state.historyIndex < state.drawHistory.length - 1) {
      state.drawHistory = state.drawHistory.slice(0, state.historyIndex + 1)
    }

    // 添加新的历史记录
    state.drawHistory.push(imageData)

    // 限制历史记录数量
    const maxSize = config.historyConfig.maxHistorySize || 10
    if (state.drawHistory.length > maxSize) {
      state.drawHistory = state.drawHistory.slice(-maxSize)
    }

    state.historyIndex = state.drawHistory.length - 1
    updateHistoryState()
  }

  /**
   * 更新历史记录状态
   */
  const updateHistoryState = () => {
    state.canUndo = state.historyIndex > 0
    state.canRedo = state.historyIndex < state.drawHistory.length - 1
  }

  /**
   * 撤销操作
   */
  const undo = () => {
    if (!state.canUndo || !canvas.value) return

    state.historyIndex--
    const imageData = state.drawHistory[state.historyIndex]

    const ctx = canvas.value.getContext('2d')
    if (ctx && imageData) {
      ctx.putImageData(imageData, 0, 0)
    }

    updateHistoryState()
    console.log(`撤销操作，当前历史索引: ${state.historyIndex}`)
  }

  /**
   * 重做操作
   */
  const redo = () => {
    if (!state.canRedo || !canvas.value) return

    state.historyIndex++
    const imageData = state.drawHistory[state.historyIndex]

    const ctx = canvas.value.getContext('2d')
    if (ctx && imageData) {
      ctx.putImageData(imageData, 0, 0)
    }

    updateHistoryState()
    console.log(`重做操作，当前历史索引: ${state.historyIndex}`)
  }

  /**
   * 创建标准化Canvas（确保一致的背景和编码）
   */
  const createStandardizedCanvas = (
    imageData: ImageData
  ): HTMLCanvasElement => {
    const { width, height } = imageData
    const { canvas: tempCanvas, ctx: tempCtx } = createTempCanvas(width, height)

    // 设置Canvas属性确保一致性
    tempCtx.imageSmoothingEnabled = false
    tempCtx.globalCompositeOperation = 'source-over'

    // 设置纯白色背景，确保在所有平台上显示一致
    tempCtx.fillStyle = '#ffffff'
    tempCtx.fillRect(0, 0, width, height)

    // 创建一个新的Canvas来处理透明度
    const sourceCanvas = document.createElement('canvas')
    sourceCanvas.width = width
    sourceCanvas.height = height
    const sourceCtx = sourceCanvas.getContext('2d')
    if (sourceCtx) {
      sourceCtx.putImageData(imageData, 0, 0)

      // 使用source-over模式绘制到白色背景上
      tempCtx.globalCompositeOperation = 'source-over'
      tempCtx.drawImage(sourceCanvas, 0, 0)
    }

    return tempCanvas
  }

  /**
   * 确认截图操作
   */
  const confirmScreenshot = async () => {
    if (!canvas.value || !selection.cutInfo.value) return

    try {
      // 获取选择框区域的图片数据
      const ctx = canvas.value.getContext('2d')
      if (!ctx) return

      const { startX, startY, width, height } = selection.cutInfo.value
      const imageData = ctx.getImageData(startX, startY, width, height)

      // 创建标准化的canvas（确保背景一致）
      const standardizedCanvas = createStandardizedCanvas(imageData)

      // 转换为blob并保存到剪切板（使用统一的编码参数）
      const mimeType =
        currentFormat.value === 'jpg'
          ? 'image/jpeg'
          : `image/${currentFormat.value}`
      const quality = currentFormat.value === 'jpg' ? 0.95 : undefined

      standardizedCanvas.toBlob(
        async blob => {
          if (blob) {
            try {
              await copyToClipboard(blob)
              logScreenshotSavedToClipboard()

              // 显示成功提示
              showSuccessToast('截图已保存到剪切板')

              // 调用完成回调
              const base64 = standardizedCanvas.toDataURL(mimeType, quality)
              config.completeCallback({ base64, blob })

              // 关闭截图
              cancelScreenshot()
            } catch (error) {
              console.error('保存到剪切板失败:', error)
              showErrorToast('保存到剪切板失败')
            }
          }
        },
        mimeType,
        quality
      )
    } catch (error) {
      console.error('确认截图失败:', error)
    }
  }

  /**
   * 取消截图操作
   */
  const cancelScreenshot = () => {
    // 清理状态
    state.isCapturing = false
    state.currentTool = 'select'
    state.drawHistory = []
    state.historyIndex = -1
    state.canUndo = false
    state.canRedo = false

    // 移除截图容器
    if (screenshotContainer.value) {
      screenshotContainer.value.remove()
      screenshotContainer.value = null
    }

    // 重置canvas引用
    canvas.value = null

    // 调用关闭回调
    config.closeCallback()

    console.log('截图操作已取消')
  }

  /**
   * 下载截图文件
   */
  const downloadScreenshot = async () => {
    if (!canvas.value || !selection.cutInfo.value) return

    try {
      // 获取选择框区域的图片数据
      const ctx = canvas.value.getContext('2d')
      if (!ctx) return

      const { startX, startY, width, height } = selection.cutInfo.value
      const imageData = ctx.getImageData(startX, startY, width, height)

      // 创建标准化的canvas（确保与剪切板一致）
      const standardizedCanvas = createStandardizedCanvas(imageData)

      // 转换为blob并下载（使用与剪切板完全相同的编码参数）
      const mimeType =
        currentFormat.value === 'jpg'
          ? 'image/jpeg'
          : `image/${currentFormat.value}`
      const quality = currentFormat.value === 'jpg' ? 0.95 : undefined

      standardizedCanvas.toBlob(
        blob => {
          if (blob) {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
            const filename = `screenshot-${timestamp}.${currentFormat.value}`
            downloadFile(blob, filename)
            console.log(`截图已下载: ${filename}`)
          }
        },
        mimeType,
        quality
      )
    } catch (error) {
      console.error('下载截图失败:', error)
    }
  }

  // 初始化功能模块
  toolbar = useToolbar(
    screenshotContainer,
    currentTool,
    currentFormat,
    config.toolbarConfig,
    {
      onUndo: () => undo(),
      onRedo: () => redo(),
      onConfirm: () => confirmScreenshot(),
      onCancel: () => cancelScreenshot(),
      onDownload: () => downloadScreenshot()
    }
  )

  selection = useSelection(
    screenshotContainer,
    canvas,
    currentTool,
    cutInfo => {
      // 当选择框变化时，更新工具栏位置
      toolbar.updateToolbarPosition(cutInfo, config.toolbarPosition)
    }
  )

  drawing = useDrawing(canvas, currentTool, selection.cutInfo, () =>
    saveToHistory()
  )

  /**
   * 开始截图
   */
  const initScreenshot = async () => {
    try {
      logScreenshotStart()
      state.isCapturing = true

      // 获取视口信息
      const scrollX = window.pageXOffset || document.documentElement.scrollLeft
      const scrollY = window.pageYOffset || document.documentElement.scrollTop
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      logViewportInfo(scrollX, scrollY, viewportWidth, viewportHeight)

      // 使用snapdom捕获整个页面
      const fullPageResult = await snapdom(document.body, {
        width: document.body.scrollWidth,
        height: document.body.scrollHeight,
        backgroundColor: '#ffffff'
      })

      const fullPageDataUrl = fullPageResult.url

      logSnapshotComplete()

      // 创建canvas编辑界面
      await createCanvasEditor(
        fullPageDataUrl,
        scrollX,
        scrollY,
        viewportWidth,
        viewportHeight
      )
    } catch (error) {
      console.error('截图失败:', error)
      state.isCapturing = false
    }
  }

  /**
   * 创建Canvas编辑界面
   */
  const createCanvasEditor = async (
    fullPageDataUrl: string,
    scrollX: number,
    scrollY: number,
    viewportWidth: number,
    viewportHeight: number
  ) => {
    // 创建图片对象
    const img = new Image()

    return new Promise<void>(resolve => {
      img.onload = () => {
        // 创建临时canvas来裁剪视口区域
        const tempCanvas = document.createElement('canvas')
        const tempCtx = tempCanvas.getContext('2d')
        if (!tempCtx) return

        tempCanvas.width = viewportWidth
        tempCanvas.height = viewportHeight

        // 裁剪视口区域
        tempCtx.drawImage(
          img,
          scrollX,
          scrollY,
          viewportWidth,
          viewportHeight,
          0,
          0,
          viewportWidth,
          viewportHeight
        )

        logCanvasDrawn()

        // 创建全屏编辑界面
        createFullscreenEditor(
          tempCanvas.toDataURL(),
          viewportWidth,
          viewportHeight
        )
        resolve()
      }

      img.src = fullPageDataUrl
    })
  }

  /**
   * 创建全屏编辑界面
   */
  const createFullscreenEditor = (
    viewportDataUrl: string,
    width: number,
    height: number
  ) => {
    // 创建全屏容器
    const container = document.createElement('div')
    container.className = 'screenshot-container'
    container.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.8);
      z-index: 9999;
      cursor: crosshair;
    `

    // 创建canvas
    const canvasElement = document.createElement('canvas')
    canvasElement.width = width
    canvasElement.height = height
    canvasElement.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      cursor: crosshair;
    `

    // 绘制截图到canvas
    const ctx = canvasElement.getContext('2d')
    if (ctx) {
      const img = new Image()
      img.onload = () => {
        ctx.drawImage(img, 0, 0)

        // 初始化绘制功能
        drawing.initDrawing()

        // 创建默认选择框（30%大小，居中）
        const defaultCutInfo = createDefaultSelection(
          width,
          height,
          DEFAULT_SELECTION_RATIO
        )
        selection.createSelectionBox(defaultCutInfo)

        // 显示工具栏
        toolbar.showToolbar(defaultCutInfo)

        // 保存初始状态到历史记录
        saveToHistory()

        logDefaultSelectionCreated(defaultCutInfo)
      }
      img.src = viewportDataUrl
    }

    // 添加事件监听
    setupEventListeners(canvasElement, container)

    // 添加到DOM
    container.appendChild(canvasElement)
    document.body.appendChild(container)

    // 保存引用
    screenshotContainer.value = container
    canvas.value = canvasElement

    logCanvasAdded()
  }

  /**
   * 更新光标样式
   */
  const updateCursor = (e: MouseEvent, container: HTMLElement) => {
    const target = e.target as HTMLElement

    // 如果在工具栏内，保持默认光标
    if (target.closest('.screenshot-toolbar')) {
      container.style.cursor = 'default'
      return
    }

    // 如果在调整点上，使用调整点的光标
    if (target.classList.contains('resize-handle')) {
      return // 调整点自己会设置光标
    }

    // 如果在选择框内
    const selectionBox = container.querySelector(
      '.selection-box'
    ) as HTMLElement
    if (selectionBox) {
      const rect = selectionBox.getBoundingClientRect()
      const containerRect = container.getBoundingClientRect()
      const x = e.clientX - containerRect.left
      const y = e.clientY - containerRect.top

      const selectionRect = {
        left: rect.left - containerRect.left,
        top: rect.top - containerRect.top,
        right: rect.right - containerRect.left,
        bottom: rect.bottom - containerRect.top
      }

      if (
        x >= selectionRect.left &&
        x <= selectionRect.right &&
        y >= selectionRect.top &&
        y <= selectionRect.bottom
      ) {
        // 在选择框内，根据当前工具设置光标
        if (currentTool.value === 'select') {
          container.style.cursor = 'move' // 四向移动光标
        } else {
          container.style.cursor = 'crosshair' // 绘制工具光标
        }
        return
      }
    }

    // 在选择框外，显示禁用光标
    container.style.cursor = 'not-allowed'
  }

  /**
   * 设置事件监听
   */
  const setupEventListeners = (
    canvasElement: HTMLCanvasElement,
    container: HTMLElement
  ) => {
    let isMouseDown = false
    let startPoint = { x: 0, y: 0 }

    // 鼠标按下事件
    const handleMouseDown = (e: MouseEvent) => {
      const target = e.target as HTMLElement

      // 如果点击的是调整点，不处理
      if (target.classList.contains('resize-handle')) {
        return
      }

      // 如果点击的是工具栏，不处理
      if (target.closest('.screenshot-toolbar')) {
        return
      }

      const point = getCanvasRelativePoint(e, canvasElement)

      // 检查是否为绘制工具
      const isDrawingTool = DRAWING_TOOLS.includes(currentTool.value as any)

      if (isDrawingTool) {
        // 绘制工具 - 检查是否在选择框内
        if (selection.cutInfo.value) {
          const { startX, startY, width, height } = selection.cutInfo.value
          if (
            point.x >= startX &&
            point.x <= startX + width &&
            point.y >= startY &&
            point.y <= startY + height
          ) {
            // 在选择框内，可以进行绘制
            isMouseDown = true
            startPoint = point
            drawing.startDrawing(point)
          }
          // 在选择框外，不进行任何操作
        }
      } else {
        // 非绘制工具（select模式），检查是否在选择框内进行拖动
        if (selection.cutInfo.value) {
          const { startX, startY, width, height } = selection.cutInfo.value
          if (
            point.x >= startX &&
            point.x <= startX + width &&
            point.y >= startY &&
            point.y <= startY + height
          ) {
            // 在选择框内，开始拖动
            console.log('在选择框内点击，开始拖动')
            selection.startDrag(e)
          }
          // 在选择框外，不进行任何操作
        }
      }
    }

    // 鼠标移动事件
    const handleMouseMove = (e: MouseEvent) => {
      // 更新光标样式
      updateCursor(e, container)

      if (!isMouseDown) return

      const point = getCanvasRelativePoint(e, canvasElement)
      drawing.continueDrawing(point)
    }

    // 鼠标释放事件
    const handleMouseUp = (e: MouseEvent) => {
      if (!isMouseDown) return

      isMouseDown = false
      drawing.finishDrawing()
    }

    // 键盘事件
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        closeScreenshot()
      } else if (e.key === 'z' && (e.ctrlKey || e.metaKey)) {
        drawing.undo()
      }
    }

    // 绑定事件 - 绑定到容器而不是canvas，这样选择框内的点击也能被捕获
    container.addEventListener('mousedown', handleMouseDown)
    container.addEventListener('mousemove', handleMouseMove)
    container.addEventListener('mouseup', handleMouseUp)
    document.addEventListener('keydown', handleKeyDown)

    // 容器点击事件（用于关闭）
    if (config.clickMaskToClose && !config.onlyCloseByButton) {
      container.addEventListener('click', e => {
        if (e.target === container) {
          closeScreenshot()
        }
      })
    }

    // 绑定工具栏事件
    setupToolbarEvents()
  }

  /**
   * 设置工具栏事件
   */
  const setupToolbarEvents = () => {
    if (!screenshotContainer.value) return

    // 使用事件委托处理工具栏按钮点击
    screenshotContainer.value.addEventListener('click', e => {
      const target = e.target as HTMLElement

      // 处理操作按钮
      if (target.classList.contains('action-button')) {
        e.stopPropagation()
        e.preventDefault()

        const action = target.className.match(/action-(\w+)/)?.[1]
        handleToolbarAction(action || '')
      }
    })
  }

  /**
   * 处理工具栏操作
   */
  const handleToolbarAction = (action: string) => {
    switch (action) {
      case 'undo':
        drawing.undo()
        break
      case 'confirm':
        completeScreenshot()
        break
      case 'cancel':
        closeScreenshot()
        break
      case 'download':
        downloadScreenshot()
        break
      default:
        console.log('未知操作:', action)
    }
  }

  /**
   * 关闭截图
   */
  const closeScreenshot = () => {
    if (screenshotContainer.value) {
      screenshotContainer.value.remove()
      screenshotContainer.value = null
    }

    canvas.value = null
    state.isCapturing = false
    state.cutInfo = null

    // 清理绘制历史
    drawing.clearHistory()

    // 调用关闭回调
    config.closeCallback()

    console.log('截图已关闭')
  }

  /**
   * 完成截图
   */
  const completeScreenshot = async () => {
    if (!canvas.value || !selection.cutInfo.value) {
      console.error('无法完成截图：缺少必要的元素')
      return
    }

    try {
      const ctx = canvas.value.getContext('2d')
      if (!ctx) return

      const cutInfo = selection.cutInfo.value

      // 创建结果canvas
      const resultCanvas = document.createElement('canvas')
      resultCanvas.width = cutInfo.width
      resultCanvas.height = cutInfo.height

      const resultCtx = resultCanvas.getContext('2d')
      if (!resultCtx) return

      // 裁剪选中区域
      resultCtx.drawImage(
        canvas.value,
        cutInfo.startX,
        cutInfo.startY,
        cutInfo.width,
        cutInfo.height,
        0,
        0,
        cutInfo.width,
        cutInfo.height
      )

      // 转换为指定格式
      const mimeType = `image/${
        currentFormat.value === 'jpg' ? 'jpeg' : currentFormat.value
      }`
      const base64 = resultCanvas.toDataURL(mimeType, 0.9)
      const blob = base64ToBlob(base64, mimeType)

      // 复制到剪切板
      await copyToClipboard(blob)

      // 调用完成回调
      config.completeCallback({ base64, blob })

      // 关闭截图界面
      closeScreenshot()

      console.log('截图完成并已复制到剪切板')
    } catch (error) {
      console.error('完成截图时出错:', error)
    }
  }

  return {
    state,
    initScreenshot,
    closeScreenshot,
    completeScreenshot,
    // 暴露子模块功能
    selection,
    toolbar,
    drawing
  }
}
