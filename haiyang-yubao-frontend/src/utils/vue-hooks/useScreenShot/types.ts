/**
 * 截图工具相关类型定义
 */

/**
 * 图片格式类型
 */
export type ImageFormat = "png" | "jpg" | "webp" | "svg";

/**
 * 绘制工具类型
 */
export type DrawTool =
  | "select" // 选择工具
  | "rectangle" // 矩形
  | "circle" // 圆形
  | "arrow" // 箭头
  | "brush" // 画笔
  | "mosaic" // 马赛克
  | "text"; // 文字

/**
 * 工具栏位置
 */
export type ToolbarPosition = "left" | "center" | "right";

/**
 * 截图模式
 */
export type ScreenshotMode = "dom" | "webrtc";

/**
 * 裁剪信息
 */
export interface CutInfo {
  startX: number;
  startY: number;
  width: number;
  height: number;
}

/**
 * 工具栏配置
 */
export interface ToolbarConfig {
  /** 是否显示工具栏 */
  showToolbar?: boolean;
  /** 可用的工具列表 */
  availableTools?: DrawTool[];
  /** 是否显示格式选择器 */
  showFormatSelector?: boolean;
}

/**
 * 历史记录配置
 */
export interface HistoryConfig {
  /** 最大历史记录数量 */
  maxHistorySize?: number;
}

/**
 * 截图配置选项
 */
export interface ScreenshotOptions {
  /** 默认图片格式 */
  format?: ImageFormat;
  /** 截图模式 */
  mode?: ScreenshotMode;
  /** 工具栏位置 */
  toolbarPosition?: ToolbarPosition;
  /** 工具栏配置 */
  toolbarConfig?: ToolbarConfig;
  /** 历史记录配置 */
  historyConfig?: HistoryConfig;
  /** 是否允许点击遮罩关闭 */
  clickMaskToClose?: boolean;
  /** 是否只能通过按钮关闭 */
  onlyCloseByButton?: boolean;
  /** 完成回调 */
  completeCallback?: (data: { base64: string; blob: Blob }) => void;
  /** 关闭回调 */
  closeCallback?: () => void;
}

/**
 * 截图状态
 */
export interface ScreenshotState {
  /** 是否正在截图 */
  isCapturing: boolean;
  /** 当前工具 */
  currentTool: DrawTool;
  /** 当前格式 */
  currentFormat: ImageFormat;
  /** 裁剪信息 */
  cutInfo: CutInfo | null;
  /** 绘制历史 */
  drawHistory: ImageData[];
  /** 当前历史记录索引 */
  historyIndex: number;
  /** 是否可以撤销 */
  canUndo: boolean;
  /** 是否可以重做 */
  canRedo: boolean;
}

/**
 * 坐标点
 */
export interface Point {
  x: number;
  y: number;
}

/**
 * 调整点位置
 */
export type ResizeDirection = "nw" | "n" | "ne" | "w" | "e" | "sw" | "s" | "se";

/**
 * 调整点信息
 */
export interface ResizeHandle {
  position: ResizeDirection;
  x: number;
  y: number;
  cursor: string;
}

/**
 * 截图配置
 */
export interface ScreenshotConfig {
  /** 裁剪框边框颜色 */
  cutBoxBdColor: string;
  /** 工具栏背景色 */
  toolbarBgColor: string;
  /** 工具栏文字颜色 */
  toolbarTextColor: string;
  /** 遮罩层透明度 */
  maskOpacity: number;
}
