import { CONSOLE_MESSAGES } from './constants'

/**
 * 显示成功提示的Toast组件
 * @param message 提示消息
 * @param duration 显示时长（毫秒）
 */
export function showSuccessToast(message: string, duration = 3000): void {
  console.log(CONSOLE_MESSAGES.TOAST_SHOW, message)

  // 创建toast容器
  const toast = document.createElement('div')
  toast.className = 'screenshot-toast'
  toast.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 300px;
    text-align: center;
    line-height: 1.4;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  `

  // 添加成功图标和消息
  toast.innerHTML = `
    <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
      <span style="color: #4CAF50; font-size: 16px;">✓</span>
      <span>${message}</span>
    </div>
  `

  // 添加到页面
  document.body.appendChild(toast)

  // 显示动画
  requestAnimationFrame(() => {
    toast.style.opacity = '1'
  })

  // 自动隐藏
  setTimeout(() => {
    toast.style.opacity = '0'
    setTimeout(() => {
      if (toast.parentNode) {
        document.body.removeChild(toast)
      }
    }, 300)
  }, duration)
}

/**
 * 显示错误提示的Toast组件
 * @param message 错误消息
 * @param duration 显示时长（毫秒）
 */
export function showErrorToast(message: string, duration = 3000): void {
  console.log(CONSOLE_MESSAGES.TOAST_SHOW, message)

  // 创建toast容器
  const toast = document.createElement('div')
  toast.className = 'screenshot-toast-error'
  toast.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(244, 67, 54, 0.9);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 300px;
    text-align: center;
    line-height: 1.4;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  `

  // 添加错误图标和消息
  toast.innerHTML = `
    <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
      <span style="color: #fff; font-size: 16px;">✕</span>
      <span>${message}</span>
    </div>
  `

  // 添加到页面
  document.body.appendChild(toast)

  // 显示动画
  requestAnimationFrame(() => {
    toast.style.opacity = '1'
  })

  // 自动隐藏
  setTimeout(() => {
    toast.style.opacity = '0'
    setTimeout(() => {
      if (toast.parentNode) {
        document.body.removeChild(toast)
      }
    }, 300)
  }, duration)
}
