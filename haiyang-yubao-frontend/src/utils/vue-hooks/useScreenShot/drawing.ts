import { ref, type Ref } from 'vue'
import type { DrawTool, Point, CutInfo } from './types'

/**
 * 绘制工具功能
 */
export function useDrawing(
  canvas: Ref<HTMLCanvasElement | null>,
  currentTool: Ref<DrawTool>,
  cutInfo: Ref<CutInfo | null>,
  onDrawComplete?: () => void
) {
  // 绘制状态
  const isDrawing = ref(false)
  const drawHistory = ref<ImageData[]>([])
  const startPoint = ref<Point>({ x: 0, y: 0 })
  const currentPoint = ref<Point>({ x: 0, y: 0 })

  /**
   * 检查点是否在选择框内
   */
  const isPointInSelection = (point: Point): boolean => {
    if (!cutInfo.value) return false

    const { startX, startY, width, height } = cutInfo.value
    return (
      point.x >= startX &&
      point.x <= startX + width &&
      point.y >= startY &&
      point.y <= startY + height
    )
  }

  /**
   * 开始绘制
   */
  const startDrawing = (point: Point) => {
    if (!canvas.value || currentTool.value === 'select') return

    // 检查是否在选择框内
    if (!isPointInSelection(point)) {
      console.log('绘制点不在选择框内，忽略绘制')
      return
    }

    const ctx = canvas.value.getContext('2d')
    if (!ctx) return

    isDrawing.value = true
    startPoint.value = point
    currentPoint.value = point

    // 保存当前状态用于撤销
    saveDrawState()

    console.log(`开始绘制 ${currentTool.value}:`, point)
  }

  /**
   * 继续绘制
   */
  const continueDrawing = (point: Point) => {
    if (!isDrawing.value || !canvas.value || currentTool.value === 'select')
      return

    // 检查是否在选择框内
    if (!isPointInSelection(point)) {
      return
    }

    currentPoint.value = point

    // 根据工具类型进行绘制
    switch (currentTool.value) {
      case 'brush':
        drawBrush(point)
        break
      case 'rectangle':
      case 'circle':
      case 'arrow':
        // 这些工具需要实时预览
        redrawWithPreview()
        break
      case 'mosaic':
        drawMosaic(point)
        break
    }
  }

  /**
   * 完成绘制
   */
  const finishDrawing = () => {
    if (!isDrawing.value || !canvas.value) return

    const ctx = canvas.value.getContext('2d')
    if (!ctx) return

    // 根据工具类型完成绘制
    switch (currentTool.value) {
      case 'rectangle':
        drawRectangle(ctx, startPoint.value, currentPoint.value)
        break
      case 'circle':
        drawCircle(ctx, startPoint.value, currentPoint.value)
        break
      case 'arrow':
        drawArrow(ctx, startPoint.value, currentPoint.value)
        break
      case 'text':
        drawText()
        break
    }

    isDrawing.value = false
    console.log(`完成绘制 ${currentTool.value}`)

    // 保存到历史记录
    if (onDrawComplete) {
      onDrawComplete()
    }
  }

  /**
   * 画笔绘制
   */
  const drawBrush = (point: Point) => {
    if (!canvas.value) return

    const ctx = canvas.value.getContext('2d')
    if (!ctx) return

    ctx.globalCompositeOperation = 'source-over'
    ctx.strokeStyle = '#ff0000'
    ctx.lineWidth = 3
    ctx.lineCap = 'round'
    ctx.lineJoin = 'round'

    if (startPoint.value.x === point.x && startPoint.value.y === point.y) {
      // 第一个点
      ctx.beginPath()
      ctx.moveTo(point.x, point.y)
    } else {
      // 连续绘制
      ctx.lineTo(point.x, point.y)
      ctx.stroke()
    }

    startPoint.value = point
  }

  /**
   * 绘制矩形
   */
  const drawRectangle = (
    ctx: CanvasRenderingContext2D,
    start: Point,
    end: Point
  ) => {
    const width = end.x - start.x
    const height = end.y - start.y

    ctx.globalCompositeOperation = 'source-over'
    ctx.strokeStyle = '#ff0000'
    ctx.lineWidth = 2
    ctx.strokeRect(start.x, start.y, width, height)
  }

  /**
   * 绘制圆形
   */
  const drawCircle = (
    ctx: CanvasRenderingContext2D,
    start: Point,
    end: Point
  ) => {
    const radius = Math.sqrt(
      Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2)
    )

    ctx.globalCompositeOperation = 'source-over'
    ctx.strokeStyle = '#ff0000'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.arc(start.x, start.y, radius, 0, 2 * Math.PI)
    ctx.stroke()
  }

  /**
   * 绘制箭头
   */
  const drawArrow = (
    ctx: CanvasRenderingContext2D,
    start: Point,
    end: Point
  ) => {
    const headLength = 15
    const angle = Math.atan2(end.y - start.y, end.x - start.x)

    ctx.globalCompositeOperation = 'source-over'
    ctx.strokeStyle = '#ff0000'
    ctx.lineWidth = 2
    ctx.lineCap = 'round'

    // 绘制主线
    ctx.beginPath()
    ctx.moveTo(start.x, start.y)
    ctx.lineTo(end.x, end.y)
    ctx.stroke()

    // 绘制箭头头部
    ctx.beginPath()
    ctx.moveTo(end.x, end.y)
    ctx.lineTo(
      end.x - headLength * Math.cos(angle - Math.PI / 6),
      end.y - headLength * Math.sin(angle - Math.PI / 6)
    )
    ctx.moveTo(end.x, end.y)
    ctx.lineTo(
      end.x - headLength * Math.cos(angle + Math.PI / 6),
      end.y - headLength * Math.sin(angle + Math.PI / 6)
    )
    ctx.stroke()
  }

  /**
   * 绘制马赛克
   */
  const drawMosaic = (point: Point) => {
    if (!canvas.value) return

    const ctx = canvas.value.getContext('2d')
    if (!ctx) return

    const size = 10
    const imageData = ctx.getImageData(
      point.x - size / 2,
      point.y - size / 2,
      size,
      size
    )

    // 简单的马赛克效果：取平均颜色
    const data = imageData.data
    let r = 0,
      g = 0,
      b = 0
    const pixelCount = data.length / 4

    for (let i = 0; i < data.length; i += 4) {
      r += data[i]
      g += data[i + 1]
      b += data[i + 2]
    }

    r = Math.floor(r / pixelCount)
    g = Math.floor(g / pixelCount)
    b = Math.floor(b / pixelCount)

    ctx.fillStyle = `rgb(${r}, ${g}, ${b})`
    ctx.fillRect(point.x - size / 2, point.y - size / 2, size, size)
  }

  /**
   * 绘制文字
   */
  const drawText = () => {
    const text = prompt('请输入文字:')
    if (!text || !canvas.value) return

    const ctx = canvas.value.getContext('2d')
    if (!ctx) return

    ctx.globalCompositeOperation = 'source-over'
    ctx.fillStyle = '#ff0000'
    ctx.font = '16px Arial'
    ctx.fillText(text, startPoint.value.x, startPoint.value.y)
  }

  /**
   * 重绘并显示预览
   */
  const redrawWithPreview = () => {
    if (!canvas.value || drawHistory.value.length === 0) return

    const ctx = canvas.value.getContext('2d')
    if (!ctx) return

    // 恢复到绘制前的状态
    const lastState = drawHistory.value[drawHistory.value.length - 1]
    ctx.putImageData(lastState, 0, 0)

    // 绘制预览
    switch (currentTool.value) {
      case 'rectangle':
        drawRectangle(ctx, startPoint.value, currentPoint.value)
        break
      case 'circle':
        drawCircle(ctx, startPoint.value, currentPoint.value)
        break
      case 'arrow':
        drawArrow(ctx, startPoint.value, currentPoint.value)
        break
    }
  }

  /**
   * 保存绘制状态
   */
  const saveDrawState = () => {
    if (!canvas.value) return

    const ctx = canvas.value.getContext('2d')
    if (!ctx) return

    const imageData = ctx.getImageData(
      0,
      0,
      canvas.value.width,
      canvas.value.height
    )
    drawHistory.value.push(imageData)

    // 限制历史记录数量
    if (drawHistory.value.length > 10) {
      drawHistory.value.shift()
    }
  }

  /**
   * 撤销上一步操作
   */
  const undo = () => {
    if (!canvas.value || drawHistory.value.length <= 1) return

    const ctx = canvas.value.getContext('2d')
    if (!ctx) return

    // 移除当前状态
    drawHistory.value.pop()

    // 恢复到上一个状态
    if (drawHistory.value.length > 0) {
      const lastState = drawHistory.value[drawHistory.value.length - 1]
      ctx.putImageData(lastState, 0, 0)
    }

    console.log('撤销操作完成')
  }

  /**
   * 清空绘制历史
   */
  const clearHistory = () => {
    drawHistory.value = []
  }

  /**
   * 初始化绘制状态
   */
  const initDrawing = () => {
    if (!canvas.value) return

    // 保存初始状态
    saveDrawState()
  }

  return {
    isDrawing,
    drawHistory,
    startDrawing,
    continueDrawing,
    finishDrawing,
    undo,
    clearHistory,
    initDrawing,
    canUndo: () => drawHistory.value.length > 1
  }
}
