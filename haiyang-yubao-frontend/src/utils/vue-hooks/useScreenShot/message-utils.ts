/**
 * 消息工具函数
 */
import { CONSOLE_MESSAGES } from './constants'
import type { CutInfo } from './types'

/**
 * 格式化消息模板
 * @param template 消息模板
 * @param args 参数数组
 * @returns 格式化后的消息
 */
export function formatMessage(
  template: string,
  ...args: (string | number)[]
): string {
  return template.replace(/\{(\d+)\}/g, (match, index) => {
    const argIndex = parseInt(index, 10)
    return args[argIndex]?.toString() || match
  })
}

/**
 * 记录视口信息
 * @param scrollX 滚动X位置
 * @param scrollY 滚动Y位置
 * @param width 视口宽度
 * @param height 视口高度
 */
export function logViewportInfo(
  scrollX: number,
  scrollY: number,
  width: number,
  height: number
): void {
  const message = formatMessage(
    CONSOLE_MESSAGES.VIEWPORT_INFO,
    scrollX,
    scrollY,
    width,
    height
  )
  console.log(message)
}

/**
 * 记录截图开始
 */
export function logScreenshotStart(): void {
  console.log(CONSOLE_MESSAGES.SCREENSHOT_START)
}

/**
 * 记录快照完成
 */
export function logSnapshotComplete(): void {
  console.log(CONSOLE_MESSAGES.SNAPSHOT_COMPLETE)
}

/**
 * 记录Canvas绘制完成
 */
export function logCanvasDrawn(): void {
  console.log(CONSOLE_MESSAGES.CANVAS_DRAWN)
}

/**
 * 记录Canvas添加到DOM
 */
export function logCanvasAdded(): void {
  console.log(CONSOLE_MESSAGES.CANVAS_ADDED)
}

/**
 * 记录默认选择框创建
 * @param cutInfo 选择框信息
 */
export function logDefaultSelectionCreated(cutInfo: CutInfo): void {
  console.log(CONSOLE_MESSAGES.DEFAULT_SELECTION_CREATED, cutInfo)
}

/**
 * 记录截图完成
 */
export function logScreenshotComplete(): void {
  console.log(CONSOLE_MESSAGES.SCREENSHOT_COMPLETE)
}

/**
 * 记录截图下载
 * @param filename 文件名
 */
export function logScreenshotDownloaded(filename: string): void {
  const message = formatMessage(
    CONSOLE_MESSAGES.SCREENSHOT_DOWNLOADED,
    filename
  )
  console.log(message)
}

/**
 * 记录截图保存到剪切板
 */
export function logScreenshotSavedToClipboard(): void {
  console.log(CONSOLE_MESSAGES.SCREENSHOT_SAVED_TO_CLIPBOARD)
}

/**
 * 记录截图取消
 */
export function logScreenshotCancelled(): void {
  console.log(CONSOLE_MESSAGES.SCREENSHOT_CANCELLED)
}

/**
 * 记录错误：缺少必要元素
 */
export function logErrorMissingElements(): void {
  console.error(CONSOLE_MESSAGES.ERROR_MISSING_ELEMENTS)
}

/**
 * 记录确认截图失败
 * @param error 错误信息
 */
export function logErrorConfirmFailed(error: unknown): void {
  console.error(CONSOLE_MESSAGES.ERROR_CONFIRM_FAILED, error)
}

/**
 * 记录下载截图失败
 * @param error 错误信息
 */
export function logErrorDownloadFailed(error: unknown): void {
  console.error(CONSOLE_MESSAGES.ERROR_DOWNLOAD_FAILED, error)
}

/**
 * 记录完成截图失败
 * @param error 错误信息
 */
export function logErrorCompleteFailed(error: unknown): void {
  console.error(CONSOLE_MESSAGES.ERROR_COMPLETE_FAILED, error)
}

/**
 * 生成时间戳文件名
 * @param format 文件格式
 * @returns 文件名
 */
export function generateTimestampFilename(format: string): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  return `screenshot-${timestamp}.${format}`
}
