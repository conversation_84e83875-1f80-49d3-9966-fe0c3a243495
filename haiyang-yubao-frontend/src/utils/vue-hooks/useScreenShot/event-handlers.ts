/**
 * 事件处理工具函数
 */
import type { Ref } from 'vue'
import type { DrawTool, CutInfo } from './types'
import {
  CSS_CLASSES,
  CURSOR_STYLES,
  EVENT_TYPES,
  KEYBOARD_KEYS,
  DRAWING_TOOLS,
  CONSOLE_MESSAGES
} from './constants'
import { getCanvasRelativePoint } from './utils'

/**
 * 事件处理器配置接口
 */
export interface EventHandlerConfig {
  canvas: Ref<HTMLCanvasElement | null>
  container: Ref<HTMLElement | null>
  currentTool: Ref<DrawTool>
  selection: {
    cutInfo: Ref<CutInfo | null>
    startDrag: (e: MouseEvent) => void
  }
  drawing: {
    startDrawing: (point: { x: number; y: number }) => void
    continueDrawing: (point: { x: number; y: number }) => void
    finishDrawing: () => void
    undo: () => void
  }
  onClose: () => void
}

/**
 * 创建鼠标按下事件处理器
 */
export function createMouseDownHandler(config: EventHandlerConfig) {
  return (e: MouseEvent) => {
    const target = e.target as HTMLElement

    // 如果点击的是调整点，不处理
    if (target.classList.contains(CSS_CLASSES.RESIZE_HANDLE)) {
      return
    }

    // 如果点击的是工具栏，不处理
    if (target.closest(`.${CSS_CLASSES.SCREENSHOT_TOOLBAR}`)) {
      return
    }

    if (!config.canvas.value) return

    const point = getCanvasRelativePoint(e, config.canvas.value)

    // 检查是否为绘制工具
    const isDrawingTool = DRAWING_TOOLS.includes(
      config.currentTool.value as any
    )

    if (isDrawingTool) {
      // 绘制工具 - 检查是否在选择框内
      if (config.selection.cutInfo.value) {
        const { startX, startY, width, height } = config.selection.cutInfo.value
        if (
          point.x >= startX &&
          point.x <= startX + width &&
          point.y >= startY &&
          point.y <= startY + height
        ) {
          // 在选择框内，可以进行绘制
          config.drawing.startDrawing(point)
          return true // 返回true表示开始绘制
        }
        // 在选择框外，不进行任何操作
      }
    } else {
      // 非绘制工具（select模式），检查是否在选择框内进行拖动
      if (config.selection.cutInfo.value) {
        const { startX, startY, width, height } = config.selection.cutInfo.value
        if (
          point.x >= startX &&
          point.x <= startX + width &&
          point.y >= startY &&
          point.y <= startY + height
        ) {
          // 在选择框内，开始拖动
          console.log(CONSOLE_MESSAGES.DRAG_START_IN_SELECTION)
          config.selection.startDrag(e)
        }
        // 在选择框外，不进行任何操作
      }
    }

    return false // 返回false表示没有开始绘制
  }
}

/**
 * 创建鼠标移动事件处理器
 */
export function createMouseMoveHandler(config: EventHandlerConfig) {
  let isDrawing = false

  return {
    handler: (e: MouseEvent) => {
      // 更新光标样式
      if (config.container.value) {
        updateCursor(e, config.container.value, config.selection.cutInfo)
      }

      if (!isDrawing || !config.canvas.value) return

      const point = getCanvasRelativePoint(e, config.canvas.value)
      config.drawing.continueDrawing(point)
    },
    setDrawing: (drawing: boolean) => {
      isDrawing = drawing
    }
  }
}

/**
 * 创建鼠标释放事件处理器
 */
export function createMouseUpHandler(config: EventHandlerConfig) {
  return (isDrawing: boolean, setDrawing: (drawing: boolean) => void) => {
    return () => {
      if (!isDrawing) return

      setDrawing(false)
      config.drawing.finishDrawing()
    }
  }
}

/**
 * 创建键盘事件处理器
 */
export function createKeyDownHandler(config: EventHandlerConfig) {
  return (e: KeyboardEvent) => {
    if (e.key === KEYBOARD_KEYS.ESCAPE) {
      config.onClose()
    } else if (e.ctrlKey && e.key === KEYBOARD_KEYS.CTRL_Z) {
      e.preventDefault()
      config.drawing.undo()
    }
  }
}

/**
 * 更新光标样式
 */
export function updateCursor(
  e: MouseEvent,
  container: HTMLElement,
  cutInfo: Ref<CutInfo | null>
) {
  const target = e.target as HTMLElement

  // 如果在工具栏内，保持默认光标
  if (target.closest(`.${CSS_CLASSES.SCREENSHOT_TOOLBAR}`)) {
    container.style.cursor = CURSOR_STYLES.DEFAULT
    return
  }

  // 如果在调整点上，使用调整点的光标
  if (target.classList.contains(CSS_CLASSES.RESIZE_HANDLE)) {
    return // 调整点自己会设置光标
  }

  // 如果在选择框内
  const selectionBox = container.querySelector(
    `.${CSS_CLASSES.SELECTION_BOX}`
  ) as HTMLElement

  if (selectionBox && cutInfo.value) {
    const rect = selectionBox.getBoundingClientRect()
    const containerRect = container.getBoundingClientRect()
    const x = e.clientX - containerRect.left
    const y = e.clientY - containerRect.top

    if (
      x >= rect.left - containerRect.left &&
      x <= rect.right - containerRect.left &&
      y >= rect.top - containerRect.top &&
      y <= rect.bottom - containerRect.top
    ) {
      // 在选择框内，显示移动光标
      container.style.cursor = CURSOR_STYLES.MOVE
      return
    }
  }

  // 在选择框外，显示禁用光标
  container.style.cursor = CURSOR_STYLES.NOT_ALLOWED
}

/**
 * 绑定Canvas事件
 */
export function bindCanvasEvents(
  canvas: HTMLCanvasElement,
  config: EventHandlerConfig
) {
  const mouseDownHandler = createMouseDownHandler(config)
  const mouseMoveHandler = createMouseMoveHandler(config)
  const keyDownHandler = createKeyDownHandler(config)

  let isDrawing = false

  const handleMouseDown = (e: MouseEvent) => {
    isDrawing = mouseDownHandler(e) || false
    mouseMoveHandler.setDrawing(isDrawing)
  }

  const handleMouseMove = mouseMoveHandler.handler

  const handleMouseUp = createMouseUpHandler(config)(
    isDrawing,
    (drawing: boolean) => {
      isDrawing = drawing
      mouseMoveHandler.setDrawing(drawing)
    }
  )

  // 绑定事件
  canvas.addEventListener(EVENT_TYPES.MOUSEDOWN, handleMouseDown)
  canvas.addEventListener(EVENT_TYPES.MOUSEMOVE, handleMouseMove)
  canvas.addEventListener(EVENT_TYPES.MOUSEUP, handleMouseUp)
  document.addEventListener(EVENT_TYPES.KEYDOWN, keyDownHandler)

  // 返回清理函数
  return () => {
    canvas.removeEventListener(EVENT_TYPES.MOUSEDOWN, handleMouseDown)
    canvas.removeEventListener(EVENT_TYPES.MOUSEMOVE, handleMouseMove)
    canvas.removeEventListener(EVENT_TYPES.MOUSEUP, handleMouseUp)
    document.removeEventListener(EVENT_TYPES.KEYDOWN, keyDownHandler)
  }
}

/**
 * 绑定容器点击事件（用于关闭）
 */
export function bindContainerClickEvent(
  container: HTMLElement,
  onClose: () => void,
  clickMaskToClose = false,
  onlyCloseByButton = true
) {
  if (clickMaskToClose && !onlyCloseByButton) {
    const handleClick = (e: Event) => {
      if (e.target === container) {
        onClose()
      }
    }

    container.addEventListener(EVENT_TYPES.CLICK, handleClick)

    return () => {
      container.removeEventListener(EVENT_TYPES.CLICK, handleClick)
    }
  }

  return () => {
    // 空的清理函数
    console.log('清理容器点击事件')
  }
}
