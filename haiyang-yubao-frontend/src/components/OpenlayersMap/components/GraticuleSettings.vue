<template>
  <div class="xqgj">
    
    <div class="query-item">
      <div class="query-title">网格颜色：</div>
      <div class="query-info">
        <div class="color-picker-wrapper">
          <input
            type="color"
            v-model="settings.gridColor"
            @change="handleSettingsChange"
            class="color-picker"
          />
          <span class="color-value">{{ settings.gridColor }}</span>
        </div>
      </div>
    </div>

    <div class="query-item">
      <div class="query-title">网格宽度：</div>
      <div class="query-info">
        <div class="number-input-wrapper">
          <input
            type="number"
            v-model.number="settings.gridWidth"
            @input="handleSettingsChange"
            min="1"
            max="10"
            step="1"
            class="number-input"
          />
          <span class="unit">px</span>
        </div>
      </div>
    </div>

    <div class="query-item">
      <div class="query-title">经纬度间隔：</div>
      <div class="query-info">
        <div class="number-input-wrapper">
          <input
            type="number"
            v-model.number="settings.spacing"
            @input="handleSettingsChange"
            min="1"
            max="30"
            step="1"
            class="number-input"
          />
          <span class="unit">度</span>
        </div>
      </div>
    </div>

    <div class="query-item">
      <div class="query-title">网格线类型：</div>
      <div class="query-info">
        <div class="radio-group">
          <label class="radio-item">
            <input
              type="radio"
              v-model="settings.lineType"
              @change="handleSettingsChange"
              value="dashed"
              class="radio-input"
            />
            <span class="radio-label">虚线</span>
          </label>
          <label class="radio-item">
            <input
              type="radio"
              v-model="settings.lineType"
              @change="handleSettingsChange"
              value="solid"
              class="radio-input"
            />
            <span class="radio-label">实线</span>
          </label>
        </div>
      </div>
    </div>

    <div class="query-item">
      <div class="query-title">显示网格：</div>
      <div class="query-info">
        <label class="checkbox-label">
          <input type="checkbox" v-model="settings.showGrid" @change="handleSettingsChange" />
          <span class="checkmark"></span>
        </label>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'

const emit = defineEmits<{
  'update:settings': [settings: any]
}>()


const settings = reactive({
  showGrid: true, 
  gridColor: '#2581d0', 
  gridWidth: 1, 
  spacing: 5, 
  lineType: 'dashed' 
})

function handleSettingsChange() {
  emit('update:settings', { ...settings })
}
</script>

<style scoped>
.xqgj {
  background: #fafcfe;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #e7f0fb;
  margin: 5px 0px;
}

.query-item {
  display: flex;
  align-items: center;
  padding: 7px 16px;

  .query-title {
    white-space: nowrap;
    width: 80px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #222222;
    line-height: 16px;
  }

  .query-info {
    display: flex;
    align-items: center;
    height: 28px;
  }
}

.color-picker-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-picker {
  width: 32px;
  height: 32px;
  border: 1px solid #e7f0fb;
  border-radius: 4px;
  cursor: pointer;
  padding: 0;
  background: #fff;
}

.color-picker:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.color-picker:hover {
  border-color: #1c81f8;
}

.color-value {
  font-size: 12px;
  color: #666666;
  font-family: Microsoft YaHei, Microsoft YaHei;
}
.checkbox-label {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  opacity: 0;
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 20px;
  width: 20px;
  background-color: #fff;
  border: 2px solid #e7f0fb;
  border-radius: 3px;
  transition: all 0.2s ease;
}

.checkbox-label:hover .checkmark {
  border-color: #1c81f8;
}

.checkbox-label input[type="checkbox"]:checked ~ .checkmark {
  background-color: #1c81f8;
  border-color: #1c81f8;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.checkbox-label input[type="checkbox"]:checked ~ .checkmark:after {
  display: block;
}

.checkbox-label .checkmark:after {
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-label input[type="checkbox"]:disabled ~ .checkmark {
  background-color: #f5f5f5;
  border-color: #ddd;
  cursor: not-allowed;
}

.checkbox-label input[type="checkbox"]:disabled {
  cursor: not-allowed;
}

.number-input-wrapper {
  display: flex;
  align-items: center;
  gap: 4px;
}

.number-input {
  width: 200px;
  height: 32px;
  border: 1px solid #e7f0fb;
  border-radius: 4px;
  padding: 0 8px;
  font-size: 14px;
  text-align: center;
  background: #fff;
  font-family: Microsoft YaHei, Microsoft YaHei;
}

.number-input:disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.number-input:focus {
  outline: none;
  border-color: #1c81f8;
}

.number-input:hover {
  border-color: #1c81f8;
}

.unit {
  font-size: 14px;
  color: #666666;
  min-width: 20px;
  font-family: Microsoft YaHei, Microsoft YaHei;
}

.radio-group {
  display: flex;
  gap: 20px;
}

.radio-item {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.radio-input {
  margin-right: 6px;
  width: 16px;
  height: 16px;
}

.radio-input:disabled {
  cursor: not-allowed;
}

.radio-input:disabled + .radio-label {
  color: #999;
  cursor: not-allowed;
}

.radio-label {
  font-size: 14px;
  color: #222222;
  font-family: Microsoft YaHei, Microsoft YaHei;
  font-weight: 400;
}

.button-group {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-cancel {
  background: #fff;
  color: #666;
}

.btn-cancel:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

.btn-confirm {
  background: #2581d0;
  color: #fff;
  border-color: #2581d0;
}

.btn-confirm:hover {
  background: #1a6bb8;
  border-color: #1a6bb8;
}
</style>