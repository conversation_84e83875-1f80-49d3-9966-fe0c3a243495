{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "chrome",
      "request": "launch",
      "name": "Start PC FE",
      "url": "https://localhost:8081",
      "webRoot": "${workspaceFolder}/haiyang-yubao-frontend",
      "pathMapping": {
        "/@fs": "${workspaceFolder}/haiyang-yubao-frontend"
      },
      "sourceMaps": true,
      "sourceMapPathOverrides": {
        "meteor://💻app/*": "${workspaceFolder}/*",
        "webpack:///./~/*": "${workspaceFolder}/node_modules/*",
        "webpack://?:*/*": "${workspaceFolder}/*"
      },
      "preLaunchTask": "Before Start PC FE",
      //   "preLaunchTask": "Start PC FE",
      "skipFiles": ["node_modules/**"]
    }
  ]
}
