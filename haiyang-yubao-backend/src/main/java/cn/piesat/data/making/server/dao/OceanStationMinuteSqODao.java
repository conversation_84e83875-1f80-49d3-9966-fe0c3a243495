package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.OceanStationMinuteSqO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站分钟数据-每分钟实时资料-原始数据表数据库访问层
 *
 * <AUTHOR>
 */
public interface OceanStationMinuteSqODao extends BaseMapper<OceanStationMinuteSqO> {

    @Select({"<script>",
            "select * from ocean_station_minute_sq_o where monitoringdate between #{startTime} and #{endTime} " +
                    "and station_num in " +
                    "<foreach collection='stationNumList' item='stationNum' open='(' separator=',' close=')'> " +
                    "#{stationNum}" +
                    "</foreach>",
            "</script>"})
    List<OceanStationMinuteSqO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList);

    @Select("SELECT MAX(createtime) FROM ocean_station_minute_sq_o")
    LocalDateTime getMaxCreateTime();
}
