package cn.piesat.data.making.server.controller;


import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.entity.AlarmLevel;
import cn.piesat.data.making.server.service.AlarmLevelService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.List;

/**
 * 警报等级信息
 *
 * <AUTHOR>
 * @since 2024-09-18 14:39:15
 */
@RestController
@RequestMapping("/alarmLevel")
public class AlarmLevelController {
    /**
     * 服务对象
     */
    @Autowired
    private AlarmLevelService AlarmLevelService;

    /**
     * 查询所有数据
     *
     * @return 所有数据
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "警报等级管理", operateType = OperateType.SELECT)
    public List<AlarmLevel> pageList() {
        return this.AlarmLevelService.list();
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "警报等级管理", operateType = OperateType.SELECT)
    public AlarmLevel selectOne(@PathVariable Long id) {
        return this.AlarmLevelService.getById(id);
    }

    /**
     * 新增数据
     *
     * @param AlarmLevel 实体对象
     * @return 新增结果
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "警报等级管理", operateType = OperateType.INSERT)
    public AlarmLevel insert(@RequestBody AlarmLevel AlarmLevel) {
        this.AlarmLevelService.save(AlarmLevel);
        return AlarmLevel;
    }

    /**
     * 修改数据
     *
     * @param AlarmLevel 实体对象
     * @return 修改结果
     */
    @PostMapping("/update")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "警报等级管理", operateType = OperateType.UPDATE)
    public void update(@RequestBody AlarmLevel AlarmLevel) {
        this.AlarmLevelService.updateById(AlarmLevel);
    }

}

