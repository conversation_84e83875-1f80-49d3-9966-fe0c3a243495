package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.UserDTO;
import cn.piesat.data.making.server.service.UserService;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.UserVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
public class UserController {

    @Resource
    private UserService userService;

    /**
     * 查询用户表分页
     *
     * @param tagId 标签id
     * @param pageNum  页数
     * @param pageSize 条数
     * @return
     */
    @GetMapping("/page")
    @SysLog(systemName = "预报制作系统", moduleName = "用户表管理", operateType = OperateType.SELECT)
    public PageResult<UserVO> getPage(@RequestParam(required = false) Long tagId,
                                      @RequestParam(defaultValue = "1") Integer pageNum,
                                      @RequestParam(defaultValue = "10") Integer pageSize) {
        UserDTO dto = new UserDTO();
        dto.setTagId(tagId);
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);
        return userService.getPage(dto, pageParam);
    }

    /**
     * 查询用户表列表
     *
     * @param tagId 标签id
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报制作系统", moduleName = "用户表管理", operateType = OperateType.SELECT)
    public List<UserVO> getList(@RequestParam(required = false) Long tagId) {
        UserDTO dto = new UserDTO();
        dto.setTagId(tagId);
        return userService.getList(dto);
    }

    /**
     * 根据用户表id查询详情
     *
     * @param id
     * @return
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报制作系统", moduleName = "用户表管理", operateType = OperateType.SELECT)
    public UserVO getById(@PathVariable("id") Long id) {
        return userService.getInfoById(id);
    }

    /**
     * 保存用户表
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报制作系统", moduleName = "用户表管理", operateType = OperateType.INSERT)
    public void save(@Validated(value = {UserDTO.Save.class}) @RequestBody UserDTO dto) {
        userService.save(dto);
    }

    /**
     * 根据用户表id删除用户表
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @SysLog(systemName = "预报制作系统", moduleName = "用户表管理", operateType = OperateType.DELETE)
    public void deleteById(@PathVariable("id") Long id) {
        userService.deleteById(id);
    }
}

