package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.AreaDTO;
import cn.piesat.data.making.server.service.AreaService;
import cn.piesat.data.making.server.vo.AreaVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 区域表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/area")
public class AreaController {

    @Resource
    private AreaService areaService;

    /**
     * 根据区域类型编码查询区域列表
     *
     * @param areaTypeCode 区域类型编码
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "区域管理", operateType = OperateType.SELECT)
    public List<AreaVO> getList(@RequestParam(required = false) String areaTypeCode) {
        AreaDTO dto = new AreaDTO();
        dto.setAreaTypeCode(areaTypeCode);
        return areaService.getList(dto);
    }

    /**
     * 保存区域
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "区域管理", operateType = OperateType.INSERT)
    public Long save(@Validated(value = {AreaDTO.Save.class}) @RequestBody AreaDTO dto) {
        return areaService.save(dto);
    }

    /**
     * 根据区域id删除区域
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "区域管理", operateType = OperateType.DELETE)
    public void deleteById(@PathVariable("id") Long id) {
        areaService.deleteById(id);
    }


    /**
     * 生成areaJsonFile
     */
    @PostMapping("/generateAreaJson")
    public void  generateAreaJson(@RequestBody List<Long> areaIds){
        areaService.generateAreaJson(areaIds);
    }
}

