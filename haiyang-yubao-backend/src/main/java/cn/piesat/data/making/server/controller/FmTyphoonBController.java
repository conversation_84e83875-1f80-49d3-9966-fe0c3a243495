package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.dto.FmTyphoonBDTO;
import cn.piesat.data.making.server.vo.FmTyphoonBVO;
import cn.piesat.data.making.server.service.FmTyphoonBService;
import cn.piesat.data.making.server.vo.FmTyphoonScoreVO;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;
import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.common.page.constant.PageConstant;
import cn.piesat.common.page.annotation.JpaPage;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * 台风信息控制层
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:21
 */
@RestController
@RequestMapping("fmTyphoonB")
public class FmTyphoonBController {

    @Resource
    private FmTyphoonBService fmTyphoonBService;

    /**
     * 查询分页
     *
     * @param id
     * @param pageNum  页数
     * @param pageSize 条数
     * @return
     */
    @GetMapping("/page")
    @JpaPage(PageConstant.PAGE_VARIABLE_NAME)
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风信息管理", operateType = OperateType.SELECT)
    public PageResult<FmTyphoonBVO> getPage(@RequestParam(required = false) Long id,
                                            @RequestParam(defaultValue = "1") Integer pageNum,
                                            @RequestParam(defaultValue = "10") Integer pageSize) {
        FmTyphoonBDTO dto = new FmTyphoonBDTO();
        dto.setId(id);
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);
        return fmTyphoonBService.getPage(dto, pageParam);
    }

    /**
     * 查询列表
     *
     * @param id
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风信息管理", operateType = OperateType.SELECT)
    public List<FmTyphoonBVO> getList(@RequestParam(required = false) Long id) {
        FmTyphoonBDTO dto = new FmTyphoonBDTO();
        dto.setId(id);
        return fmTyphoonBService.getList(dto);
    }

    /**
     * 根据id查询数据
     *
     * @param id
     * @return
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风信息管理", operateType = OperateType.SELECT)
    public FmTyphoonBVO getById(@PathVariable Long id) {
        return fmTyphoonBService.getById(id);
    }

    /**
     * 保存数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风信息管理", operateType = OperateType.INSERT)
    public void save(@Validated(value = {FmTyphoonBDTO.Save.class}) @RequestBody FmTyphoonBDTO dto) {
        fmTyphoonBService.save(dto);
    }

    /**
     * 根据id删除数据
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风信息管理", operateType = OperateType.DELETE)
    public void deleteById(@PathVariable("id") Long id) {
        fmTyphoonBService.deleteById(id);
    }

    /**
     * 根据idList批量删除数据
     *
     * @param idList
     * @return
     */
    @DeleteMapping("/delete")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风信息管理", operateType = OperateType.DELETE)
    public void deleteByIdList(@RequestBody List<Long> idList) {
        fmTyphoonBService.deleteByIdList(idList);
    }

    /**
     * 查询相似路径台风
     * @param code 台风编码
     * @param startYear 查询起始年份
     * @param distance 匹配距离
     * @param points 最小匹配点数
     * @param score 分值
     */
    @GetMapping("/similar")
    public List<FmTyphoonScoreVO> getSimilarTyphoon(@RequestParam("code") String code,
                                                    @RequestParam(required=false,defaultValue = "2008") int startYear,
                                                    @RequestParam(required=false,value = "distance",defaultValue = "500") int distance,
                                                    @RequestParam(required=false,defaultValue = "7") int points,
                                                    @RequestParam(required=false,defaultValue = "50") int score) throws Exception {

        return fmTyphoonBService.getSimilarTyphoonList(code,startYear,points,distance,score);

    }

    @GetMapping("/syncTyphoon/{startYear}/{endYear}")
    public String syncTyphoon(@PathVariable Integer startYear, @PathVariable Integer endYear){
        return fmTyphoonBService.syncTyphoon(startYear,endYear);
    }

    @Scheduled(cron = "${schedule.cron.syncOceanAndSmallBuoyData}")
    public void syncTyphoonCron(){
        LocalDate now = LocalDate.now();
        LocalDate localDate = now.plusYears(1);
        fmTyphoonBService.syncTyphoon(now.getYear(),localDate.getYear());
    }
}
