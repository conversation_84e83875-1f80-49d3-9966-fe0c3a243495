package cn.piesat.data.making.server.controller;


import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.StormSurgeAlarmDTO;
import cn.piesat.data.making.server.entity.StormSurgeAlarm;
import cn.piesat.data.making.server.processor.StormSurgeGenerateText;
import cn.piesat.data.making.server.service.FmPublishAlarmService;
import cn.piesat.data.making.server.service.StormSurgeAlarmService;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.GenerateTextVO;
import cn.piesat.webconfig.validation.ValidatedGroup;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 风暴潮警报制作
 *
 * <AUTHOR>
 * @since 2024-10-22 15:27:33
 */
@RestController
@RequestMapping("/stormSurgeAlarm")
public class StormSurgeAlarmController {
    /**
     * 服务对象
     */
    @Autowired
    private StormSurgeAlarmService stormSurgeAlarmBService;

    @Autowired
    private FmPublishAlarmService fmPublishAlarmService;

    /**
     * 分页查询所有数据
     *
     * @param pageNum  页码
     * @param pageSize 条数
     * @return 所有数据
     */
    @GetMapping("/pageList")
    public PageResult<StormSurgeAlarm> pageList(@RequestParam Integer pageNum, @RequestParam Integer pageSize,
                                                @RequestParam(value = "startTime", required = false) Date startTime,
                                                @RequestParam(value = "endTime", required = false) Date endTime) {
        return this.stormSurgeAlarmBService.pageList(pageNum, pageSize, startTime, endTime);
    }

    /**
     * 查询所有数据
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "风暴潮警报制作管理", operateType = OperateType.SELECT)
    public List<StormSurgeAlarm> list(){
        return this.stormSurgeAlarmBService.list(new LambdaQueryWrapper<StormSurgeAlarm>().orderByAsc(StormSurgeAlarm::getCreateTime));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/info/{id}")
    public StormSurgeAlarm selectOne(@PathVariable Long id) {
        return this.stormSurgeAlarmBService.getById(id);
    }

    /**
     * 新增数据
     *
     * @param stormSurgeAlarmDTO 实体对象
     * @return 新增结果
     */
    @PostMapping("/save")
    public StormSurgeAlarm insert(@RequestBody @Validated(ValidatedGroup.Save.class) StormSurgeAlarmDTO stormSurgeAlarmDTO) {
        return this.stormSurgeAlarmBService.saveInfo(stormSurgeAlarmDTO);
    }

    /**
     * 修改数据
     *
     * @param stormSurgeAlarmDTO 实体对象
     * @return 修改结果
     */
    @PostMapping("/update")
    public void update(@RequestBody @Validated(ValidatedGroup.Update.class) StormSurgeAlarmDTO stormSurgeAlarmDTO) {
        this.stormSurgeAlarmBService.updateInfo(stormSurgeAlarmDTO);
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @PostMapping("/delete")
    public void delete(@RequestBody List<Long> idList) {
        this.stormSurgeAlarmBService.removeByIds(idList);
    }


    /**
     * 生成文字
     * @param stormSurgeGenerateText 参数
     * @return
     */
    @PostMapping("/generateText")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "风暴潮警报制作管理", operateType = OperateType.UPDATE)
    public GenerateTextVO generateText(@RequestBody StormSurgeGenerateText stormSurgeGenerateText){
        return stormSurgeAlarmBService.generateText(stormSurgeGenerateText);
    }

    /**
     * 发布
     * @param id 参数
     */
    @PostMapping("/release/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "风暴潮警报制作管理", operateType = OperateType.INSERT)
    public void release(@PathVariable Long id){
        stormSurgeAlarmBService.release(id);
    }

    /**
     * 统计
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    @PostMapping("/statistic")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "风暴潮警报制作管理", operateType = OperateType.SELECT)
    public Map<Long, Long> statistic(@RequestParam(value = "startTime", required = false) Date startTime,
                                     @RequestParam(value = "endTime", required = false) Date endTime) {
        return this.stormSurgeAlarmBService.statistic(startTime, endTime);
    }

    /**
     * 通过主键查询单条数据,并同步到推送表
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/pushSelectOne/{id}")
    public StormSurgeAlarm pushSelectOne(@PathVariable Long id) {
        StormSurgeAlarm stormSurgeAlarm = this.stormSurgeAlarmBService.getById(id);
        fmPublishAlarmService.publishAlarm(stormSurgeAlarm);
        return stormSurgeAlarm;
    }

    @GetMapping("/downloadDoc/{id}.docx")
    //@SysLog(systemName = "预报警报制作发布系统", moduleName = "风暴潮警报制作管理", operateType = OperateType.SELECT)
    public void downloadDoc(HttpServletResponse response, @PathVariable("id") Long productRecordId){
        stormSurgeAlarmBService.downloadDoc(response,productRecordId);
    }
}

