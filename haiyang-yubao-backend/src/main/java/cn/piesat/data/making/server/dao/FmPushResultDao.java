package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.FmPushResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface FmPushResultDao extends BaseMapper<FmPushResult> {

    @Select("select a.*,c.id as biz_id,c.biz_name,c.biz_code,c.push_channel from fm_push_user_b a join(select user_id,biz_id from fm_push_user_biz_rel_b where biz_id in(SELECT id FROM fm_push_biz_b where biz_code=#{bizCode}))b on a.id=b.user_id join fm_push_biz_b c on b.biz_id=c.id")
    List<Map<String,Object>> queryListMapByParam(String bizCode);
}
