package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.entity.TideDailyHourData;
import cn.piesat.data.making.server.service.impl.TideDailyServiceImpl;
import cn.piesat.data.making.server.vo.TideDailyWarnLevelVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 潮汐表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/tide")
public class TideController {

    @Autowired
    private TideDailyServiceImpl tideDailyService;

    /**
     * 站点潮位数据
     *
     * @param stationIds 站点列表
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param datum      默认 datum
     * @return
     */
    @RequestMapping("/daily_data")
    public List<TideDailyWarnLevelVO> getTideDailyDataList(@RequestParam("ids") List<Long> stationIds,
                                                           @RequestParam(value = "startTime", required = false) Date startTime,
                                                           @RequestParam(value = "endTime", required = false) Date endTime,
                                                           @RequestParam(value = "datum", defaultValue = "datum") String datum) {
        return tideDailyService.getTideDailyListByStation(stationIds, startTime, endTime, datum);
    }

    /**
     * 根据站点id查询潮汐数据列表
     *
     * @param stationId 站点id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param datum     基面
     * @return
     **/
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "潮汐管理", operateType = OperateType.SELECT)
    public List<TideDailyHourData> getList(@RequestParam(value = "stationId") Long stationId,
                                           @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                           @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                           @RequestParam(value = "datum") String datum) {
        return tideDailyService.getList(stationId, startTime, endTime, datum);
    }

    /**
     * 导出天文潮数据
     *
     * @param stationIds 站点ids
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param datum      基面
     * @return
     **/
    @GetMapping("/export")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "潮汐管理", operateType = OperateType.SELECT)
    public void exportList(HttpServletResponse response, @RequestParam(value = "stationIds") String stationIds,
                           @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                           @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                           @RequestParam(value = "datum") String datum) {
        tideDailyService.exportList(response, stationIds, startTime, endTime, datum);
    }
}
