package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.utils.GeoToolsUtil;
import org.apache.commons.lang3.StringUtils;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * GEO工具接口
 *
 * <AUTHOR>
 * @date 2025/03/25
 */
@RestController
@RequestMapping("/geo")
public class GeoUtilController {

    // 缓存全球国家边界Feature对象
    private SimpleFeatureCollection globalNationalBoundaryFeatureCollection;

    public GeoUtilController(@Value("${piesat.make.globalNationalBoundaryFilePath}") String globalNationalBoundaryFilePath) throws Exception {
        if (StringUtils.isNoneBlank(globalNationalBoundaryFilePath) && new File(globalNationalBoundaryFilePath).exists()) {
            globalNationalBoundaryFeatureCollection = GeoToolsUtil.getFeatureCollection(new File(globalNationalBoundaryFilePath));
        }
    }

    /**
     * 获取全球国家边界交点
     *
     * @param geoJsonString geo json字符串
     * @param attribute     属性
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * @throws Exception 例外
     */
    @PostMapping("/globalNationalBoundary/intersection")
    public List<Map<String, Object>> getGlobalNationalBoundaryIntersection(@RequestParam("geoJsonString") String geoJsonString,
                                                                           @RequestParam(value = "attribute", required = false, defaultValue = "value") String attribute) throws Exception {
        // 将转义的geoJsonString转为正确的json格式
        geoJsonString = geoJsonString.replace("\\\"", "\"")
                .replaceAll("^\"|\"$", "")
                .replace("\\", "");
        geoJsonString = StringUtils.strip(geoJsonString, "\"");

        // 调用工具类计算传入的geoJson与全球国家边界的交点
        SimpleFeatureCollection featureCollection = GeoToolsUtil.getFeatureCollection(geoJsonString);
        return GeoToolsUtil.getIntersection(featureCollection, globalNationalBoundaryFeatureCollection, attribute);
    }

}
