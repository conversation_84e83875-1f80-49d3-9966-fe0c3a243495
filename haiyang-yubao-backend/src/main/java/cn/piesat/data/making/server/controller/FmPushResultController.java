package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.service.FmPushResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("fmPushResult")
public class FmPushResultController {

    @Autowired
    private FmPushResultService fmPushResultServiceImpl;

    @GetMapping("/testMail")
    @Scheduled(cron = "${schedule.cron.syncPublishDate}")
    public void testMail(){
        fmPushResultServiceImpl.syncPushData();
    }
}
