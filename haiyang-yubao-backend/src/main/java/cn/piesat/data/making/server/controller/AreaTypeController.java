package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.service.AreaTypeService;
import cn.piesat.data.making.server.vo.AreaTypeVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 区域类型表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/areaType")
public class AreaTypeController {

    @Resource
    private AreaTypeService areaTypeService;

    /**
     * 查询区域类型-区域树形列表
     *
     * @param name 名称
     * @return
     */
    @GetMapping("/treeList")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "区域类型管理", operateType = OperateType.SELECT)
    public List<AreaTypeVO> getTreeList(@RequestParam(required = false) String name,
                                        @RequestParam(required = false) List<String> codes) {
        return areaTypeService.getTreeList(name,codes);
    }
}

