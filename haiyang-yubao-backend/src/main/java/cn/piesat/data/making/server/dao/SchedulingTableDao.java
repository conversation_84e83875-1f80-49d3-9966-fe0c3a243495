package cn.piesat.data.making.server.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.transaction.annotation.Transactional;
import cn.piesat.data.making.server.entity.SchedulingTable;

import java.util.Date;
import java.util.List;

/**
 * 数据库访问层
 *
 * <AUTHOR>
 * @date 2024-09-04 17:11:58
 */
public interface SchedulingTableDao extends BaseMapper<SchedulingTable> {

    @Transactional
    @Select("delete from scheduling_table WHERE id in #{idList}")
    void deleteByIdList(List<Long> idList);

    @Select(value = "select * from scheduling_table s WHERE DATE(s.scheduling_date) = #{date}")
    SchedulingTable finBySchedulingDate(Date date);

    @Transactional
    @Select("delete from scheduling_table WHERE scheduling_main_id = #{id}")
    void deleteBySchedulingMainId(Long id);
}
