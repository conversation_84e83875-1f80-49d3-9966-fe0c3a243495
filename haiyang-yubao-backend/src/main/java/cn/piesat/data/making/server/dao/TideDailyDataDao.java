package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.TideDailyData;
import cn.piesat.data.making.server.vo.TideDailyWarnLevelVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

public interface TideDailyDataDao extends BaseMapper<TideDailyData> {
    List<TideDailyWarnLevelVO> selectWarnLevel(@Param("stationIds") List<Long> stationIds, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("datum") String datum);

    @Select({"<script>",
            "WITH RankedHeights AS ( " +
                    "SELECT station_name, height, tide_time, ROW_NUMBER() OVER (PARTITION BY station_name ORDER BY height DESC, tide_time ASC) AS rn " +
                    "FROM " +
                    "fm_tide_daily " +
                    "WHERE 1=1 and datum='tide_datum' " +
                    "<if test='stationId != null'>" +
                    "and station_id = #{stationId} " +
                    "</if>" +
                    "<if test='startTime != null'>" +
                    "and tide_time &gt;= #{startTime} " +
                    "</if>" +
                    "<if test='endTime != null'>" +
                    "and tide_time &lt;= #{endTime} " +
                    "</if>" +
                    ") " +
                    "SELECT station_name as stationName, height, tide_time as tideTime FROM RankedHeights WHERE rn = 1;",
            "</script>"})
    List<TideDailyData> getMaxTideInfoList(Long stationId, Date startTime, Date endTime);

    @Select({"<script>",
            "WITH RankedHeights AS ( " +
                    "SELECT station_name, height, tide_time, ROW_NUMBER() OVER (PARTITION BY station_name ORDER BY height ASC, tide_time ASC) AS rn " +
                    "FROM " +
                    "fm_tide_daily " +
                    "WHERE 1=1 and datum='tide_datum' " +
                    "<if test='stationId != null'>" +
                    "and station_id = #{stationId} " +
                    "</if>" +
                    "<if test='startTime != null'>" +
                    "and tide_time &gt;= #{startTime} " +
                    "</if>" +
                    "<if test='endTime != null'>" +
                    "and tide_time &lt;= #{endTime} " +
                    "</if>" +
                    ") " +
                    "SELECT station_name as stationName, height, tide_time as tideTime FROM RankedHeights WHERE rn = 1;",
            "</script>"})
    List<TideDailyData> getMinTideInfoList(Long stationId, Date startTime, Date endTime);
}
