package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.OceanStationHourHuO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 海洋站整点-相对湿度-原始数据表数据库访问层
 *
 * <AUTHOR>
 */
public interface OceanStationHourHuODao extends BaseMapper<OceanStationHourHuO> {

    @Select({"<script>",
            "select * from ocean_station_hour_hu_o where monitoringdate between #{startTime} and #{endTime} " +
                    "and station_num in " +
                    "<foreach collection='stationNumList' item='stationNum' open='(' separator=',' close=')'> " +
                    "#{stationNum}" +
                    "</foreach>",
            "</script>"})
    List<OceanStationHourHuO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList);

    @Select("SELECT MAX(createtime) FROM ocean_station_hour_hu_o")
    LocalDateTime getMaxCreateTime();
}
