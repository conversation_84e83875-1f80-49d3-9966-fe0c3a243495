package cn.piesat.data.making.server.constant;

public class CommonConstant {
    public static final String ALARM_PRO_BASE_PATH = "%s/DATA/PRO/ALERT";

    public static final String UPLOAD_IMAGE_PATH = "%s/image/%s/%s/%s.png";

    public static final String GEOJSON_FILE_PATH = "%s/generate/geojson/area-%s.json";

    public static final String FORECAST_FILE_PATH = "%s/generate/forecast/forecast_time_%s.json";

    public static final String ALERT_WAVE_WORD_FILE_PATH = ALARM_PRO_BASE_PATH+"/O/AHSW/%s/%s/";

    public static final String ALERT_WAVE_WORD_FILE_NAME = ALERT_WAVE_WORD_FILE_PATH+"O_AHSW_L4_STP_%s_V1_WAVE_%s.docx";

    public static final String ALERT_WAVE_HTML_FILE_PATH = ALARM_PRO_BASE_PATH+"/O/AHSW/%s/%s/";

    public static final String ALERT_WAVE_HTML_FILE_NAME = ALERT_WAVE_HTML_FILE_PATH+"O_AHSW_L4_STP_%s_V1_WAVE_%s.html";

    public static final String ALERT_WAVE_TXT_FILE_PATH = ALARM_PRO_BASE_PATH+"/O/AHST/%s/%s/";

    public static final String ALERT_WAVE_TXT_FILE_NAME = ALERT_WAVE_TXT_FILE_PATH+"O_AHST_L4_STP_%s_V1_WAVE_%s.txt";

    public static final String ALERT_SURGES_WORD_FILE_PATH = ALARM_PRO_BASE_PATH+"/O/ASSW/%s/%s/";

    public static final String ALERT_SURGES_WORD_FILE_NAME = ALERT_SURGES_WORD_FILE_PATH+"O_ASSW_L4_STP_%s_V1_SURGES_%s.docx";

    public static final String ALERT_SURGES_HTML_FILE_PATH = ALARM_PRO_BASE_PATH+"/O/ASSW/%s/%s/";

    public static final String ALERT_SURGES_HTML_FILE_NAME = ALERT_SURGES_HTML_FILE_PATH+"O_ASSW_L4_STP_%s_V1_SURGES_%s.html";

    public static final String ALERT_SURGES_TXT_FILE_PATH = ALARM_PRO_BASE_PATH+"/O/ASST/%s/%s/";

    public static final String ALERT_SURGES_TXT_FILE_NAME = ALERT_SURGES_TXT_FILE_PATH+"O_ASST_L4_STP_%s_V1_SURGES_%s.txt";

    public static final String MESSAGE_WAVE_WORD_FILE_PATH = ALARM_PRO_BASE_PATH+"/O/MHSW/%s/%s/";

    public static final String MESSAGE_WAVE_WORD_FILE_NAME = MESSAGE_WAVE_WORD_FILE_PATH+"O_MHSW_L4_STP_%s_V1_WAVE_%s.docx";

    public static final String MESSAGE_WAVE_HTML_FILE_PATH = ALARM_PRO_BASE_PATH+"/O/MHSW/%s/%s/";

    public static final String MESSAGE_WAVE_HTML_FILE_NAME = MESSAGE_WAVE_HTML_FILE_PATH+"O_MHSW_L4_STP_%s_V1_WAVE_%s.html";

    public static final String MESSAGE_WAVE_TXT_FILE_PATH = ALARM_PRO_BASE_PATH+"/O/MHST/%s/%s/";

    public static final String MESSAGE_WAVE_TXT_FILE_NAME = MESSAGE_WAVE_TXT_FILE_PATH+"O_MHST_L4_STP_%s_V1_WAVE_%s.txt";

    public static final String MESSAGE_SURGES_WORD_FILE_PATH = ALARM_PRO_BASE_PATH+"/O/MSSW/%s/%s/";

    public static final String MESSAGE_SURGES_WORD_FILE_NAME = MESSAGE_SURGES_WORD_FILE_PATH+"O_MSSW_L4_STP_%s_V1_SURGES_%s.docx";

    public static final String MESSAGE_SURGES_HTML_FILE_PATH = ALARM_PRO_BASE_PATH+"/O/MSSW/%s/%s/";

    public static final String MESSAGE_SURGES_HTML_FILE_NAME = MESSAGE_SURGES_HTML_FILE_PATH+"O_MSSW_L4_STP_%s_V1_SURGES_%s.html";

    public static final String MESSAGE_SURGES_TXT_FILE_PATH = ALARM_PRO_BASE_PATH+"/O/MSST/%s/%s/";

    public static final String MESSAGE_SURGES_TXT_FILE_NAME = MESSAGE_SURGES_TXT_FILE_PATH+"O_MSST_L4_STP_%s_V1_SURGES_%s.txt";

    public static final String FORECAST_SPECIAL_WORD_FILE_PATH = "%s/DATA/PRO/FORECAST/F/FSSW/%s/%s/";

    public static final String FORECAST_SPECIAL_WORD_FILE_NAME = FORECAST_SPECIAL_WORD_FILE_PATH+"O_AHSW_L4_STP_%s_V1_WAVE_%s.docx";

    public static final String MESSAGE_PUBLIC_WORD_FILE_PATH = "%s/DATA/PRO/PUBLIC/O/%s/%s/%s/";

    public static final String MESSAGE_PUBLIC_WORD_FILE_NAME = MESSAGE_PUBLIC_WORD_FILE_PATH + "O_%s_L4_STP_%s_V1_%s.docx";

    public static final String FORECAST_TEMPLATE_PATH = "%s/forecastTemplate/%s/%s";
    public static final String FORECAST_PRODUCT_WORD_NAME = "%s/DATA/PRO/FORECAST/F/FDRW/%s/%s/F_FDRW_S4_STP_%s_V1_FORECAST_%s.docx";
    public static final String FORECAST_PRODUCT_TXT_NAME = "%s/DATA/PRO/FORECAST/F/FDRW/%s/%s/F_FDRW_S4_STP_%s_V1_FORECAST_%s.txt";
    public static final String FORECAST_PRODUCT_XML_NAME = "%s/DATA/PRO/FORECAST/F/FDRW/%s/%s/F_FDRW_S4_STP_%s_V1_FORECAST_%s.xml";

    public static final String FORECAST_PRODUCT_TEMP_WORD_NAME = "%s/DATA/PRO/FORECAST/F/FDRW/%s/%s/temp_F_FDRW_S4_STP_%s_V1_FORECAST_%s.docx";
}
