package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.OceanSmallShallowseauoyO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 浅海小型浮标数据-原始数据（文件编码标识BL）表数据库访问层
 *
 * <AUTHOR>
 */
public interface OceanSmallShallowseauoyODao extends BaseMapper<OceanSmallShallowseauoyO> {

    @Select({"<script>",
            "select * from ocean_small_shallowsea_buoy_o where monitoringtime between #{startTime} and #{endTime} " +
                    "and buoyinfo_id in " +
                    "<foreach collection='stationNumList' item='stationNum' open='(' separator=',' close=')'> " +
                    "#{stationNum}" +
                    "</foreach>",
            "</script>"})
    List<OceanSmallShallowseauoyO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList);

    @Select("SELECT MAX(createtime) FROM ocean_small_shallowsea_buoy_o")
    LocalDateTime getMaxCreateTime();
}
