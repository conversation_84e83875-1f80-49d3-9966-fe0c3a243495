package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.model.StationInfo;
import cn.piesat.data.making.server.service.StationTypeService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 站点类型表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/stationType")
public class StationTypeController {

    @Resource
    private StationTypeService stationTypeService;

    /**
     * 查询站点类型-行政区-站点树形列表
     *
     * @param name            名称
     * @param flag            标记叶子节点类型：region station
     * @param code            编码
     * @return
     */
    @GetMapping("/treeList")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "站点类型管理", operateType = OperateType.SELECT)
    public List<StationInfo> getTreeList(@RequestParam(required = false) String name,
                                         @RequestParam String flag,
                                         @RequestParam(required = false) String code) {
        return stationTypeService.getTreeList(name, flag, code);
    }
}

