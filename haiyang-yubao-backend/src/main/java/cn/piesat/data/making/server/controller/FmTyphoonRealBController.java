package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.dto.FmTyphoonRealBDTO;
import cn.piesat.data.making.server.vo.FmTyphoonRealBVO;
import cn.piesat.data.making.server.service.FmTyphoonRealBService;
import org.springframework.web.bind.annotation.*;
import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.common.page.constant.PageConstant;
import cn.piesat.common.page.annotation.JpaPage;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * 台风实时数据信息控制层
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:23
 */
@RestController
@RequestMapping("fmTyphoonRealB")
public class FmTyphoonRealBController {

    @Resource
    private FmTyphoonRealBService fmTyphoonRealBService;

    /**
     * 查询分页
     *
     * @param id
     * @param pageNum  页数
     * @param pageSize 条数
     * @return
     */
    @GetMapping("/page")
    @JpaPage(PageConstant.PAGE_VARIABLE_NAME)
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风实时数据信息管理", operateType = OperateType.SELECT)
    public PageResult<FmTyphoonRealBVO> getPage(@RequestParam(required = false) Long id,
                                                @RequestParam(defaultValue = "1") Integer pageNum,
                                                @RequestParam(defaultValue = "10") Integer pageSize) {
        FmTyphoonRealBDTO dto = new FmTyphoonRealBDTO();
        dto.setId(id);
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);
        return fmTyphoonRealBService.getPage(dto, pageParam);
    }

    /**
     * 查询列表
     *
     * @param id
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风实时数据信息管理", operateType = OperateType.SELECT)
    public List<FmTyphoonRealBVO> getList(@RequestParam(required = false) Long id) {
        FmTyphoonRealBDTO dto = new FmTyphoonRealBDTO();
        dto.setId(id);
        return fmTyphoonRealBService.getList(dto);
    }

    /**
     * 根据id查询数据
     *
     * @param id
     * @return
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风实时数据信息管理", operateType = OperateType.SELECT)
    public FmTyphoonRealBVO getById(@PathVariable Long id) {
        return fmTyphoonRealBService.getById(id);
    }

    /**
     * 保存数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风实时数据信息管理", operateType = OperateType.INSERT)
    public void save(@Validated(value = {FmTyphoonRealBDTO.Save.class}) @RequestBody FmTyphoonRealBDTO dto) {
        fmTyphoonRealBService.save(dto);
    }

    /**
     * 根据id删除数据
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风实时数据信息管理", operateType = OperateType.DELETE)
    public void deleteById(@PathVariable("id") Long id) {
        fmTyphoonRealBService.deleteById(id);
    }

    /**
     * 根据idList批量删除数据
     *
     * @param idList
     * @return
     */
    @DeleteMapping("/delete")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风实时数据信息管理", operateType = OperateType.DELETE)
    public void deleteByIdList(@RequestBody List<Long> idList) {
        fmTyphoonRealBService.deleteByIdList(idList);
    }
}
