package cn.piesat.data.making.server.controller;


import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.processor.WaveGenerateText;
import cn.piesat.data.making.server.service.FmPublishAlarmService;
import cn.piesat.data.making.server.service.StormSurgeAlarmService;
import cn.piesat.data.making.server.vo.GenerateTextVO;
import cn.piesat.data.making.server.dto.SeaWaveAlarmDTO;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.entity.SeaWaveAlarm;
import cn.piesat.data.making.server.service.SeaWaveAlarmService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 海浪警报制作
 *
 * <AUTHOR>
 * @since 2024-09-18 14:39:18
 */
@RestController
@RequestMapping("/seaWaveAlarm")
public class SeaWaveAlarmController {
    /**
     * 服务对象
     */
    @Autowired
    private SeaWaveAlarmService seaWaveAlarmService;

    @Autowired
    private FmPublishAlarmService fmPublishAlarmService;


    /**
     * 分页查询所有数据
     *
     * @param pageNum  页码
     * @param pageSize 条数
     * @return 所有数据
     */
    @GetMapping("/pageList")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪警报制作管理", operateType = OperateType.SELECT)
    public PageResult<SeaWaveAlarm> pageList(@RequestParam Integer pageNum, @RequestParam Integer pageSize,
                                             @RequestParam(value = "startTime", required = false) Date startTime,
                                             @RequestParam(value = "endTime", required = false) Date endTime) {
        return this.seaWaveAlarmService.pageList(pageNum, pageSize, startTime, endTime);
    }

    /**
     * 查询所有数据
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪警报制作管理", operateType = OperateType.SELECT)
    public List<SeaWaveAlarm> list(){
        return this.seaWaveAlarmService.list(new LambdaQueryWrapper<SeaWaveAlarm>().orderByAsc(SeaWaveAlarm::getCreateTime));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪警报制作管理", operateType = OperateType.SELECT)
    public SeaWaveAlarm selectOne(@PathVariable Long id) {
        return this.seaWaveAlarmService.getById(id);
    }

    /**
     * 新增数据
     *
     * @param seaWaveAlarmDTO 实体对象
     * @return 新增结果
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪警报制作管理", operateType = OperateType.INSERT)
    public SeaWaveAlarm insert(@RequestBody @Validated SeaWaveAlarmDTO seaWaveAlarmDTO) {
        return this.seaWaveAlarmService.saveInfo(seaWaveAlarmDTO);
    }

    /**
     * 修改数据
     *
     * @param seaWaveAlarmDTO 实体对象
     * @return 修改结果
     */
    @PostMapping("/update")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪警报制作管理", operateType = OperateType.UPDATE)
    public void update(@RequestBody @Validated SeaWaveAlarmDTO seaWaveAlarmDTO) {
        this.seaWaveAlarmService.updateInfo(seaWaveAlarmDTO);
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @PostMapping("/delete")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪警报制作管理", operateType = OperateType.DELETE)
    public void delete(@RequestBody List<Long> idList) {
        this.seaWaveAlarmService.removeByIds(idList);
    }

    /**
     * 生成文字
     * @param waveAlarmGenerateText 参数
     * @return
     */
    @PostMapping("/generateText")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪警报制作管理", operateType = OperateType.UPDATE)
    public GenerateTextVO generateText(@RequestBody WaveGenerateText waveAlarmGenerateText){
        return seaWaveAlarmService.generateText(waveAlarmGenerateText);
    }

    /**
     * 发布
     * @param id 参数
     */
    @PostMapping("/release/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪警报制作管理", operateType = OperateType.INSERT)
    public void release(@PathVariable Long id){
        seaWaveAlarmService.release(id);
    }

    /**
     * 统计
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    @PostMapping("/statistic")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪警报制作管理", operateType = OperateType.SELECT)
    public Map<Long, Long> statistic(@RequestParam(value = "startTime", required = false) Date startTime,
                                     @RequestParam(value = "endTime", required = false) Date endTime) {
        return this.seaWaveAlarmService.statistic(startTime, endTime);
    }


    /**
     * 通过主键查询单条数据,并推送发布到官网、公众号
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/pushSelectOne/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海浪警报制作管理", operateType = OperateType.SELECT)
    public SeaWaveAlarm pushSelectOne(@PathVariable Long id) {
        SeaWaveAlarm seaWaveAlarm = this.seaWaveAlarmService.getById(id);
        //转换数据到fm_publish_alarm表
        fmPublishAlarmService.publishAlarm(seaWaveAlarm);
        return seaWaveAlarm;
    }

    @GetMapping("/downloadDoc/{id}.docx")
    //@SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品记录管理", operateType = OperateType.SELECT)
    public void downloadDoc(HttpServletResponse response, @PathVariable("id") Long productRecordId){
        seaWaveAlarmService.downloadDoc(response,productRecordId);
    }
}

