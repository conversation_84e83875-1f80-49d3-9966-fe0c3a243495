package cn.piesat.data.making.server.common;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

public class JsonManipulation {

    private static final List<String> REQUIRED_FIELDS = Arrays.asList("name", "category", "color", "level", "transparency", "time", "content");

    public static String addGeoJsonProperties(String originalGeoJson, String category, Date releaseTime, String alarmContent) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode rootNode = mapper.readTree(originalGeoJson);

            // 添加 CRS 信息
            addCrsNode(rootNode, mapper);

            JsonNode features = rootNode.get("features");
            for (JsonNode feature : features) {
                ObjectNode propertiesNode = (ObjectNode) feature.get("properties");
                // 存储要删除的字段名
                List<String> fieldsToRemove = new ArrayList<>();
                Iterator<String> fieldNames = propertiesNode.fieldNames();
                while (fieldNames.hasNext()) {
                    String fieldName = fieldNames.next();
                    if (!REQUIRED_FIELDS.contains(fieldName)) {
                        fieldsToRemove.add(fieldName);
                    }
                }
                // 删除多余的属性
                for (String fieldName : fieldsToRemove) {
                    propertiesNode.remove(fieldName);
                }
                // 检查并添加属性
                addProperty(propertiesNode, "name", "无风险区域");
                addProperty(propertiesNode, "category", category);
                addProperty(propertiesNode, "color", "");
                addProperty(propertiesNode, "level", "无警报");
                addProperty(propertiesNode, "transparency", "1.0");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日HH时");
                addProperty(propertiesNode, "time", releaseTime == null ? "" : sdf.format(releaseTime));
                addProperty(propertiesNode, "content", alarmContent);
            }
            return mapper.writeValueAsString(rootNode);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void addCrsNode(JsonNode rootNode, ObjectMapper mapper) {
        ObjectNode crsNode = mapper.createObjectNode();
        ObjectNode crsPropertiesNode = mapper.createObjectNode();
        crsPropertiesNode.put("name", "urn:ogc:def:crs:OGC:1.3:CRS84");
        crsNode.put("type", "name");
        crsNode.set("properties", crsPropertiesNode);
        ((ObjectNode) rootNode).set("crs", crsNode);
    }

    public static void addProperty(ObjectNode propertiesNode, String propertyName, String propertyValue) {
        if (!propertiesNode.has(propertyName)) {
            propertiesNode.put(propertyName, propertyValue);
        }
    }
}