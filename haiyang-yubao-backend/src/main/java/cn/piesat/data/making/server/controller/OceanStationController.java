package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.OceanStationDataDTO;
import cn.piesat.data.making.server.model.StationInfo;
import cn.piesat.data.making.server.service.OceanStationService;
import cn.piesat.data.making.server.vo.FillMapVO;
import cn.piesat.data.making.server.vo.OceanStationDataVO;
import cn.piesat.data.making.server.vo.TideDailyHourDataVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * 海洋站表
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/oceanStation")
public class OceanStationController {

    @Resource
    private OceanStationService oceanStationService;

    /**
     * 查询中台海洋站列表 数据维护新增站点时使用
     *
     * @param name 海洋站名称
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报制作系统", moduleName = "海洋站管理", operateType = OperateType.SELECT)
    public List<StationInfo> getList(@RequestParam(required = false) String name) {
        return oceanStationService.getList(name);
    }

    /**
     * 根据时间范围查询观测结果 24小时观测数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    @GetMapping("/data/list")
    @SysLog(systemName = "预报制作系统", moduleName = "海洋站管理", operateType = OperateType.SELECT)
    public List<OceanStationDataVO> getDataList(@RequestParam(value = "startTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                @RequestParam(value = "endTime") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return oceanStationService.getDataList(startTime, endTime);
    }

    /**
     * 根据时间范围、站点查询观测结果-小时
     *
     * @param dto
     * @return
     */
    @PostMapping("/hourDataList")
    @SysLog(systemName = "预报制作系统", moduleName = "海洋站管理", operateType = OperateType.SELECT)
    public List<OceanStationDataVO> getHourDataList(@Validated(value = {OceanStationDataDTO.Query.class}) @RequestBody OceanStationDataDTO dto) {
        return oceanStationService.getHourDataList(dto.getStartTime(), dto.getEndTime(), dto.getOceanStationCodeList());
    }

    /**
     * 根据时间范围、站点查询观测结果-分钟
     *
     * @param dto
     * @return
     */
    @PostMapping("/minuteDataList")
    @SysLog(systemName = "预报制作系统", moduleName = "海洋站管理", operateType = OperateType.SELECT)
    public List<OceanStationDataVO> getMinuteDataList(@Validated(value = {OceanStationDataDTO.Query.class}) @RequestBody OceanStationDataDTO dto) {
        return oceanStationService.getMinuteDataList(dto.getStartTime(), dto.getEndTime(), dto.getOceanStationCodeList());
    }

    /**
     * 根据时间范围、空间范围查询观测结果
     *
     * @param dto
     * @return
     */
    @PostMapping("/geoRangeDataList")
    @SysLog(systemName = "预报制作系统", moduleName = "海洋站管理", operateType = OperateType.SELECT)
    public FillMapVO getGeoRangeDataList(@Validated(value = {OceanStationDataDTO.RangeQuery.class}) @RequestBody OceanStationDataDTO dto) {
        return oceanStationService.getGeoRangeDataList(dto);
    }

    /**
     * 同步数据-海洋站、浅海小型浮标
     *
     * @return
     */
    @GetMapping("/syncOceanAndSmallBuoyData")
    public void syncOceanAndSmallBuoyData(@RequestParam(required = false) String table, @RequestParam(required = false) String startTime,
                                          @RequestParam(required = false) String endTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startTimeL = LocalDateTime.parse(startTime, formatter);
        LocalDateTime endTimeL = LocalDateTime.parse(endTime, formatter);
        LocalDateTime lastDateTime = startTimeL;
        //判断结束时间是否大于开始时间
        while (endTimeL.isAfter(lastDateTime)) {
            //开始时间+1天
            lastDateTime = startTimeL.plusDays(1);
            //如果结束时间小于开始时间+1天，使用endTime
            if (lastDateTime.isAfter(endTimeL)) {
                oceanStationService.syncOceanAndSmallBuoyData(table, startTimeL, endTimeL);
            } else {
                oceanStationService.syncOceanAndSmallBuoyData(table, startTimeL, lastDateTime);
                startTimeL = lastDateTime;
            }
        }
    }

    /**
     * 定时同步数据-海洋站、浅海小型浮标
     *
     * @return
     */
    @Scheduled(cron = "${schedule.cron.syncOceanAndSmallBuoyData}")
    public void syncOceanAndSmallBuoyData() {
        new Thread(() -> {
            Long curTime = System.currentTimeMillis();
            log.info("海洋站、浅海小型浮标同步任务开始======");
            oceanStationService.syncOceanAndSmallBuoyData(null, null, null);
            log.info("海洋站、浅海小型浮标同步任务结束======");
            Long difTime = (System.currentTimeMillis()-curTime)/1000;
            log.info("=======================执行任务耗时:"+difTime+"============================");
        }).start();
    }

    /**
     * 定时同步数据-深海大型锚系浮标
     *
     * @return
     */
    @GetMapping("/syncLargeBuoyData")
    @Scheduled(cron = "${schedule.cron.syncLargeBuoyData}")
    public void syncLargeBuoyData() {
        log.info("深海大型锚系浮标同步任务开始======");
        oceanStationService.syncLargeBuoyData();
        log.info("深海大型锚系浮标同步任务结束======");
    }

    /**
     * 根据关联站点编码查询实况观测潮位数据列表
     *
     * @param stationNum 关联站点编码
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     **/
    @GetMapping("/tideList")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海洋站管理", operateType = OperateType.SELECT)
    public List<TideDailyHourDataVO> getTideList(@RequestParam(value = "stationNum") String stationNum,
                                                 @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                 @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return oceanStationService.getTideList(stationNum, startTime, endTime);
    }
}

