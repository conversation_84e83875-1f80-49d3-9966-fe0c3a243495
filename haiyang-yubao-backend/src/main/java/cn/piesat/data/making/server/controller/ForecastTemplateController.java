package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.ForecastTemplateDTO;
import cn.piesat.data.making.server.entity.ForecastTemplate;
import cn.piesat.data.making.server.model.ForecastTemplateInfo;
import cn.piesat.data.making.server.service.ForecastTemplateService;
import cn.piesat.data.making.server.vo.ForecastTemplateVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 预报模板表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/forecastTemplate")
public class ForecastTemplateController {

    @Resource
    private ForecastTemplateService forecastTemplateService;

    /**
     * 查询预报模板列表
     *
     * @param status 状态
     * @return
     */
    @GetMapping("/treeList")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报模板管理", operateType = OperateType.SELECT)
    public List<ForecastTemplateInfo> getTreeList(@RequestParam(required = false) Boolean status) {
        return forecastTemplateService.getTreeList(status);
    }

    /**
     * 根据预报模板id查询详情
     *
     * @param id
     * @return
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报模板管理", operateType = OperateType.SELECT)
    public ForecastTemplateVO getById(@PathVariable("id") Long id) {
        return forecastTemplateService.getInfoById(id);
    }

    /**
     * 保存预报模板
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报模板管理", operateType = OperateType.INSERT)
    public Long save(@Validated(value = {ForecastTemplateDTO.Save.class}) @RequestBody ForecastTemplateDTO dto) {
        return forecastTemplateService.save(dto);
    }

    /**
     * 根据预报模板id修改预报模板状态
     *
     * @param id
     * @param status 状态
     * @return
     */
    @PostMapping("/update/{id}/{status}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报模板管理", operateType = OperateType.UPDATE)
    public void update(@PathVariable Long id, @PathVariable Boolean status) {
        ForecastTemplate entity = new ForecastTemplate();
        entity.setId(id);
        entity.setStatus(status);
        forecastTemplateService.updateById(entity);
    }

    /**
     * 根据预报模板id删除预报模板
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报模板管理", operateType = OperateType.DELETE)
    public void deleteById(@PathVariable("id") Long id) {
        forecastTemplateService.deleteById(id);
    }

    /**
     * 根据预报模板id修改预报模板名称
     *
     * @param dto
     * @return
     */
    @PostMapping("/updateName")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报模板管理", operateType = OperateType.UPDATE)
    public void updateName(@RequestBody ForecastTemplateDTO dto) {
        ForecastTemplate entity = new ForecastTemplate();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        forecastTemplateService.updateById(entity);
    }
}

