package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.entity.LiveObserveElement;
import cn.piesat.data.making.server.service.LiveObserveElementService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 实况观测要素表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/liveObserveElement")
public class LiveObserveElementController {

    @Resource
    private LiveObserveElementService liveObserveElementService;

    /**
     * 查询实况观测要素表列表
     *
     * @param entity
     * @return
     */
    @PostMapping("/list")
    @SysLog(systemName = "预报制作系统", moduleName = "实况观测要素表管理", operateType = OperateType.SELECT)
    public List<LiveObserveElement> getList(@RequestBody LiveObserveElement entity) {
        return liveObserveElementService.getList(entity);
    }
}

