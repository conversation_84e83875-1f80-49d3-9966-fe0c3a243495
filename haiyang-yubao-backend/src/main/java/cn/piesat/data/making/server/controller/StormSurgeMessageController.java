package cn.piesat.data.making.server.controller;


import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.StormSurgeMessageDTO;
import cn.piesat.data.making.server.entity.StormSurgeMessage;
import cn.piesat.data.making.server.service.StormSurgeMessageService;
import cn.piesat.data.making.server.utils.page.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 风暴潮消息制作
 *
 * <AUTHOR>
 * @since 2024-10-22 15:27:35
 */
@RestController
@RequestMapping("/stormSurgeMessage")
public class StormSurgeMessageController {
    /**
     * 服务对象
     */
    @Autowired
    private StormSurgeMessageService stormSurgeMessageBService;

    /**
     * 分页查询所有数据
     *
     * @param pageNum  页码
     * @param pageSize 条数
     * @return 所有数据
     */
    @GetMapping("/pageList")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "风暴潮消息管理", operateType = OperateType.SELECT)
    public PageResult<StormSurgeMessage> pageList(@RequestParam Integer pageNum, @RequestParam Integer pageSize,
                                                  @RequestParam(value = "startTime", required = false) Date startTime,
                                                  @RequestParam(value = "endTime", required = false) Date endTime) {
        return this.stormSurgeMessageBService.pageList(pageNum, pageSize, startTime, endTime);
    }

    /**
     * 查询所有数据
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "风暴潮消息管理", operateType = OperateType.SELECT)
    public List<StormSurgeMessage> list(){
        return this.stormSurgeMessageBService.list(new LambdaQueryWrapper<StormSurgeMessage>().orderByAsc(StormSurgeMessage::getCreateTime));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "风暴潮消息管理", operateType = OperateType.SELECT)
    public StormSurgeMessage selectOne(@PathVariable Long id) {
        return this.stormSurgeMessageBService.getById(id);
    }

    /**
     * 新增数据
     *
     * @param stormSurgeMessageDTO 实体对象
     * @return 新增结果
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "风暴潮消息管理", operateType = OperateType.INSERT)
    public StormSurgeMessage insert(@RequestBody @Validated StormSurgeMessageDTO stormSurgeMessageDTO) {
        return this.stormSurgeMessageBService.saveInfo(stormSurgeMessageDTO);
    }

    /**
     * 修改数据
     *
     * @param stormSurgeMessageDTO 实体对象
     * @return 修改结果
     */
    @PostMapping("/update")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "风暴潮消息管理", operateType = OperateType.UPDATE)
    public void update(@RequestBody @Validated StormSurgeMessageDTO stormSurgeMessageDTO) {
        this.stormSurgeMessageBService.updateInfo(stormSurgeMessageDTO);
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @PostMapping("/delete")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "风暴潮消息管理", operateType = OperateType.DELETE)
    public void delete(@RequestBody List<Long> idList) {
        this.stormSurgeMessageBService.removeByIds(idList);
    }


    /**
     * 发布
     * @param stormSurgeMessageDTO 参数
     */
    @PostMapping("/release")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "风暴潮消息管理", operateType = OperateType.INSERT)
    public void release(@RequestBody @Validated StormSurgeMessageDTO stormSurgeMessageDTO){
        stormSurgeMessageBService.release(stormSurgeMessageDTO);
    }

}

