package cn.piesat.data.making.server.controller;


import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.AlarmProductTemplateDTO;
import cn.piesat.data.making.server.entity.AlarmProductTemplate;
import cn.piesat.data.making.server.service.AlarmProductTemplateService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.List;

/**
 * 警报产品模板
 *
 * <AUTHOR>
 * @since 2024-09-18 14:39:16
 */
@RestController
@RequestMapping("/alarmProductTemplate")
public class AlarmProductTemplateController {
    /**
     * 服务对象
     */
    @Autowired
    private AlarmProductTemplateService alarmProductTemplateService;

    /**
     * 查询所有数据
     *
     * @return 所有数据
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "警报产品模板管理", operateType = OperateType.SELECT)
    public List<AlarmProductTemplateDTO> list(@RequestParam(required = false) Integer status) {
        return this.alarmProductTemplateService.list(status);
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "警报产品模板管理", operateType = OperateType.SELECT)
    public AlarmProductTemplate selectOne(@PathVariable Long id) {
        return this.alarmProductTemplateService.getById(id);
    }

    /**
     * 新增数据
     *
     * @param alarmProductTemplate 实体对象
     * @return 新增结果
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "警报产品模板管理", operateType = OperateType.INSERT)
    public AlarmProductTemplate insert(@RequestBody AlarmProductTemplate alarmProductTemplate) {
        this.alarmProductTemplateService.save(alarmProductTemplate);
        return alarmProductTemplate;
    }

    /**
     * 修改数据
     *
     * @param alarmProductTemplate 实体对象
     * @return 修改结果
     */
    @PostMapping("/update")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "警报产品模板管理", operateType = OperateType.UPDATE)
    public void update(@RequestBody AlarmProductTemplate alarmProductTemplate) {
        this.alarmProductTemplateService.updateById(alarmProductTemplate);
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @PostMapping("/delete")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "警报产品模板管理", operateType = OperateType.DELETE)
    public void delete(@RequestBody List<Long> idList) {
        this.alarmProductTemplateService.removeByIds(idList);
    }


    /**
     * 设置开启
     * @param id 主键id
     */
    @PostMapping("/setOpen/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "警报产品模板管理", operateType = OperateType.UPDATE)
    public void setOpen(@PathVariable Long id) {
        alarmProductTemplateService.setOpen(id);
    }
}

