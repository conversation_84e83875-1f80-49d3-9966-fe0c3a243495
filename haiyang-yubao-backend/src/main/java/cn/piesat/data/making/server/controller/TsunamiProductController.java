package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dto.TsunamiProductDTO;
import cn.piesat.data.making.server.service.TsunamiProductService;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.TsunamiProductVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 海啸产品表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/tsunamiProduct")
public class TsunamiProductController {

    @Resource
    private TsunamiProductService tsunamiProductService;

    /**
     * 查询海啸产品表分页
     *
     * @param name      名称
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param pageNum   页数
     * @param pageSize  条数
     * @return
     */
    @GetMapping("/page")
    @SysLog(systemName = "预报制作系统", moduleName = "海啸产品表管理", operateType = OperateType.SELECT)
    public PageResult<TsunamiProductVO> getPage(@RequestParam(required = false) String name, @RequestParam(required = false) Date startTime,
                                                @RequestParam(required = false) Date endTime, @RequestParam(defaultValue = "1") Integer pageNum,
                                                @RequestParam(defaultValue = "10") Integer pageSize) {
        TsunamiProductDTO dto = new TsunamiProductDTO();
        dto.setName(name);
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);
        return tsunamiProductService.getPage(dto, pageParam);
    }

    /**
     * 查询海啸产品表列表
     *
     * @param name      名称
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报制作系统", moduleName = "海啸产品表管理", operateType = OperateType.SELECT)
    public List<TsunamiProductVO> getList(@RequestParam(required = false) String name, @RequestParam(required = false) Date startTime,
                                          @RequestParam(required = false) Date endTime) {
        TsunamiProductDTO dto = new TsunamiProductDTO();
        dto.setName(name);
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        return tsunamiProductService.getList(dto);
    }

    /**
     * 下载
     *
     * @param id
     * @return
     **/
    @GetMapping("/download/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海啸产品表管理", operateType = OperateType.SELECT)
    public void downloadFile(@PathVariable("id") Long id, HttpServletResponse response) {
        tsunamiProductService.downloadFile(id, response);
    }

    /**
     * 批量下载
     *
     * @param ids       ids
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     **/
    @GetMapping("/batch/download")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "海啸产品表管理", operateType = OperateType.SELECT)
    public void batchDownload(@RequestParam(required = false) String ids, @RequestParam(required = false) Date startTime,
                              @RequestParam(required = false) Date endTime, HttpServletResponse response) {
        TsunamiProductDTO dto = new TsunamiProductDTO();
        dto.setIds(ids);
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        tsunamiProductService.batchDownload(dto, response);
    }
}

