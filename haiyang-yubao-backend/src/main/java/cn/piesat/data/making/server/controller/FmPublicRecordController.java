package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.entity.FmPublicProductRecord;
import cn.piesat.data.making.server.service.FmPublicProductRecordService;
import cn.piesat.data.making.server.service.FmPublicRecordService;
import cn.piesat.data.making.server.service.FmPushResultService;
import cn.piesat.data.making.server.vo.FmPublicProductRecordVO;
import cn.piesat.data.making.server.vo.FmPublicRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Calendar;

@RestController
@RequestMapping("fmPublicRecord")
public class FmPublicRecordController {

    @Autowired
    private FmPublicRecordService fmPublicRecordServiceImpl;

    @Autowired
    private FmPublicProductRecordService fmPublicProductRecordServiceImpl;

    @Autowired
    private FmPushResultService fmPushResultServiceImpl;

    @GetMapping("/getLastRecord")
    public FmPublicRecordVO getLastRecord(String publicType){
        return fmPublicRecordServiceImpl.getLastRecord(publicType);
    }

    @PostMapping("/saveOrUpdate")
    public FmPublicRecordVO saveOrUpdate(@RequestBody FmPublicRecordVO fmPublicRecordVO){
        if(fmPublicRecordVO!=null&&fmPublicRecordVO.getId()!=null&&fmPublicRecordServiceImpl.getByPk(fmPublicRecordVO.getId())!=null){
            return fmPublicRecordServiceImpl.update(fmPublicRecordVO);
        } else {
            return fmPublicRecordServiceImpl.save(fmPublicRecordVO);
        }
    }

    @PutMapping("/submit")
    public FmPublicRecordVO submit(@RequestBody FmPublicRecordVO fmPublicRecordVO){
        fmPublicRecordVO.setReportTime(Calendar.getInstance().getTime());
        FmPublicRecordVO result = this.saveOrUpdate(fmPublicRecordVO);

        //在这里增加发送天文大潮邮件
        //add by wangxin

        FmPublicProductRecordVO fmPublicProductRecordVO = fmPublicProductRecordServiceImpl.findByRecordId(result.getId());

        String wordFileName = fmPublicProductRecordVO.getFileUrl().substring(fmPublicProductRecordVO.getFileUrl().lastIndexOf("/")+1);
        fmPushResultServiceImpl.addFmPushResList("",result.getTemplateCode(),result.getContentF(),fmPublicProductRecordVO.getFileUrl(),wordFileName,"/fmPublicRecord/downloadDoc/"+result.getId()+".docx");

        return result;
    }

    @GetMapping("/download/{id}")
    public void download(@PathVariable("id") Long id, HttpServletResponse response){
        FmPublicProductRecordVO fmPublicProductRecordVO = fmPublicProductRecordServiceImpl.findByRecordId(id);
        fmPublicProductRecordServiceImpl.download(response,fmPublicProductRecordVO.getId());
    }

    @GetMapping("/downloadDoc/{id}.docx")
    public void downloadDoc(@PathVariable("id") Long id, HttpServletResponse response){
        FmPublicProductRecordVO fmPublicProductRecordVO = fmPublicProductRecordServiceImpl.findByRecordId(id);
        fmPublicProductRecordServiceImpl.download(response,fmPublicProductRecordVO.getId());
    }
}
