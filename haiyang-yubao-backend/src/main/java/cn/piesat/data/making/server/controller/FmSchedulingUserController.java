package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.entity.FmSchedulingUser;
import cn.piesat.data.making.server.service.FmSchedulingUserService;
import cn.piesat.security.ucenter.starter.utils.UserUtils;
import org.springframework.web.bind.annotation.*;
import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.common.page.constant.PageConstant;
import cn.piesat.common.page.annotation.JpaPage;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * 控制层
 *
 * <AUTHOR>
 * @date 2024-10-30 16:08:29
 */
@RestController
@RequestMapping("fmSchedulingUser")
public class FmSchedulingUserController {

    @Resource
    private FmSchedulingUserService fmSchedulingUserService;

    /**
     * 查询分页
     *
     * @param id
     * @param pageNum  页数
     * @param pageSize 条数
     * @return
     */
    @GetMapping("/page")
    @JpaPage(PageConstant.PAGE_VARIABLE_NAME)
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.SELECT)
    public PageResult<FmSchedulingUser> getPage(@RequestParam(required = false) Long id,
                                                  @RequestParam(defaultValue = "1") Integer pageNum,
                                                  @RequestParam(defaultValue = "10") Integer pageSize) {
        FmSchedulingUser dto = new FmSchedulingUser();
        dto.setId(id);
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);
        return fmSchedulingUserService.getPage(dto, pageParam);
    }

    /**
     * 查询列表
     *
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.SELECT)
    public List<FmSchedulingUser> getList() {
        FmSchedulingUser dto = new FmSchedulingUser();
        return fmSchedulingUserService.getList(dto);
    }


    /**
     * 保存数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "管理", operateType = OperateType.INSERT)
    public void save( @RequestBody FmSchedulingUser dto) {
        Long userId = UserUtils.getUserId();
        String realName = UserUtils.getUserInfo().getRealName();
        dto.setUserId(userId);
        dto.setUserName(realName);
        fmSchedulingUserService.save(dto);
    }

}
