package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.ShipData;
import cn.piesat.data.making.server.vo.ShipDataVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * 船舶表表数据库访问层
 *
 * <AUTHOR>
 */
public interface ShipDataDao extends BaseMapper<ShipData> {

    @Select("SELECT DISTINCT sign_ship FROM fm_ship_data_b order by sign_ship")
    List<String> getDistinctList();

    @Select({"<script>",
            "SELECT id,sign_ship signShip,latitude,longitude,visibility,air_temperature airTemperature,dew_point_temp dewPointTemp," +
                    "wind_direction windDirection,wind_speed windSpeed,air_pressure airPressure,total_cloud_amount totalCloudAmount," +
                    "low_cloud_amount lowCloudAmount,sea_temp seaTemp,wave_period wavePeriod,wave_height waveHeight,surge_direction surgeDirection," +
                    "surge_period surgePeriod,surge_height surgeHeight,location_geo locationGeo,location_json locationJson,time FROM fm_ship_data_b " +
                    "WHERE 1=1 " +
                    "<if test='startTime != null'>" +
                    "and time &gt;= #{startTime} " +
                    "</if>" +
                    "<if test='endTime != null'>" +
                    "and time &lt;= #{endTime} " +
                    "</if>" +
                    "<if test='geoRange != null'>" +
                    "and ST_Contains(ST_GeomFromText(#{geoRange},4326), location_geo) " +
                    "</if>" +
                    "<if test='signShip != null'>" +
                    "and sign_ship = #{signShip}  " +
                    "</if> order by sign_ship",
            "</script>"})
    List<ShipDataVO> getDataList(Date startTime, Date endTime, String geoRange, String signShip);

}
