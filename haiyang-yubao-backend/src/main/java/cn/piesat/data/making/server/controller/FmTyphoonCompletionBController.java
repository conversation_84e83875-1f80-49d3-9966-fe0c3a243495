package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.dto.FmTyphoonCompletionBDTO;
import cn.piesat.data.making.server.vo.FmTyphoonCompletionBVO;
import cn.piesat.data.making.server.service.FmTyphoonCompletionBService;
import org.springframework.web.bind.annotation.*;
import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.common.page.constant.PageConstant;
import cn.piesat.common.page.annotation.JpaPage;
import cn.piesat.common.utils.PageResult;
import cn.piesat.data.making.server.utils.page.PageParam;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * 台风位置预警完成数据控制层
 *
 * <AUTHOR>
 * @date 2024-09-23 14:43:22
 */
@RestController
@RequestMapping("fmTyphoonCompletionB")
public class FmTyphoonCompletionBController {

    @Resource
    private FmTyphoonCompletionBService fmTyphoonCompletionBService;

    /**
     * 查询分页
     *
     * @param id
     * @param pageNum  页数
     * @param pageSize 条数
     * @return
     */
    @GetMapping("/page")
    @JpaPage(PageConstant.PAGE_VARIABLE_NAME)
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风位置预警完成数据管理", operateType = OperateType.SELECT)
    public PageResult<FmTyphoonCompletionBVO> getPage(@RequestParam(required = false) Long id,
                                                      @RequestParam(defaultValue = "1") Integer pageNum,
                                                      @RequestParam(defaultValue = "10") Integer pageSize) {
        FmTyphoonCompletionBDTO dto = new FmTyphoonCompletionBDTO();
        dto.setId(id);
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);
        return fmTyphoonCompletionBService.getPage(dto, pageParam);
    }

    /**
     * 查询列表
     *
     * @param id
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风位置预警完成数据管理", operateType = OperateType.SELECT)
    public List<FmTyphoonCompletionBVO> getList(@RequestParam(required = false) Long id) {
        FmTyphoonCompletionBDTO dto = new FmTyphoonCompletionBDTO();
        dto.setId(id);
        return fmTyphoonCompletionBService.getList(dto);
    }

    /**
     * 根据id查询数据
     *
     * @param id
     * @return
     */
    @GetMapping("/info/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风位置预警完成数据管理", operateType = OperateType.SELECT)
    public FmTyphoonCompletionBVO getById(@PathVariable Long id) {
        return fmTyphoonCompletionBService.getById(id);
    }

    /**
     * 保存数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风位置预警完成数据管理", operateType = OperateType.INSERT)
    public void save(@Validated(value = {FmTyphoonCompletionBDTO.Save.class}) @RequestBody FmTyphoonCompletionBDTO dto) {
        fmTyphoonCompletionBService.save(dto);
    }

    /**
     * 根据id删除数据
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风位置预警完成数据管理", operateType = OperateType.DELETE)
    public void deleteById(@PathVariable("id") Long id) {
        fmTyphoonCompletionBService.deleteById(id);
    }

    /**
     * 根据idList批量删除数据
     *
     * @param idList
     * @return
     */
    @DeleteMapping("/delete")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "台风位置预警完成数据管理", operateType = OperateType.DELETE)
    public void deleteByIdList(@RequestBody List<Long> idList) {
        fmTyphoonCompletionBService.deleteByIdList(idList);
    }
}
