package cn.piesat.data.making.server.config;

import cn.piesat.data.making.server.utils.GeoToolsUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.postgis.Geometry;
import org.postgis.PGgeometry;

import java.io.IOException;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedTypes({String.class})
public class PgGeometryTypeHandler extends BaseTypeHandler<String> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        String param;
        try {
            param = GeoToolsUtil.geojsonToGeo(parameter);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        //POINT (110.4 20.2)
        PGgeometry pGgeometry = new PGgeometry(param);
        Geometry geometry = pGgeometry.getGeometry();
        geometry.setSrid(4326);
        ps.setObject(i, pGgeometry);
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String string = rs.getString(columnName);
        return getResult(string);
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String string = rs.getString(columnIndex);
        return getResult(string);
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String string = cs.getString(columnIndex);
        return getResult(string);
    }

    private String getResult(String string) throws SQLException {
        PGgeometry pGgeometry = new PGgeometry(string);
        String s = pGgeometry.toString();
        return s.replace("SRID=4326;", "");
    }
}