package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.dao.OceanStationHourAtODao;
import cn.piesat.data.making.server.entity.OceanStation;
import cn.piesat.data.making.server.service.FmForecastProductDataService;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/26 13:18
 */
public class OceanStationObserveDataController {

    @Resource
    private OceanStationHourAtODao oceanStationHourAtODao;

    /**
     * 查询海洋站表分页
     *
     * @param pageNum  页数
     * @param pageSize 条数
     * @return
     */
//    @GetMapping("/page")
//    @SysLog(systemName = "预报制作系统", moduleName = "海洋站管理", operateType = OperateType.SELECT)
//    public PageResult<OceanStation> getPage(@RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "10") Integer pageSize) {
//
//        PageParam pageParam = new PageParam();
//        pageParam.setPageNum(pageNum);
//        pageParam.setPageSize(pageSize);
//        return forecastRecordDetailService.saveBatch(detailList);
//    }
}
