package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.service.FmPublicTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("fmPublicTask")
public class FmPublicTaskController {

    @Autowired
    private FmPublicTaskService fmPublicTaskServiceImpl;

    @GetMapping("/schedule")
    @Scheduled(cron = "${schedule.cron.createForecastTask}")
    public void createPublicTask(){
        fmPublicTaskServiceImpl.createPublicTask();
    }

    @GetMapping("/getLastTaskId")
    public Long getLastTaskId(String publicType){
        return fmPublicTaskServiceImpl.getLastTaskId(publicType);
    }
}
