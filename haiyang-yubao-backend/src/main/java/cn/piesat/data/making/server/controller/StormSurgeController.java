package cn.piesat.data.making.server.controller;

import cn.piesat.data.making.server.service.StormSurgeService;
import cn.piesat.data.making.server.vo.TideDailyHourDataVO;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 预报分析-风暴潮
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/stormSurge")
public class StormSurgeController {

    @Resource
    private StormSurgeService stormSurgeService;

    /**
     * 根据站点编码查询风暴潮数据列表
     *
     * @param stationNum 站点编码
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return
     **/
    @GetMapping("/tideList")
    public List<TideDailyHourDataVO> getTideList(@RequestParam(value = "stationNum") String stationNum,
                                                 @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime, @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return stormSurgeService.getTideList(stationNum, startTime, endTime);
    }
}
