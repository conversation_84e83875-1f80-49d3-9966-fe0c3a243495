package cn.piesat.data.making.server.config;

import cn.piesat.data.making.server.config.properties.RedissonProperties;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.redisson.spring.cache.RedissonSpringCacheManager;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;

/**
 * TODO redis连接配置
 * <AUTHOR> hongwei.peng
 * @version 1.0
 * @date 2021/10/26 16:14
 */
@Configuration
@EnableConfigurationProperties(RedissonProperties.class)
public class RedissonConfiguration {

  private RedissonProperties redissonProperties;

  public RedissonConfiguration(RedissonProperties properties) {
    this.redissonProperties = properties;
  }

  @Bean(destroyMethod = "shutdown")
  @ConditionalOnProperty(prefix = "spring.redisson", name = "mode", havingValue = "single")
  RedissonClient redisson() {
    Config config = new Config();
    Method method =
        ReflectionUtils.findMethod(Config.class, "useSingleServer", new Class[] { SingleServerConfig.class });
    ReflectionUtils.makeAccessible(method);
    ReflectionUtils.invokeMethod(method, config, redissonProperties.getSingleServerConfig());
    RedissonClient client = Redisson.create(config);
    return client;
  }

  @Bean(destroyMethod = "shutdown")
  @ConditionalOnProperty(prefix = "spring.redisson", name = "mode", havingValue = "cluster")
  RedissonClient redissonCluster() {
    Config config = new Config();
    Method method = ReflectionUtils.findMethod(Config.class,"useClusterServers", new Class[]{ClusterServersConfig.class});
    ReflectionUtils.makeAccessible(method);
    ReflectionUtils.invokeMethod(method,config,redissonProperties.getClusterServersConfig());
    return Redisson.create(config);
  }

  @Bean
  @Primary
  CacheManager redissonCacheManager(RedissonClient client) {
    RedissonSpringCacheManager cacheManager = new RedissonSpringCacheManager(client);
    cacheManager.setAllowNullValues(false);
    return cacheManager;
  }

}
